import * as React from 'react';

interface StatusChangeEmailProps {
  scanName: string;
  assetType: string;
  oldStatus: string;
  newStatus: string;
  scanId: string;
}

// This is a React component that can be rendered to HTML using react-email
// For now, we're using a simpler approach with inline HTML in the API
// This file is included for future enhancement if you want to use react-email

export const StatusChangeEmail = ({
  scanName,
  assetType,
  oldStatus,
  newStatus,
  scanId,
}: StatusChangeEmailProps) => {
  // Format status for display
  const formatStatus = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'in-progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 're-test': return 'Re-test';
      default: return status;
    }
  };

  // Format asset type for display
  const formatAssetType = (type: string) => {
    if (!type) return 'Unknown';
    return type
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return { bg: '#fff8e1', text: '#f57c00' };
      case 'in-progress': return { bg: '#e3f2fd', text: '#1976d2' };
      case 're-test': return { bg: '#f3e5f5', text: '#7b1fa2' };
      case 'completed': return { bg: '#e8f5e9', text: '#388e3c' };
      default: return { bg: '#f5f5f5', text: '#333333' };
    }
  };

  const oldStatusColor = getStatusColor(oldStatus);
  const newStatusColor = getStatusColor(newStatus);

  return (
    <div style={container}>
      <div style={header}>
        <h1 style={heading}>Scan Status Update</h1>
      </div>
      <div style={content}>
        <p style={paragraph}>The status of your scan has been updated.</p>

        <div style={details}>
          <p style={detailRow}>
            <strong>Scan Type:</strong> {formatAssetType(assetType)} Scan
          </p>
          <p style={detailRow}>
            <strong>Target:</strong> {scanName}
          </p>
          <p style={detailRow}>
            <strong>Previous Status:</strong>
            <span style={{
              ...statusBadge,
              backgroundColor: oldStatusColor.bg,
              color: oldStatusColor.text
            }}>
              {formatStatus(oldStatus)}
            </span>
          </p>
          <p style={detailRow}>
            <strong>New Status:</strong>
            <span style={{
              ...statusBadge,
              backgroundColor: newStatusColor.bg,
              color: newStatusColor.text
            }}>
              {formatStatus(newStatus)}
            </span>
          </p>
          <p style={detailRow}>
            <strong>Updated At:</strong> {new Date().toLocaleString()}
          </p>
        </div>

        <p style={paragraph}>
          You can view the complete details of this scan by clicking the button below:
        </p>

        <a href={`https://app.getdeepscan.com/scans/${scanId}`} style={button}>
          View Pentest Details
        </a>
      </div>
      <div style={footer}>
        <p style={footerText}>
          This is an automated message from DeepScan. Please do not reply to this email.
        </p>
        <p style={footerText}>
          &copy; {new Date().getFullYear()} DeepScan. All rights reserved.
        </p>
      </div>
    </div>
  );
};

// Styles
const container = {
  fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
  lineHeight: 1.6,
  color: '#333',
  backgroundColor: '#f9f9f9',
  margin: 0,
  padding: 0,
  maxWidth: '600px',
};

const header = {
  textAlign: 'center' as const,
  paddingBottom: '20px',
  borderBottom: '1px solid #eee',
};

const heading = {
  color: '#333',
  fontSize: '24px',
  marginBottom: '20px',
};

const content = {
  padding: '20px 0',
};

const paragraph = {
  margin: '16px 0',
};

const details = {
  backgroundColor: '#f5f5f5',
  padding: '15px',
  borderRadius: '4px',
  margin: '20px 0',
};

const detailRow = {
  margin: '8px 0',
};

const statusBadge = {
  display: 'inline-block',
  padding: '6px 12px',
  borderRadius: '50px',
  fontSize: '14px',
  fontWeight: 500,
  marginLeft: '5px',
};

const button = {
  display: 'inline-block',
  backgroundColor: '#1976d2',
  color: 'white',
  textDecoration: 'none',
  padding: '10px 20px',
  borderRadius: '4px',
  marginTop: '20px',
  fontWeight: 500,
};

const footer = {
  textAlign: 'center' as const,
  paddingTop: '20px',
  borderTop: '1px solid #eee',
  fontSize: '12px',
  color: '#777',
};

const footerText = {
  margin: '8px 0',
};

export default StatusChangeEmail;
