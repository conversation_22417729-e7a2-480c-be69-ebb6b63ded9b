import * as React from 'react';

interface ProgressUpdateEmailProps {
  scanName: string;
  assetType: string;
  progress: number;
  scanId: string;
}

export const ProgressUpdateEmail = ({
  scanName,
  assetType,
  progress,
  scanId,
}: ProgressUpdateEmailProps) => {
  const formatAssetType = (type: string) => {
    if (!type) return 'Unknown';
    return type
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div style={container}>
      <div style={header}>
        <h1 style={heading}>Pen Test Progress Update: {progress}% Complete</h1>
      </div>
      <div style={content}>
        <p style={paragraph}>The pen test for <strong>{formatAssetType(assetType)}</strong> on <strong>{scanName}</strong> has reached a new milestone!</p>
        <div style={details}>
          <p style={detailRow}><strong>Current Progress:</strong> {progress}%</p>
          <p style={detailRow}><strong>Updated At:</strong> {new Date().toLocaleString()}</p>
        </div>
        <p style={paragraph}>You can view the complete details of this scan by clicking the button below:</p>
        <a href={`https://app.getdeepscan.com/scans/${scanId}`} style={button}>
          View Pentest Details
        </a>
      </div>
      <div style={footer}>
        <p style={footerText}>
          This is an automated message from DeepScan. Please do not reply to this email.
        </p>
        <p style={footerText}>
          &copy; {new Date().getFullYear()} DeepScan. All rights reserved.
        </p>
      </div>
    </div>
  );
};

// Styles
const container = {
  fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
  lineHeight: 1.6,
  color: '#333',
  backgroundColor: '#f9f9f9',
  margin: 0,
  padding: 0,
  maxWidth: '600px',
};

const header = {
  textAlign: 'center' as const,
  paddingBottom: '20px',
  borderBottom: '1px solid #eee',
};

const heading = {
  color: '#333',
  fontSize: '24px',
  marginBottom: '20px',
};

const content = {
  padding: '20px 0',
};

const paragraph = {
  margin: '16px 0',
};

const details = {
  backgroundColor: '#f5f5f5',
  padding: '15px',
  borderRadius: '4px',
  margin: '20px 0',
};

const detailRow = {
  margin: '8px 0',
};

const button = {
  display: 'inline-block',
  backgroundColor: '#1976d2',
  color: 'white',
  textDecoration: 'none',
  padding: '10px 20px',
  borderRadius: '4px',
  marginTop: '20px',
  fontWeight: 500,
};

const footer = {
  textAlign: 'center' as const,
  paddingTop: '20px',
  borderTop: '1px solid #eee',
  fontSize: '12px',
  color: '#777',
};

const footerText = {
  margin: '8px 0',
};

export default ProgressUpdateEmail;