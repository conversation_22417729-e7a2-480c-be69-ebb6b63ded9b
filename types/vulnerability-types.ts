export type VulnerabilitySeverity = 'critical' | 'high' | 'medium' | 'low';

export interface Vulnerability {
  id: string;
  name: string;
  severity: VulnerabilitySeverity;
  description: string;
  affectedComponent: string;
  scanId: string;
  scanName?: string;
  target?: string;
  createdAt: string;
  updatedAt: string;
  detailedExplanation?: string;
  remediationSteps?: string[];
  impact?: string;
  cveId?: string;
  status: 'Open' | 'Closed' | 'Pending Retest';
  cvss?: number;
  notes?: VulnerabilityNote[]; // Add notes field
}
 
export interface VulnerabilityNote {
  id: string;
  text: string;
  author: string;
  authorId: string;
  createdAt: string;
}

export interface VulnerabilityFilter {
  severity?: VulnerabilitySeverity[];
  status?: string[];
  search?: string;
  scanId?: string;
  scanName?: string | null;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface VulnerabilitySort {
  field: keyof Vulnerability;
  direction: 'asc' | 'desc';
}

export interface VulnerabilityDetailGenerationRequest {
  name: string;
  severity: VulnerabilitySeverity;
  description: string;
  affectedComponent: string;
  target?: string;
}

export interface VulnerabilityDetailGenerationResponse {
  detailedExplanation: string;
  remediationSteps: string[];
  impact: string;
  cveId?: string;
}
