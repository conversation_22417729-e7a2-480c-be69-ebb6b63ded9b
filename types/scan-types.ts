export type ScanType = {
    id: string;
    name: string;
    status: "pending" | "in-progress" | "completed" | "re-test";
    target: string;
    requestedAt: Date;
    completedAt?: Date;
    criticalVulnerabilities: number;
    highVulnerabilities: number;
    mediumVulnerabilities: number;
    lowVulnerabilities: number;
    resultFileUrl?: string;
    userId: string;
    userEmail?: string;
    aiSummary?: any;
    [key: string]: any;
};

export type UserInfo = {
    userId: string;
    email: string;
    displayName?: string;
    organization?: string;
    scans: ScanType[];
};
