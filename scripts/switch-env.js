/**
 * Environment Switching Script
 * 
 * This script helps switch between development and production environments
 * by copying the appropriate .env file to .env.local
 * 
 * Usage:
 * node scripts/switch-env.js [development|production]
 */

const fs = require('fs');
const path = require('path');

// Get the target environment from command line arguments
const targetEnv = process.argv[2]?.toLowerCase();

// Validate the target environment
if (!targetEnv || (targetEnv !== 'development' && targetEnv !== 'production')) {
  console.error('Error: Please specify a valid environment (development or production)');
  console.log('Usage: node scripts/switch-env.js [development|production]');
  process.exit(1);
}

// Define file paths
const rootDir = path.resolve(__dirname, '..');
const sourceEnvFile = path.join(rootDir, `.env.${targetEnv}`);
const targetEnvFile = path.join(rootDir, '.env.local');

// Check if the source environment file exists
if (!fs.existsSync(sourceEnvFile)) {
  console.error(`Error: Environment file ${sourceEnvFile} does not exist.`);
  console.log(`Please create a .env.${targetEnv} file first.`);
  process.exit(1);
}

try {
  // Copy the environment file
  fs.copyFileSync(sourceEnvFile, targetEnvFile);
  console.log(`✅ Successfully switched to ${targetEnv} environment`);
  console.log(`Copied ${sourceEnvFile} to ${targetEnvFile}`);
  
  // Additional instructions
  if (targetEnv === 'development') {
    console.log('\n📋 Development Environment Notes:');
    console.log('1. Make sure you have created a development Firebase project');
    console.log('2. Update the Firebase credentials in .env.development if needed');
    console.log('3. Restart your development server with: npm run dev');
  } else {
    console.log('\n📋 Production Environment Notes:');
    console.log('1. The application is now configured to use production Firebase project');
    console.log('2. Be careful when making changes to the production database');
    console.log('3. Restart your server to apply the changes');
  }
} catch (error) {
  console.error(`Error switching environments: ${error.message}`);
  process.exit(1);
}
