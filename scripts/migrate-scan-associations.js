/**
 * Migration script to update scan associations
 * 
 * This script:
 * 1. Updates all scans with consistent user IDs and emails
 * 2. Ensures all team members have proper IDs in the team document
 * 3. Creates direct associations between scans and team members
 * 
 * Run with: node scripts/migrate-scan-associations.js
 */

const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Initialize Firebase Admin SDK
try {
  const serviceAccountPath = path.resolve(__dirname, '../service-account.json');
  if (fs.existsSync(serviceAccountPath)) {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
  } else {
    console.error('Service account file not found. Please create service-account.json in the root directory.');
    process.exit(1);
  }
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Utility function to generate stable ID from email
function generateStableIdFromEmail(email) {
  if (!email) return '';
  // Remove all non-alphanumeric characters and convert to lowercase
  const sanitizedEmail = email.toLowerCase().replace(/[^a-z0-9]/g, '');
  return `email-${sanitizedEmail}`;
}

// Utility function to check if an ID is a stable email-based ID
function isStableEmailId(id) {
  return id?.startsWith('email-');
}

// Main migration function
async function migrateScans() {
  const db = admin.firestore();
  const auth = admin.auth();
  
  console.log('Starting scan association migration...');
  
  // Step 1: Get all teams
  console.log('Fetching all teams...');
  const teamsSnapshot = await db.collection('teams').get();
  
  // Process each team
  for (const teamDoc of teamsSnapshot.docs) {
    const teamData = teamDoc.data();
    console.log(`Processing team: ${teamDoc.id}`);
    
    // Get team members and emails
    const teamMembers = teamData.teamMembers || [];
    const teamMemberEmails = teamData.teamMemberEmails || [];
    
    console.log(`Team has ${teamMembers.length} members and ${teamMemberEmails.length} emails`);
    
    // Create a map of emails to user IDs
    const emailToIdMap = new Map();
    const updatedTeamMembers = [];
    
    // Step 2: Process team member emails to get or create stable IDs
    for (const email of teamMemberEmails) {
      try {
        // Try to find user by email in Firebase Auth
        const userRecord = await auth.getUserByEmail(email);
        emailToIdMap.set(email, userRecord.uid);
        updatedTeamMembers.push(userRecord.uid);
        console.log(`Mapped email ${email} to Firebase user ID: ${userRecord.uid}`);
      } catch (error) {
        // User not found in Firebase Auth, create a stable ID
        const stableId = generateStableIdFromEmail(email);
        emailToIdMap.set(email, stableId);
        updatedTeamMembers.push(stableId);
        console.log(`Mapped email ${email} to stable ID: ${stableId}`);
      }
    }
    
    // Step 3: Update the team document with consistent member IDs
    if (updatedTeamMembers.length > 0 && JSON.stringify(updatedTeamMembers) !== JSON.stringify(teamMembers)) {
      console.log(`Updating team ${teamDoc.id} with consistent member IDs`);
      await teamDoc.ref.update({
        teamMembers: updatedTeamMembers
      });
    }
    
    // Step 4: Get all scans for this team's members
    console.log(`Fetching scans for team ${teamDoc.id}...`);
    
    // First, get scans by user ID
    const scansByUserIdPromises = updatedTeamMembers.map(userId => 
      db.collection('scans').where('userId', '==', userId).get()
    );
    
    // Then, get scans by user email
    const scansByUserEmailPromises = teamMemberEmails.map(email => 
      db.collection('scans').where('userEmail', '==', email).get()
    );
    
    // Wait for all queries to complete
    const scanSnapshots = await Promise.all([
      ...scansByUserIdPromises,
      ...scansByUserEmailPromises
    ]);
    
    // Process all scans
    const processedScanIds = new Set();
    for (const snapshot of scanSnapshots) {
      for (const scanDoc of snapshot.docs) {
        // Skip if we've already processed this scan
        if (processedScanIds.has(scanDoc.id)) continue;
        processedScanIds.add(scanDoc.id);
        
        const scanData = scanDoc.data();
        let needsUpdate = false;
        const updates = {};
        
        // Check if scan has userEmail but no userId
        if (scanData.userEmail && !scanData.userId) {
          const userId = emailToIdMap.get(scanData.userEmail);
          if (userId) {
            updates.userId = userId;
            needsUpdate = true;
            console.log(`Adding userId ${userId} to scan ${scanDoc.id}`);
          }
        }
        
        // Check if scan has userId but no userEmail
        if (scanData.userId && !scanData.userEmail) {
          try {
            // Try to get user email from Firebase Auth
            const userRecord = await auth.getUser(scanData.userId);
            if (userRecord.email) {
              updates.userEmail = userRecord.email;
              needsUpdate = true;
              console.log(`Adding userEmail ${userRecord.email} to scan ${scanDoc.id}`);
            }
          } catch (error) {
            // User not found in Firebase Auth, check if it's a stable ID
            if (isStableEmailId(scanData.userId)) {
              // Try to find the email in the team member emails
              const matchingEmail = teamMemberEmails.find(email => 
                generateStableIdFromEmail(email) === scanData.userId
              );
              
              if (matchingEmail) {
                updates.userEmail = matchingEmail;
                needsUpdate = true;
                console.log(`Adding userEmail ${matchingEmail} to scan ${scanDoc.id} based on stable ID`);
              }
            }
          }
        }
        
        // Update the scan if needed
        if (needsUpdate) {
          await scanDoc.ref.update(updates);
          console.log(`Updated scan ${scanDoc.id}`);
        }
      }
    }
  }
  
  console.log('Migration completed successfully!');
}

// Run the migration
migrateScans()
  .then(() => {
    console.log('Migration script completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running migration script:', error);
    process.exit(1);
  });
