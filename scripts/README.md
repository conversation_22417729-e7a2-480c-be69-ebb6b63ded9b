# DeepScan Platform Migration Scripts

This directory contains scripts for migrating and updating data in the DeepScan platform.

## Prerequisites

Before running these scripts, make sure you have:

1. Node.js installed (v14 or later)
2. Firebase CLI installed (`npm install -g firebase-tools`)
3. Logged in to Firebase (`firebase login`)
4. Selected the correct Firebase project (`firebase use --add`)

## Available Scripts

### 1. Update Team Members

This script updates all teams with consistent member IDs and ensures all team members have proper IDs in the team document.

```bash
node scripts/update-team-members.js
```

### 2. Update Scan Associations

This script updates all scans with consistent user IDs and emails, adds team information to scans, and generates stable IDs for all users.

```bash
node scripts/update-scan-associations.js
```

## Running Order

For best results, run the scripts in this order:

1. First, update team members: `node scripts/update-team-members.js`
2. Then, update scan associations: `node scripts/update-scan-associations.js`

This ensures that team member IDs are consistent before updating scan associations.

## Troubleshooting

If you encounter any issues:

1. Make sure you're logged in to Firebase with the correct account
2. Verify you've selected the correct Firebase project
3. Check the Firebase console for any errors in the Firestore database
4. Try running the scripts with the `--debug` flag for more detailed logs

## After Migration

After running these scripts, you should see:

1. All team members have consistent IDs in the team document
2. All scans have proper associations with team members
3. All users have stable IDs that don't change between API calls

This will fix issues with scan counts and team member associations in the team dashboard.
