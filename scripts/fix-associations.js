/**
 * DeepScan Platform - Fix Associations Script
 * 
 * This script fixes all team member and scan associations in one go.
 * It requires minimal setup and handles everything automatically.
 * 
 * INSTRUCTIONS:
 * 1. Make sure you have Node.js installed
 * 2. Run: npm install firebase-admin
 * 3. Log in to Firebase: firebase login
 * 4. Select your project: firebase use --add
 * 5. Run this script: node scripts/fix-associations.js
 */

const admin = require('firebase-admin');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Initialize Firebase Admin SDK using application default credentials
try {
  admin.initializeApp({
    credential: admin.credential.applicationDefault()
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Error initializing Firebase Admin SDK:', error.message);
  console.log('\n📋 Please make sure you have set up Firebase credentials:');
  console.log('1. Run "firebase login" to log in to your Firebase account');
  console.log('2. Run "firebase use --add" to select your project');
  console.log('3. Try running this script again\n');
  process.exit(1);
}

const db = admin.firestore();
const auth = admin.auth();

// Utility function to generate stable ID from email
function generateStableIdFromEmail(email) {
  if (!email) return '';
  // Remove all non-alphanumeric characters and convert to lowercase
  const sanitizedEmail = email.toLowerCase().replace(/[^a-z0-9]/g, '');
  return `email-${sanitizedEmail}`;
}

// Utility function to check if an ID is a stable email-based ID
function isStableEmailId(id) {
  return id?.startsWith('email-');
}

// Function to update team members
async function updateTeamMembers() {
  console.log('\n🔄 Starting team member update...');
  
  // Step 1: Get all teams
  console.log('📊 Fetching all teams...');
  const teamsSnapshot = await db.collection('teams').get();
  console.log(`📋 Found ${teamsSnapshot.size} teams to process`);
  
  if (teamsSnapshot.empty) {
    console.log('⚠️ No teams found in the database');
    return;
  }
  
  // Step 2: Process each team
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const teamDoc of teamsSnapshot.docs) {
    const teamId = teamDoc.id;
    const teamData = teamDoc.data();
    
    try {
      const teamMembers = teamData.teamMembers || [];
      const teamMemberEmails = teamData.teamMemberEmails || [];
      
      console.log(`\n🔍 Processing team ${teamId}`);
      console.log(`📧 Team has ${teamMemberEmails.length} member emails and ${teamMembers.length} member IDs`);
      
      // Create a map of emails to user IDs
      const emailToIdMap = new Map();
      const updatedTeamMembers = [];
      
      // Process team member emails to get or create stable IDs
      for (const email of teamMemberEmails) {
        try {
          // Try to find user by email in Firebase Auth
          const userRecord = await auth.getUserByEmail(email);
          emailToIdMap.set(email, userRecord.uid);
          updatedTeamMembers.push(userRecord.uid);
          console.log(`✅ Mapped email ${email} to Firebase user ID: ${userRecord.uid}`);
        } catch (error) {
          // User not found in Firebase Auth, create a stable ID
          const stableId = generateStableIdFromEmail(email);
          emailToIdMap.set(email, stableId);
          updatedTeamMembers.push(stableId);
          console.log(`✅ Mapped email ${email} to stable ID: ${stableId}`);
        }
      }
      
      // Update the team document with consistent member IDs
      if (updatedTeamMembers.length > 0 && JSON.stringify(updatedTeamMembers) !== JSON.stringify(teamMembers)) {
        await teamDoc.ref.update({
          teamMembers: updatedTeamMembers
        });
        updatedCount++;
        console.log(`✅ Updated team ${teamId} with ${updatedTeamMembers.length} member IDs`);
      } else {
        console.log(`ℹ️ No updates needed for team ${teamId}`);
      }
    } catch (error) {
      console.error(`❌ Error processing team ${teamId}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n✅ Team member update completed!`);
  console.log(`📊 Updated ${updatedCount} teams`);
  console.log(`⚠️ Encountered errors with ${errorCount} teams`);
  
  return { updatedCount, errorCount };
}

// Function to update scan associations
async function updateScans() {
  console.log('\n🔄 Starting scan association update...');
  
  // Step 1: Get all scans
  console.log('📊 Fetching all scans...');
  const scansSnapshot = await db.collection('scans').get();
  console.log(`📋 Found ${scansSnapshot.size} scans to process`);
  
  if (scansSnapshot.empty) {
    console.log('⚠️ No scans found in the database');
    return;
  }
  
  // Step 2: Get all teams for quick lookup
  console.log('📊 Fetching all teams...');
  const teamsSnapshot = await db.collection('teams').get();
  console.log(`📋 Found ${teamsSnapshot.size} teams`);
  
  // Create a map of team member emails to team info
  const emailToTeamMap = new Map();
  
  for (const teamDoc of teamsSnapshot.docs) {
    const teamData = teamDoc.data();
    const teamMemberEmails = teamData.teamMemberEmails || [];
    
    for (const email of teamMemberEmails) {
      emailToTeamMap.set(email, {
        teamId: teamDoc.id,
        managerId: teamData.managerId,
        managerEmail: teamData.managerEmail
      });
    }
  }
  
  console.log(`📊 Created email-to-team map with ${emailToTeamMap.size} entries`);
  
  // Step 3: Process each scan
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const scanDoc of scansSnapshot.docs) {
    const scanId = scanDoc.id;
    const scanData = scanDoc.data();
    
    try {
      let needsUpdate = false;
      const updates = {};
      
      // Check if scan has userEmail
      if (scanData.userEmail) {
        // Generate stable ID if needed
        if (!scanData.stableId) {
          updates.stableId = generateStableIdFromEmail(scanData.userEmail);
          needsUpdate = true;
        }
        
        // Check if this user is part of a team
        const teamInfo = emailToTeamMap.get(scanData.userEmail);
        if (teamInfo && (!scanData.teamId || !scanData.managerId)) {
          updates.teamId = teamInfo.teamId;
          updates.managerId = teamInfo.managerId;
          updates.managerEmail = teamInfo.managerEmail;
          needsUpdate = true;
        }
        
        // If scan has userEmail but no userId, try to find the user in Firebase Auth
        if (!scanData.userId) {
          try {
            const userRecord = await auth.getUserByEmail(scanData.userEmail);
            updates.userId = userRecord.uid;
            needsUpdate = true;
          } catch (error) {
            // User not found in Firebase Auth, use stable ID
            updates.userId = generateStableIdFromEmail(scanData.userEmail);
            needsUpdate = true;
          }
        }
      }
      
      // If scan has userId but no userEmail, try to find the user in Firebase Auth
      if (scanData.userId && !scanData.userEmail) {
        try {
          // Only try to get user by ID if it's not a stable ID
          if (!isStableEmailId(scanData.userId)) {
            const userRecord = await auth.getUser(scanData.userId);
            if (userRecord.email) {
              updates.userEmail = userRecord.email;
              needsUpdate = true;
              
              // Check if this user is part of a team
              const teamInfo = emailToTeamMap.get(userRecord.email);
              if (teamInfo && (!scanData.teamId || !scanData.managerId)) {
                updates.teamId = teamInfo.teamId;
                updates.managerId = teamInfo.managerId;
                updates.managerEmail = teamInfo.managerEmail;
                needsUpdate = true;
              }
            }
          }
        } catch (error) {
          console.log(`ℹ️ Error getting user for userId ${scanData.userId}:`, error.message);
        }
      }
      
      // Update the scan if needed
      if (needsUpdate) {
        await db.collection('scans').doc(scanId).update(updates);
        updatedCount++;
        console.log(`✅ Updated scan ${scanId}`);
      }
    } catch (error) {
      console.error(`❌ Error processing scan ${scanId}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n✅ Scan association update completed!`);
  console.log(`📊 Updated ${updatedCount} scans`);
  console.log(`⚠️ Encountered errors with ${errorCount} scans`);
  
  return { updatedCount, errorCount };
}

// Main function to run the entire migration
async function runMigration() {
  console.log('🚀 Starting DeepScan Platform data migration...');
  
  // Ask for confirmation
  rl.question('\n⚠️ This script will update team members and scan associations in your database.\n⚠️ Make sure you have a backup before proceeding.\n\nDo you want to continue? (yes/no): ', async (answer) => {
    if (answer.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled by user');
      rl.close();
      process.exit(0);
    }
    
    console.log('\n🔄 Migration starting...');
    
    try {
      // Step 1: Update team members
      const teamResult = await updateTeamMembers();
      
      // Step 2: Update scan associations
      const scanResult = await updateScans();
      
      // Summary
      console.log('\n✅ Migration completed successfully!');
      console.log('📊 Summary:');
      console.log(`  - Updated ${teamResult?.updatedCount || 0} teams`);
      console.log(`  - Updated ${scanResult?.updatedCount || 0} scans`);
      console.log(`  - Encountered errors with ${teamResult?.errorCount || 0} teams and ${scanResult?.errorCount || 0} scans`);
      
      console.log('\n🎉 Your database has been updated with consistent IDs and associations!');
      console.log('🔍 The team dashboard should now display correct scan counts for all team members.');
      
      rl.close();
      process.exit(0);
    } catch (error) {
      console.error('\n❌ Error running migration:', error);
      rl.close();
      process.exit(1);
    }
  });
}

// Run the migration
runMigration();
