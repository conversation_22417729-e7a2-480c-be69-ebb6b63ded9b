# DeepScan Platform - Quick Fix Instructions

This guide will help you fix the team member and scan association issues with a single script.

## Prerequisites

Before running the fix script, make sure you have:

1. Node.js installed (v14 or later)
2. Firebase CLI installed
3. Access to your Firebase project

## Step-by-Step Instructions

### 1. Install Firebase CLI (if not already installed)

```bash
npm install -g firebase-tools
```

### 2. Log in to Firebase

```bash
firebase login
```

This will open a browser window where you need to log in with your Google account that has access to the Firebase project.

### 3. Select your Firebase project

```bash
firebase use --add
```

Follow the prompts to select your Firebase project.

### 4. Install required dependencies

Navigate to your project directory and run:

```bash
npm install firebase-admin
```

### 5. Run the fix script

```bash
node scripts/fix-associations.js
```

The script will:
- Ask for confirmation before making any changes
- Update all team members with consistent IDs
- Update all scans with proper associations
- Show a summary of the changes made

## What This Script Fixes

This script fixes several issues:

1. **Team Member IDs**: Ensures all team members have consistent IDs in the team document
2. **Scan Associations**: <PERSON>s scans to the correct team members
3. **Stable IDs**: Creates stable IDs for users without Firebase accounts
4. **Team Information**: Adds team and manager information to scans

After running this script, the team dashboard should correctly display scan counts for all team members.

## Troubleshooting

If you encounter any issues:

- Make sure you're logged in to Firebase with the correct account
- Verify you've selected the correct Firebase project
- Check that you have the necessary permissions to read/write to Firestore

If you need further assistance, please contact support.
