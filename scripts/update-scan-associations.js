/**
 * <PERSON><PERSON><PERSON> to update scan associations directly in the database
 * 
 * This script:
 * 1. Updates all scans with consistent user IDs and emails
 * 2. Adds team information to scans
 * 3. Generates stable IDs for all users
 * 
 * Run with: node scripts/update-scan-associations.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK using application default credentials
try {
  admin.initializeApp({
    credential: admin.credential.applicationDefault()
  });
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  console.log('\nPlease make sure you have set up Firebase credentials:');
  console.log('1. Run "firebase login" to log in to your Firebase account');
  console.log('2. Run "firebase use --add" to select your project');
  console.log('3. Try running this script again\n');
  process.exit(1);
}

const db = admin.firestore();
const auth = admin.auth();

// Utility function to generate stable ID from email
function generateStableIdFromEmail(email) {
  if (!email) return '';
  // Remove all non-alphanumeric characters and convert to lowercase
  const sanitizedEmail = email.toLowerCase().replace(/[^a-z0-9]/g, '');
  return `email-${sanitizedEmail}`;
}

// Utility function to check if an ID is a stable email-based ID
function isStableEmailId(id) {
  return id?.startsWith('email-');
}

// Main migration function
async function updateScans() {
  console.log('Starting scan association update...');
  
  // Step 1: Get all scans
  console.log('Fetching all scans...');
  const scansSnapshot = await db.collection('scans').get();
  console.log(`Found ${scansSnapshot.size} scans to process`);
  
  // Step 2: Get all teams for quick lookup
  console.log('Fetching all teams...');
  const teamsSnapshot = await db.collection('teams').get();
  console.log(`Found ${teamsSnapshot.size} teams`);
  
  // Create a map of team member emails to team info
  const emailToTeamMap = new Map();
  
  for (const teamDoc of teamsSnapshot.docs) {
    const teamData = teamDoc.data();
    const teamMemberEmails = teamData.teamMemberEmails || [];
    
    for (const email of teamMemberEmails) {
      emailToTeamMap.set(email, {
        teamId: teamDoc.id,
        managerId: teamData.managerId,
        managerEmail: teamData.managerEmail
      });
    }
  }
  
  console.log(`Created email-to-team map with ${emailToTeamMap.size} entries`);
  
  // Step 3: Process each scan
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const scanDoc of scansSnapshot.docs) {
    const scanId = scanDoc.id;
    const scanData = scanDoc.data();
    
    try {
      let needsUpdate = false;
      const updates = {};
      
      // Check if scan has userEmail
      if (scanData.userEmail) {
        // Generate stable ID if needed
        if (!scanData.stableId) {
          updates.stableId = generateStableIdFromEmail(scanData.userEmail);
          needsUpdate = true;
        }
        
        // Check if this user is part of a team
        const teamInfo = emailToTeamMap.get(scanData.userEmail);
        if (teamInfo && (!scanData.teamId || !scanData.managerId)) {
          updates.teamId = teamInfo.teamId;
          updates.managerId = teamInfo.managerId;
          updates.managerEmail = teamInfo.managerEmail;
          needsUpdate = true;
        }
        
        // If scan has userEmail but no userId, try to find the user in Firebase Auth
        if (!scanData.userId) {
          try {
            const userRecord = await auth.getUserByEmail(scanData.userEmail);
            updates.userId = userRecord.uid;
            needsUpdate = true;
          } catch (error) {
            // User not found in Firebase Auth, use stable ID
            updates.userId = generateStableIdFromEmail(scanData.userEmail);
            needsUpdate = true;
          }
        }
      }
      
      // If scan has userId but no userEmail, try to find the user in Firebase Auth
      if (scanData.userId && !scanData.userEmail) {
        try {
          // Only try to get user by ID if it's not a stable ID
          if (!isStableEmailId(scanData.userId)) {
            const userRecord = await auth.getUser(scanData.userId);
            if (userRecord.email) {
              updates.userEmail = userRecord.email;
              needsUpdate = true;
              
              // Check if this user is part of a team
              const teamInfo = emailToTeamMap.get(userRecord.email);
              if (teamInfo && (!scanData.teamId || !scanData.managerId)) {
                updates.teamId = teamInfo.teamId;
                updates.managerId = teamInfo.managerId;
                updates.managerEmail = teamInfo.managerEmail;
                needsUpdate = true;
              }
            }
          }
        } catch (error) {
          console.log(`Error getting user for userId ${scanData.userId}:`, error.message);
        }
      }
      
      // Update the scan if needed
      if (needsUpdate) {
        await db.collection('scans').doc(scanId).update(updates);
        updatedCount++;
        console.log(`Updated scan ${scanId} with:`, updates);
      }
    } catch (error) {
      console.error(`Error processing scan ${scanId}:`, error);
      errorCount++;
    }
  }
  
  console.log(`\nMigration completed!`);
  console.log(`Updated ${updatedCount} scans`);
  console.log(`Encountered errors with ${errorCount} scans`);
}

// Run the migration
updateScans()
  .then(() => {
    console.log('Update script completed successfully.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running update script:', error);
    process.exit(1);
  });
