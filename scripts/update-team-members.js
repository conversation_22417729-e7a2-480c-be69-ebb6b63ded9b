/**
 * <PERSON><PERSON><PERSON> to update team members with stable IDs
 * 
 * This script:
 * 1. Updates all teams with consistent member IDs
 * 2. Ensures all team members have proper IDs in the team document
 * 
 * Run with: node scripts/update-team-members.js
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK using application default credentials
try {
  admin.initializeApp({
    credential: admin.credential.applicationDefault()
  });
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  console.log('\nPlease make sure you have set up Firebase credentials:');
  console.log('1. Run "firebase login" to log in to your Firebase account');
  console.log('2. Run "firebase use --add" to select your project');
  console.log('3. Try running this script again\n');
  process.exit(1);
}

const db = admin.firestore();
const auth = admin.auth();

// Utility function to generate stable ID from email
function generateStableIdFromEmail(email) {
  if (!email) return '';
  // Remove all non-alphanumeric characters and convert to lowercase
  const sanitizedEmail = email.toLowerCase().replace(/[^a-z0-9]/g, '');
  return `email-${sanitizedEmail}`;
}

// Main migration function
async function updateTeamMembers() {
  console.log('Starting team member update...');
  
  // Step 1: Get all teams
  console.log('Fetching all teams...');
  const teamsSnapshot = await db.collection('teams').get();
  console.log(`Found ${teamsSnapshot.size} teams to process`);
  
  // Step 2: Process each team
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const teamDoc of teamsSnapshot.docs) {
    const teamId = teamDoc.id;
    const teamData = teamDoc.data();
    
    try {
      const teamMembers = teamData.teamMembers || [];
      const teamMemberEmails = teamData.teamMemberEmails || [];
      
      console.log(`\nProcessing team ${teamId} with ${teamMemberEmails.length} member emails and ${teamMembers.length} member IDs`);
      
      // Create a map of emails to user IDs
      const emailToIdMap = new Map();
      const updatedTeamMembers = [];
      
      // Process team member emails to get or create stable IDs
      for (const email of teamMemberEmails) {
        try {
          // Try to find user by email in Firebase Auth
          const userRecord = await auth.getUserByEmail(email);
          emailToIdMap.set(email, userRecord.uid);
          updatedTeamMembers.push(userRecord.uid);
          console.log(`Mapped email ${email} to Firebase user ID: ${userRecord.uid}`);
        } catch (error) {
          // User not found in Firebase Auth, create a stable ID
          const stableId = generateStableIdFromEmail(email);
          emailToIdMap.set(email, stableId);
          updatedTeamMembers.push(stableId);
          console.log(`Mapped email ${email} to stable ID: ${stableId}`);
        }
      }
      
      // Update the team document with consistent member IDs
      if (updatedTeamMembers.length > 0 && JSON.stringify(updatedTeamMembers) !== JSON.stringify(teamMembers)) {
        await teamDoc.ref.update({
          teamMembers: updatedTeamMembers
        });
        updatedCount++;
        console.log(`Updated team ${teamId} with ${updatedTeamMembers.length} member IDs`);
      } else {
        console.log(`No updates needed for team ${teamId}`);
      }
    } catch (error) {
      console.error(`Error processing team ${teamId}:`, error);
      errorCount++;
    }
  }
  
  console.log(`\nMigration completed!`);
  console.log(`Updated ${updatedCount} teams`);
  console.log(`Encountered errors with ${errorCount} teams`);
}

// Run the migration
updateTeamMembers()
  .then(() => {
    console.log('Update script completed successfully.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running update script:', error);
    process.exit(1);
  });
