/**
 * <PERSON><PERSON><PERSON> to update all existing users with their organization information
 * 
 * This script:
 * 1. Fetches all users from the Firestore 'users' collection
 * 2. Extracts the organization from each user's email
 * 3. Updates the user document with the organization field
 * 
 * Usage:
 * node scripts/update-user-organizations.js
 */

// Import Firebase Admin SDK
const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Initialize Firebase Admin SDK
const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');

if (!fs.existsSync(serviceAccountPath)) {
  console.error('Error: service-account.json file not found.');
  console.error('Please create a service account key file and save it as service-account.json in the project root.');
  process.exit(1);
}

try {
  const serviceAccount = require(serviceAccountPath);
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

/**
 * Extract organization name from an email address
 * @param {string} email User's email address
 * @returns {string|null} Organization name (domain without TLD) or null if not found
 */
function extractOrganizationFromEmail(email) {
  if (!email || typeof email !== 'string') {
    return null;
  }

  try {
    // Split the email by @ to get the domain part
    const parts = email.split('@');
    if (parts.length !== 2) {
      return null;
    }

    const domain = parts[1];
    
    // Split the domain by dots and get the organization name (second-level domain)
    const domainParts = domain.split('.');
    if (domainParts.length < 2) {
      return null;
    }

    // Return the organization name (second-level domain)
    // For example, for "<EMAIL>", return "company"
    return domainParts[0];
  } catch (error) {
    console.error('Error extracting organization from email:', error);
    return null;
  }
}

/**
 * Update all users with their organization information
 */
async function updateUserOrganizations() {
  try {
    console.log('Fetching users from Firestore...');
    
    // Get all users from the 'users' collection
    const usersSnapshot = await db.collection('users').get();
    
    if (usersSnapshot.empty) {
      console.log('No users found in the database.');
      return;
    }
    
    console.log(`Found ${usersSnapshot.size} users. Processing...`);
    
    // Counter for tracking updates
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    // Process each user
    const updatePromises = usersSnapshot.docs.map(async (doc) => {
      const userData = doc.data();
      const userId = doc.id;
      
      // Skip if user already has organization field
      if (userData.organization) {
        console.log(`User ${userId} already has organization: ${userData.organization}. Skipping.`);
        skippedCount++;
        return;
      }
      
      // Extract organization from email
      const email = userData.email;
      if (!email) {
        console.log(`User ${userId} has no email. Skipping.`);
        skippedCount++;
        return;
      }
      
      const organization = extractOrganizationFromEmail(email);
      if (!organization) {
        console.log(`Could not extract organization from email ${email} for user ${userId}. Skipping.`);
        skippedCount++;
        return;
      }
      
      // Update the user document with the organization field
      try {
        await db.collection('users').doc(userId).update({
          organization,
          updatedAt: new Date().toISOString()
        });
        
        console.log(`Updated user ${userId} with organization: ${organization}`);
        updatedCount++;
      } catch (error) {
        console.error(`Error updating user ${userId}:`, error);
        errorCount++;
      }
    });
    
    // Wait for all updates to complete
    await Promise.all(updatePromises);
    
    console.log('\nUpdate completed:');
    console.log(`- Updated: ${updatedCount} users`);
    console.log(`- Skipped: ${skippedCount} users`);
    console.log(`- Errors: ${errorCount} users`);
    
  } catch (error) {
    console.error('Error updating user organizations:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the update function
updateUserOrganizations();
