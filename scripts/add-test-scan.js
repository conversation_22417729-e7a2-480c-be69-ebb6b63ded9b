/**
 * <PERSON><PERSON><PERSON> to add a test scan to the database
 * 
 * This script:
 * 1. Adds a test scan to the Firestore 'scans' collection
 * 2. Associates it with the admin user
 * 
 * Usage:
 * node scripts/add-test-scan.js
 */

// Import Firebase Admin SDK
const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Initialize Firebase Admin SDK
const serviceAccountPath = path.resolve(process.cwd(), 'service-account.json');

if (!fs.existsSync(serviceAccountPath)) {
  console.error('Error: service-account.json file not found.');
  console.error('Please create a service account key file and save it as service-account.json in the project root.');
  process.exit(1);
}

try {
  const serviceAccount = require(serviceAccountPath);
  
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Admin user ID - replace with your admin user ID
const ADMIN_USER_ID = '2Afw5y7UrLRehX2srze4C3twQ5h2';
const ADMIN_EMAIL = '<EMAIL>';

/**
 * Add a test scan to the database
 */
async function addTestScan() {
  try {
    console.log('Creating test scan...');
    
    // Create a test scan
    const testScan = {
      id: `test_scan_${Date.now()}`,
      scan_id: `test_scan_${Date.now()}`,
      asset_type: 'web_application',
      name: 'Test Web Application Scan',
      status: 'pending',
      target: 'https://example.com',
      requestedAt: new Date().toISOString(),
      userId: ADMIN_USER_ID,
      userEmail: ADMIN_EMAIL,
      userDisplayName: 'Admin User',
      criticalVulnerabilities: 2,
      highVulnerabilities: 3,
      mediumVulnerabilities: 5,
      lowVulnerabilities: 8
    };
    
    // Add the scan to Firestore
    const docRef = await db.collection('scans').add(testScan);
    
    // Update the document with its own ID
    await docRef.update({ id: docRef.id });
    
    console.log(`Test scan created with ID: ${docRef.id}`);
    
  } catch (error) {
    console.error('Error adding test scan:', error);
  } finally {
    // Exit the process
    process.exit(0);
  }
}

// Run the function
addTestScan();
