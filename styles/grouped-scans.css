/* Enhanced animations for grouped scans */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0); /* Force GPU acceleration */
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0); /* Force GPU acceleration */
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0); /* Force GPU acceleration */
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0); /* Force GPU acceleration */
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary), 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out forwards;
}

/* Enhanced card hover effects */

/* Status badge animations */
.status-badge-enter {
  animation: fade-in 0.3s ease-out;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button hover effects */
.enhanced-button {
  position: relative;
  overflow: hidden;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  transform: translateX(-100%);
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: transform 0.5s;
  will-change: transform;
}

.enhanced-button:hover::before {
  transform: translateX(100%);
}

/* Critical badge pulse animation */
@keyframes critical-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.critical-badge {
  animation: critical-pulse 2s ease-in-out infinite;
}

/* Smooth transitions for all interactive elements */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced shadow effects */
.enhanced-shadow {
  box-shadow: 
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.enhanced-shadow:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Glass morphism effect */
.glass-effect {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .animate-fade-in {
    animation-duration: 0.4s;
  }
  
  .animate-slide-up {
    animation-duration: 0.3s;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .enhanced-shadow {
    box-shadow: 
      0 1px 3px 0 rgba(0, 0, 0, 0.3),
      0 1px 2px 0 rgba(0, 0, 0, 0.2);
  }
  
  .enhanced-shadow:hover {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.3),
      0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }
}

/* Loading state animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .critical-badge {
    animation: none;
  }
  
  .smooth-transition {
    transition: none;
  }
}

/* Focus states for better accessibility */
.focus-ring:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Enhanced status indicators */
.status-indicator {
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.8;
}

.status-indicator.active::after {
  animation: pulse-glow 2s ease-in-out infinite;
}