.stepper-container {
    position: relative;
    width: 100%;
    height: 20px;
    display: flex;
    align-items: center;
}

.stepper-track {
    position: absolute;
    width: 100%;
    height: 4px;
    background-color: #dee2e6; /* Darker gray for better contrast */
    border-radius: 2px;
}

.stepper-progress {
    position: absolute;
    height: 4px;
    background-color: #007bff;
    border-radius: 2px;
    transform-origin: left center;
    transition: transform 0.3s ease;
    will-change: transform;
}

.stepper-progress.re-test {
    background-color: #843DF5;
}

.stepper-checkpoints {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stepper-checkpoint {
    width: 16px;
    height: 16px;
    background-color: #ffffff; /* White background to stand out */
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease, border-color 0.3s ease;
    will-change: background-color, transform, border-color;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #495057;
    border: 2px solid #dee2e6; /* Border to match the track */
}

.stepper-checkpoint.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    transform: scale(1.1);
}

.stepper-checkpoint.active.re-test {
    background-color: #843DF5;
    border-color: #843DF5;
}

.stepper-checkpoint:hover {
    transform: scale(1.2);
}