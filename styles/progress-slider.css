/* Custom styling for the progress slider */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 12px;
  border-radius: 9999px;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Thumb styling for Webkit browsers (Chrome, Safari) */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: white;
  border: 2px solid #843DF5; /* GrowthGuard purple */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin-top: -3px;
}

/* Thumb styling for Firefox */
input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: white;
  border: 2px solid #843DF5; /* GrowthGuard purple */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Thumb styling for IE */
input[type="range"]::-ms-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: white;
  border: 2px solid #843DF5; /* GrowthGuard purple */
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Track styling for better visibility */
input[type="range"]::-webkit-slider-runnable-track {
  height: 12px;
  border-radius: 9999px;
}

input[type="range"]::-moz-range-track {
  height: 12px;
  border-radius: 9999px;
}

input[type="range"]::-ms-track {
  height: 12px;
  border-radius: 9999px;
}

/* Focus state */
input[type="range"]:focus {
  outline: none;
}

/* Hover state */
input[type="range"]:hover::-webkit-slider-thumb {
  background: #f9f7ff;
  border-color: #9b65f8;
}

input[type="range"]:hover::-moz-range-thumb {
  background: #f9f7ff;
  border-color: #9b65f8;
}

input[type="range"]:hover::-ms-thumb {
  background: #f9f7ff;
  border-color: #9b65f8;
}
