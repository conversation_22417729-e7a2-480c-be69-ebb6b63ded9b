/* Chat Performance Optimizations */

/* Hardware acceleration for smooth animations */
.chat-message-user,
.chat-message-assistant,
.animate-fade-in,
.animate-slide-up {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth scrolling with momentum */
.chat-scrollbar {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Optimized animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Reduce layout thrashing */
.message-content {
  contain: layout style;
}

/* Optimize text rendering */
.message-content,
.chat-input {
  text-rendering: optimizeSpeed;
  font-smooth: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* GPU-accelerated transforms */
.card-hover {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Optimize backdrop blur */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Prevent layout shifts */
.min-h-\[4\.5rem\] {
  min-height: 4.5rem;
  contain: layout;
}