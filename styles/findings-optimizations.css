/* Findings Page Performance Optimizations */

/* Hardware acceleration for vulnerability cards */
.vulnerability-card {
  will-change: transform, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style;
}

.vulnerability-card:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Optimized fade-in animation */
.animate-fadeIn {
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform, opacity;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Smooth slide animation */
.animate-slide-up {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  transform: translateZ(0);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 15px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Optimized selection controls */
.selection-controls {
  contain: layout style;
  transform: translateZ(0);
}

/* Bulk actions panel optimization */
.bulk-actions-panel {
  contain: layout style;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Smooth scrolling for findings */
.findings-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

/* Optimized loading states */
.findings-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.2s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .findings-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}

/* Status badge optimizations */
.status-badge {
  contain: layout style paint;
  transform: translateZ(0);
  transition: transform 0.15s ease-out;
}

.status-badge:hover {
  transform: scale(1.02) translateZ(0);
}

/* Filter toolbar optimization */
.filter-toolbar {
  contain: layout style;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Load more button optimization */
.load-more-btn {
  contain: layout style;
  transform: translateZ(0);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.load-more-btn:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Intersection observer optimization */
.lazy-vulnerability {
  content-visibility: auto;
  contain-intrinsic-size: 150px;
}

/* Performance containment */
.vulnerability-list {
  contain: layout;
}

.vulnerability-item {
  contain: layout style;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-slide-up,
  .vulnerability-card,
  .load-more-btn {
    animation: none;
    transition: none;
    transform: none;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .animate-fadeIn,
  .animate-slide-up {
    animation-duration: 0.2s;
  }
  
  .vulnerability-card {
    contain: layout;
  }
}

/* Container queries for responsive design */
@container (min-width: 768px) {
  .findings-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}

/* Critical rendering path */
.above-fold-findings {
  contain: layout style paint;
}

.below-fold-findings {
  content-visibility: auto;
  contain-intrinsic-size: 150px;
}