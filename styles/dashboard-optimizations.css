/* Dashboard Performance Optimizations */

/* Hardware acceleration for cards */
.group {
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

/* Optimize chart rendering */
.recharts-wrapper {
  contain: layout style;
}

/* Smooth card hover effects */
.group:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Optimize gradient backgrounds */
.bg-gradient-to-br {
  background-attachment: fixed;
}

/* Reduce layout shifts for loading states */
.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Optimize filter effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Optimize text rendering */
.text-3xl, .text-lg, .font-bold {
  text-rendering: optimizeSpeed;
  font-smooth: auto;
}

/* Container queries for responsive design */
@container (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}