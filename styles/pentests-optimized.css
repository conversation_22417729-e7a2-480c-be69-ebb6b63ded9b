/* Performance-optimized styles for Pentests page */

/* Use CSS containment for better performance */
.pentests-container {
  contain: layout style paint;
  will-change: transform;
}

/* Optimize animations with GPU acceleration */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
  transform: translateZ(0); /* Force GPU acceleration */
  backface-visibility: hidden;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out forwards;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimized keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Optimize scan card rendering */
.scan-card {
  contain: layout style;
  transform: translateZ(0);
  will-change: transform, opacity;
}

.scan-card:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Optimize loading placeholders */
.scan-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  contain: strict;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .scan-placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}

/* Optimize grouped lists */
.grouped-scan-item {
  contain: layout style;
  transform: translateZ(0);
}

/* Reduce paint operations for status badges */
.status-badge {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize search input */
.search-input {
  contain: layout style;
  will-change: border-color, box-shadow;
}

/* Optimize tab transitions */
.tab-content {
  contain: layout style;
  transform: translateZ(0);
}

/* Virtual scrolling optimizations */
.virtual-list-container {
  contain: strict;
  overflow: hidden;
}

.virtual-list-item {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize load more button */
.load-more-button {
  contain: layout style;
  transform: translateZ(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.load-more-button:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Reduce reflow for expanding cards */
.expandable-card {
  contain: layout;
}

.expandable-card-content {
  contain: layout style;
  overflow: hidden;
}

/* Optimize icon rendering */
.icon {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Performance hints for browsers */
.performance-critical {
  contain: layout style paint;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize for mobile devices */
@media (max-width: 768px) {
  .animate-fadeIn,
  .animate-slide-up {
    animation-duration: 0.2s; /* Faster animations on mobile */
  }
  
  .scan-card {
    contain: layout; /* Less containment on mobile for better compatibility */
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-slide-up,
  .scan-card,
  .load-more-button {
    animation: none;
    transition: none;
    transform: none;
  }
}

/* Critical rendering path optimizations */
.above-fold {
  contain: layout style paint;
  critical: true;
}

.below-fold {
  contain: layout;
  content-visibility: auto;
  contain-intrinsic-size: 200px; /* Estimated height */
}

/* Optimize text rendering */
.optimized-text {
  text-rendering: optimizeSpeed;
  font-display: swap;
}

/* Optimize image loading if any */
.scan-image {
  content-visibility: auto;
  contain-intrinsic-size: 100px 100px;
}