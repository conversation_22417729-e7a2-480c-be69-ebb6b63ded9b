/* Typing indicator styles */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
  min-height: 24px;
}

.typing-dots {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #843DF5;
  opacity: 0.6;
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Ensure smooth transitions for the AI summary content */
.ai-summary-content {
  transition: all 0.2s ease;
}
