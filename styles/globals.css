@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 261 89% 60%;
  --primary-foreground: 0 0% 100%;
  --secondary: 270 100% 98%;
  --secondary-foreground: 240 10% 16%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 261 89% 60%;
  --radius: 0.5rem;

  /* GrowthGuard Brand Colors */
  --violet: 261 89% 60%;
  --violet-light: 261 89% 80%;
  --violet-lighter: 261 60% 90%;
  --violet-lightest: 270 100% 98%;
  --dark: 240 10% 16%;
  --dark-deeper: 222 24% 11%;
  --dark-deepest: 240 100% 4%;
  --gray: 240 4% 96%;
}


body {
  font-family: var(--font-jakarta), system-ui, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 261 89% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 100% 98%;
    --secondary-foreground: 240 10% 16%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 261 89% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 270 100% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 261 89% 60%;

    /* GrowthGuard Brand Colors */
    --violet: 261 89% 60%;
    --violet-light: 261 89% 80%;
    --violet-lighter: 261 60% 90%;
    --violet-lightest: 270 100% 98%;
    --dark: 240 10% 16%;
    --dark-deeper: 222 24% 11%;
    --dark-deepest: 240 100% 4%;
    --gray: 240 4% 96%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 240 10% 16%;
    --foreground: 0 0% 98%;
    --card: 240 10% 14%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 16%;
    --popover-foreground: 0 0% 98%;
    --primary: 261 89% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 270 30% 20%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 261 89% 60%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 10% 16%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 261 89% 60%;

    /* GrowthGuard Brand Colors - Dark Theme */
    --violet: 261 89% 60%;
    --violet-light: 261 89% 80%;
    --violet-lighter: 261 60% 90%;
    --violet-lightest: 270 100% 98%;
    --dark: 240 10% 16%;
    --dark-deeper: 222 24% 11%;
    --dark-deepest: 240 100% 4%;
    --gray: 240 4% 96%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes titleChange {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  20% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-title-change {
  animation: titleChange 0.8s ease-out;
}

/* Custom card styling for scan cards and organization cards */
.dark .scan-card,
.dark .org-card {
  background-color: hsl(240 10% 12%);
  border-color: hsl(240 10% 18%);
}

.dark .scan-card [class*="CardFooter"],
.dark .scan-card [class*="CardHeader"],
.dark .org-card [class*="CardFooter"],
.dark .org-card [class*="CardHeader"],
.dark .org-card [class*="CardContent"] {
  background-color: hsl(240 10% 12%);
}

.dark .scan-card button[class*="bg-white"],
.dark .org-card button[class*="bg-white"] {
  background-color: hsl(240 10% 10%) !important;
}

.dark .scan-card button[class*="bg-black"],
.dark .org-card button[class*="bg-black"] {
  background-color: hsl(240 10% 10%) !important;
}

/* Make nested cards even darker */
.dark .org-card .org-card {
  background-color: hsl(240 10% 10%);
  border-color: hsl(240 10% 16%);
}

.dark .org-card .org-card [class*="CardHeader"],
.dark .org-card .org-card [class*="CardContent"] {
  background-color: hsl(240 10% 10%);
}

