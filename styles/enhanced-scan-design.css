/* Enhanced Scan Card Design System */

/* Improved animations with better performance */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(var(--primary), 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary), 0.6);
    transform: scale(1.02);
  }
}

@keyframes status-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced card animations */
.animate-fadeIn {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.animate-slide-up {
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Status badge animations */
.status-badge-animation {
  animation: status-change 0.4s ease-out;
}

/* Enhanced hover effects for cards */
.scan-card {
  contain: layout style;
  transform: translateZ(0);
  will-change: transform, box-shadow;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scan-card:hover {
  transform: translateY(-4px) translateZ(0);
}

/* Status-specific card styles */
.scan-card-pending {
  border-left: 4px solid hsl(var(--yellow-500));
}

.scan-card-completed {
  border-left: 4px solid hsl(var(--green-500));
}

.scan-card-in-progress {
  border-left: 4px solid hsl(var(--blue-500));
}

.scan-card-re-test {
  border-left: 4px solid hsl(var(--purple-500));
}

/* Enhanced status dots */
.status-dot-critical {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-dot-in-progress {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.status-dot-re-test {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.4);
}

/* Enhanced button hover effects */
.enhanced-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

/* Improved stepper styles */
.stepper-container {
  position: relative;
  height: 8px;
  margin: 16px 0;
}

.stepper-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to right, #e9ecef, #f8f9fa);
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.stepper-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 8px;
  background: linear-gradient(to right, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.stepper-progress.re-test {
  background: linear-gradient(to right, #843DF5, #6b21a8);
}

.stepper-checkpoints {
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 12px;
  display: flex;
  justify-content: space-between;
}

.stepper-checkpoint {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #ced4da;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stepper-checkpoint.active {
  border-color: #007bff;
  background: #007bff;
  box-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
}

.stepper-checkpoint.active.re-test {
  border-color: #843DF5;
  background: #843DF5;
  box-shadow: 0 0 8px rgba(132, 61, 245, 0.5);
}

.stepper-checkpoint:hover {
  transform: scale(1.1);
}

.stepper-wrapper {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

/* Enhanced vulnerability badges */
.vulnerability-badge {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
}

.vulnerability-badge:hover {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Glass morphism effects */
.glass-effect {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Enhanced shadows */
.enhanced-shadow {
  box-shadow: 
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.enhanced-shadow:hover {
  box-shadow: 
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .enhanced-shadow {
    box-shadow: 
      0 1px 3px 0 rgba(0, 0, 0, 0.3),
      0 1px 2px 0 rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  .enhanced-shadow:hover {
    box-shadow: 
      0 10px 25px -5px rgba(0, 0, 0, 0.4),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
}

/* Performance optimizations */
.performance-critical {
  contain: layout style paint;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .animate-fadeIn,
  .animate-slide-up {
    animation-duration: 0.4s;
  }
  
  .scan-card:hover {
    transform: translateY(-2px) translateZ(0);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-slide-up,
  .status-badge-animation,
  .scan-card {
    animation: none;
    transition: none;
  }
  
  .scan-card:hover {
    transform: none;
  }
}

/* Focus states for better accessibility */
.focus-ring:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 8px;
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted-foreground))/10 50%, 
    hsl(var(--muted)) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced tab styling */
.enhanced-tabs {
  background: linear-gradient(135deg, hsl(var(--muted))/50, hsl(var(--muted))/30);
  border: 1px solid hsl(var(--border))/50;
  backdrop-filter: blur(8px);
}

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted))/30;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground))/30;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground))/50;
}