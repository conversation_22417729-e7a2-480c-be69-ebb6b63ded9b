/* Enhanced Pentest Page Performance Optimizations */

/* Hardware acceleration for smooth interactions */
.pentest-card,
.animate-fadeIn,
.status-badge {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimized fade-in animation */
.animate-fadeIn {
  animation: optimizedFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes optimizedFadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 12px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Efficient hover effects */
.pentest-card:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Optimized loading states */
.pentest-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.2s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Container queries for responsive design */
@container (min-width: 768px) {
  .pentest-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

/* Intersection observer optimizations */
.lazy-load {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Smooth scrolling */
.pentest-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}