import { Server as NetServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { NextApiRequest } from 'next';
import { NextApiResponse } from 'next';

export type NextApiResponseWithSocket = NextApiResponse & {
  socket: {
    server: NetServer & {
      io?: SocketIOServer;
    };
  };
};

export const initSocketServer = (req: NextApiRequest, res: NextApiResponseWithSocket) => {
  if (!res.socket.server.io) {
    console.log('Initializing Socket.io server...');
    
    // Create a new Socket.io server
    const io = new SocketIOServer(res.socket.server, {
      path: '/api/socket',
      addTrailingSlash: false,
    });
    
    // Store the Socket.io server instance
    res.socket.server.io = io;
    
    // Set up event handlers
    io.on('connection', (socket) => {
      console.log(`Client connected: ${socket.id}`);
      
      // Join rooms based on user ID for targeted updates
      socket.on('join', (userId) => {
        console.log(`User ${userId} joined their room`);
        socket.join(`user-${userId}`);
      });
      
      // Join team room for managers
      socket.on('joinTeam', (managerId) => {
        console.log(`Manager ${managerId} joined team room`);
        socket.join(`team-${managerId}`);
      });
      
      socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
      });
    });
  }
  
  return res.socket.server.io;
};

// Helper function to emit scan updates to relevant users
export const emitScanUpdate = (
  io: SocketIOServer,
  scanData: any,
  event: 'scan-created' | 'scan-updated' | 'scan-deleted'
) => {
  const { userId } = scanData;
  
  // Emit to the specific user
  if (userId) {
    console.log(`Emitting ${event} to user-${userId}`);
    io.to(`user-${userId}`).emit(event, scanData);
  }
  
  // If this is a team scan, also emit to the team room
  if (scanData.managerId) {
    console.log(`Emitting ${event} to team-${scanData.managerId}`);
    io.to(`team-${scanData.managerId}`).emit(event, scanData);
  }
  
  // For admin updates, broadcast to all connected clients with admin role
  if (scanData.adminUpdate) {
    console.log(`Broadcasting ${event} to all admins`);
    io.to('admin-room').emit(event, scanData);
  }
};
