/**
 * Organization management utilities for the penetration testing platform
 */

import { db, auth } from "@/lib/firebase-admin";
import { extractOrganizationFromEmail } from "@/lib/user-utils";

export interface OrganizationMember {
  userId: string;
  email: string;
  displayName?: string;
  role?: string;
  joinedAt: string;
}

export interface OrganizationInfo {
  name: string;
  domain: string;
  members: OrganizationMember[];
  totalScans: number;
  totalVulnerabilities: number;
}

/**
 * Get the organization name for a user based on their email.
 * It first checks for an organization field in the user's Firestore document,
 * then falls back to extracting it from the email domain.
 * @param userEmail User's email address
 * @returns Organization name or null if not found
 */
export async function getUserOrganization(userEmail: string): Promise<string | null> {
  if (!userEmail) return null;

  try {
    const userRecord = await auth.getUserByEmail(userEmail);
    if (userRecord?.uid) {
      const userDoc = await db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        if (userData?.organization) {
          return userData.organization;
        }
      }
    }
  } catch (error: any) {
    if (error.code !== 'auth/user-not-found') {
      console.error('Error getting user organization from Auth/Firestore:', error);
    }
    // Fallthrough to email extraction if user not found or no org in DB
  }
  
  return extractOrganizationFromEmail(userEmail);
}

/**
 * Get all members of an organization from Firestore.
 * This implementation assumes that an 'organization' field is present on user documents.
 * @param organizationName The name of the organization.
 * @returns A promise that resolves to an array of organization members.
 */
export async function getOrganizationMembers(organizationName: string): Promise<OrganizationMember[]> {
  if (!organizationName) return [];

  try {
    const usersSnapshot = await db.collection('users').where('organization', '==', organizationName).get();

    if (usersSnapshot.empty) {
      return [];
    }

    const members: OrganizationMember[] = usersSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        userId: doc.id,
        email: data.email,
        displayName: data.displayName,
        role: data.role,
        joinedAt: data.createdAt || (data.metadata?.creationTime) || new Date().toISOString(),
      };
    });
    
    return members;
  } catch (error) {
    console.error(`Error getting organization members for "${organizationName}":`, error);
    return [];
  }
}

/**
 * Get all scan IDs for a list of organization members.
 * @param memberUserIds An array of user IDs of the organization members.
 * @returns Array of unique scan IDs belonging to the organization members.
 */
export async function getOrganizationScanIds(memberUserIds: string[]): Promise<string[]> {
  if (!memberUserIds || memberUserIds.length === 0) return [];

  const scanIds = new Set<string>();

  try {
    // Firestore 'in' query limit is 30, using 10 for safety.
    const batchSize = 10;
    for (let i = 0; i < memberUserIds.length; i += batchSize) {
      const batch = memberUserIds.slice(i, i + batchSize);
      
      const scansSnapshot = await db.collection('scans').where('userId', 'in', batch).get();
      
      scansSnapshot.forEach(doc => {
        scanIds.add(doc.id);
      });
    }
    
    return Array.from(scanIds);
  } catch (error) {
    console.error('Error getting organization scan IDs:', error);
    return [];
  }
}

/**
 * Get organization information including members and statistics.
 * @param organizationName Organization name
 * @returns Organization information object or null if not found.
 */
export async function getOrganizationInfo(organizationName: string): Promise<OrganizationInfo | null> {
  if (!organizationName) return null;

  try {
    const members = await getOrganizationMembers(organizationName);
    if (members.length === 0) return null;

    const memberUserIds = members.map(member => member.userId);
    const scanIds = await getOrganizationScanIds(memberUserIds);

    let totalVulnerabilities = 0;
    if (scanIds.length > 0) {
      const batchSize = 10;
      for (let i = 0; i < scanIds.length; i += batchSize) {
        const batch = scanIds.slice(i, i + batchSize);
        
        const vulnerabilitiesSnapshot = await db.collection('vulnerabilities').where('scanId', 'in', batch).get();
        
        totalVulnerabilities += vulnerabilitiesSnapshot.size;
      }
    }

    const domain = members.length > 0 ? members[0].email.split('@')[1] : '';

    return {
      name: organizationName,
      domain,
      members,
      totalScans: scanIds.length,
      totalVulnerabilities,
    };
  } catch (error) {
    console.error(`Error getting organization info for "${organizationName}":`, error);
    return null;
  }
}

/**
 * Update user's organization in Firestore.
 * @param userId User ID
 * @param organizationName Organization name
 */
export async function updateUserOrganization(userId: string, organizationName: string): Promise<void> {
  try {
    await db.collection('users').doc(userId).set({
      organization: organizationName,
      updatedAt: new Date().toISOString()
    }, { merge: true });
  } catch (error) {
    console.error(`Error updating organization for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Check if a user belongs to a specific organization.
 * @param userEmail User's email
 * @param organizationName Organization name to check
 * @returns Boolean indicating membership.
 */
export async function isUserInOrganization(userEmail: string, organizationName: string): Promise<boolean> {
  if (!userEmail || !organizationName) return false;

  try {
    const userOrg = await getUserOrganization(userEmail);
    return userOrg === organizationName;
  } catch (error) {
    console.error('Error checking user organization membership:', error);
    return false;
  }
}