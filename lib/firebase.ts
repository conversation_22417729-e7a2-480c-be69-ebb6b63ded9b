// Firebase initialization for client-side usage
import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth, sendSignInLinkToEmail, isSignInWithEmailLink, signInWithEmailLink } from "firebase/auth";
// Only import analytics in the browser
let analytics: import("firebase/analytics").Analytics | undefined = undefined;

// Determine which environment we're in
const isDevelopment = process.env.NODE_ENV === 'development';
console.log(`Running in ${isDevelopment ? 'development' : 'production'} mode`);

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET, // e.g. deepscan-growthguard.appspot.com
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Prevent re-initialization on hot reload
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Only initialize analytics in the browser (not during SSR)
if (typeof window !== "undefined" && "measurementId" in firebaseConfig) {
  import("firebase/analytics").then(({ getAnalytics }) => {
    analytics = getAnalytics(app);
  });
}

export const auth = getAuth(app);

// Configure auth settings for email link sign-in
auth.useDeviceLanguage();

// Helper functions for email link authentication
export const sendSignInLink = async (email: string, redirectUrl: string) => {
  // Save the email to localStorage for later use
  if (typeof window !== "undefined") {
    window.localStorage.setItem('emailForSignIn', email);
  }

  // Configure the action code settings
  const actionCodeSettings = {
    url: redirectUrl,
    handleCodeInApp: true,
  };

  // Send the sign-in link
  return sendSignInLinkToEmail(auth, email, actionCodeSettings);
};

export const completeSignInWithEmailLink = async (email: string) => {
  // Check if the URL contains a sign-in link
  if (typeof window !== "undefined" && isSignInWithEmailLink(auth, window.location.href)) {
    // If no email is provided, try to get it from localStorage
    if (!email) {
      email = window.localStorage.getItem('emailForSignIn') || '';
      if (!email) {
        throw new Error('Email is required to complete sign-in');
      }
    }

    // Complete the sign-in process
    const result = await signInWithEmailLink(auth, email, window.location.href);

    // Clear the email from localStorage
    window.localStorage.removeItem('emailForSignIn');

    return result;
  } else {
    throw new Error('Invalid sign-in link');
  }
};

export const isEmailSignInLink = (url: string) => {
  return isSignInWithEmailLink(auth, url);
};

export default app;
