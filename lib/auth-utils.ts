import { NextRequest } from "next/server";
import { auth } from "@/lib/firebase-admin";

/**
 * Verifies the authentication token from the request headers
 * @param request The Next.js request object
 * @returns An object containing the user (if authenticated) and any error
 */
export async function verifyAuth(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { user: null, error: "Missing or invalid authorization header" };
    }

    // Extract the token
    const token = authHeader.split("Bearer ")[1];
    if (!token) {
      return { user: null, error: "Missing token" };
    }

    // Verify the token
    const decodedToken = await auth.verifyIdToken(token);
    
    // Get the user's custom claims (role)
    const userRecord = await auth.getUser(decodedToken.uid);
    const customClaims = userRecord.customClaims || {};
    
    // Return the user with role information
    return { 
      user: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        role: customClaims.role || "client", // Default to client if no role specified
        ...customClaims
      }, 
      error: null 
    };
  } catch (error: any) {
    console.error("Auth verification error:", error);
    return { user: null, error: error.message || "Authentication failed" };
  }
}
