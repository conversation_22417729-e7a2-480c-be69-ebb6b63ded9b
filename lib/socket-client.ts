'use client';

// This file is for client-side socket.io configuration only
import { io, Socket } from 'socket.io-client';
import { SocketEvents } from './socket-events';

// Flag to track if we're in a serverless environment
let isServerlessEnvironment = false;

// Debug mode flag - set to true for verbose logging during development
const DEBUG_SOCKET = true;

// Helper function for logging
const logDebug = (message: string, ...args: any[]) => {
  if (DEBUG_SOCKET) {
    console.log(`[Socket Client] ${message}`, ...args);
  }
};

// Singleton socket instance
let socketInstance: Socket | null = null;

// Track if we've already tried to initialize
let initializationInProgress = false;
let initializationPromise: Promise<Socket> | null = null;

// Track connection attempts
let connectionAttempts = 0;
const MAX_CONNECTION_ATTEMPTS = 5;

// Check if we're in a serverless environment
const checkServerlessEnvironment = async (): Promise<boolean> => {
  try {
    // Make a request to the socket endpoint to check if we're in a serverless environment
    const response = await fetch('/api/socket');
    const data = await response.json();

    // Check if the response indicates we're in a serverless environment
    if (data.isServerless === true) {
      logDebug('Detected serverless environment from API response (explicit flag)');
      return true;
    }

    if (data.message && (
      data.message.includes('serverless environment') ||
      data.message.includes('missing socket')
    )) {
      logDebug('Detected serverless environment from API response (message content)');
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking serverless environment:', error);
    return false;
  }
};

// Initialize socket connection
export const initSocketConnection = async (): Promise<Socket> => {
  // First, check if we're in a serverless environment
  if (!isServerlessEnvironment) {
    isServerlessEnvironment = await checkServerlessEnvironment();

    if (isServerlessEnvironment) {
      logDebug('Running in serverless environment, WebSockets are not supported');
      // Create a dummy socket that will never connect but won't throw errors
      const dummySocket = io('/api/socket', { autoConnect: false }) as Socket;
      socketInstance = dummySocket;

      // Return the dummy socket - applications should check isServerlessEnvironment
      // before relying on real-time updates
      return dummySocket;
    }
  }

  // If we already determined we're in a serverless environment, return the dummy socket
  if (isServerlessEnvironment) {
    logDebug('Already determined we are in a serverless environment, returning dummy socket');
    if (!socketInstance) {
      socketInstance = io('/api/socket', { autoConnect: false }) as Socket;
    }
    return socketInstance;
  }

  // If we already have a socket instance and it's connected, return it
  if (socketInstance && socketInstance.connected) {
    logDebug('Using existing connected socket instance');
    return socketInstance;
  }

  // If we have a socket instance but it's disconnected, try to reconnect
  if (socketInstance && !socketInstance.connected) {
    logDebug('Socket exists but is disconnected, attempting to reconnect');
    socketInstance.connect();
    return socketInstance;
  }

  // If initialization is already in progress, return the existing promise
  if (initializationInProgress && initializationPromise) {
    logDebug('Socket initialization already in progress, returning existing promise');
    return initializationPromise;
  }

  // Set flag and create a new promise
  initializationInProgress = true;
  connectionAttempts = 0;

  initializationPromise = (async () => {
    try {
      logDebug('Initializing new socket connection...');

      // Create the socket connection with more resilient settings
      socketInstance = io({
        path: '/api/socket',
        addTrailingSlash: false,
        reconnectionAttempts: 5,        // Reduced number of reconnection attempts to avoid excessive retries
        reconnectionDelay: 2000,        // Slightly longer initial delay
        timeout: 30000,                 // Increased timeout
        transports: ['polling', 'websocket'], // Try polling first, then WebSocket (more reliable initial connection)
        reconnection: true,             // Enable reconnection
        reconnectionDelayMax: 10000,    // Increased maximum delay between reconnection attempts
        randomizationFactor: 0.5,       // Standard randomization
        forceNew: true,                 // Create a new connection each time to avoid stale connections
        autoConnect: true,              // Connect automatically
      });

      // Set up connection event handlers
      socketInstance.on('connect', () => {
        logDebug('Socket connected successfully');
        connectionAttempts = 0; // Reset connection attempts on successful connection

        // Set up ping interval to keep connection alive
        if (socketInstance) {
          setupPingInterval(socketInstance);
        }
      });

      // Handle disconnect events with improved handling
      socketInstance.on('disconnect', (reason) => {
        logDebug(`Socket disconnected: ${reason}`);

        // If the disconnection was due to a transport error, handle it more gracefully
        if (reason === 'transport error' || reason === 'transport close') {
          console.warn(`Transport issue detected. Reason: ${reason}`);

          // Increment connection attempts
          connectionAttempts++;

          if (connectionAttempts > MAX_CONNECTION_ATTEMPTS) {
            console.error(`Exceeded maximum connection attempts (${MAX_CONNECTION_ATTEMPTS}). Will try again when user interacts with the page.`);

            // If we've exceeded max attempts, don't try to reconnect automatically
            // Instead, we'll wait for user interaction to trigger a reconnection
            if (socketInstance) {
              // Disable auto reconnect after too many failed attempts
              socketInstance.io.reconnection(false);

              // Add a one-time event listener for user interaction to try reconnecting again
              if (typeof window !== 'undefined') {
                const reconnectOnInteraction = () => {
                  logDebug('User interaction detected, attempting to reconnect socket');
                  connectionAttempts = 0;

                  // Re-enable reconnection
                  if (socketInstance) {
                    socketInstance.io.reconnection(true);
                    socketInstance.connect();
                  } else {
                    // If socket instance was destroyed, create a new one
                    initSocketConnection();
                  }

                  // Remove the event listeners after first interaction
                  ['click', 'keydown', 'touchstart'].forEach(event => {
                    window.removeEventListener(event, reconnectOnInteraction);
                  });
                };

                // Add event listeners for user interaction
                ['click', 'keydown', 'touchstart'].forEach(event => {
                  window.addEventListener(event, reconnectOnInteraction, { once: true });
                });
              }
            }
          }
        }
      });

      // Handle connection errors with improved recovery
      socketInstance.on('connect_error', (error) => {
        console.error('Socket connection error:', error.message);

        // Increment connection attempts
        connectionAttempts++;

        if (connectionAttempts > MAX_CONNECTION_ATTEMPTS) {
          console.error(`Exceeded maximum connection attempts (${MAX_CONNECTION_ATTEMPTS}). Will try again when user interacts with the page.`);

          // Similar approach as in disconnect handler
          if (socketInstance) {
            socketInstance.io.reconnection(false);
          }
        } else {
          // For fewer attempts, log more detailed information to help debugging
          console.warn(`Connection attempt ${connectionAttempts}/${MAX_CONNECTION_ATTEMPTS} failed. Will retry in ${socketInstance?.io?.reconnectionDelay() || 'unknown'} ms`);
        }
      });

      // Handle reconnect attempts
      socketInstance.io.on(SocketEvents.RECONNECT_ATTEMPT, (attempt) => {
        logDebug(`Reconnection attempt ${attempt}`);
      });

      // Handle successful reconnection
      socketInstance.io.on(SocketEvents.RECONNECT, (attempt) => {
        logDebug(`Reconnected after ${attempt} attempts`);
        connectionAttempts = 0; // Reset connection attempts on successful reconnection
      });

      // Handle room join acknowledgment
      socketInstance.on(SocketEvents.ROOM_JOINED, (data) => {
        logDebug('Room joined acknowledgment:', data);
      });

      logDebug('Socket.io client initialized successfully');
      return socketInstance;
    } catch (error) {
      console.error('Error initializing socket connection:', error);
      // Reset flags so we can try again
      initializationInProgress = false;
      initializationPromise = null;
      throw error;
    } finally {
      // Reset the in-progress flag
      initializationInProgress = false;
    }
  })();

  return initializationPromise;
};

// Track active ping intervals globally for cleanup
const activePingIntervals = new Set<NodeJS.Timeout>();

// Set up ping interval to keep connection alive with improved reliability
const setupPingInterval = (socket: Socket) => {
  logDebug('Setting up ping interval');

  // Track missed pongs to detect connection issues
  let missedPongs = 0;
  const MAX_MISSED_PONGS = 3;
  let lastPongTime = Date.now();
  let pongListener: ((data: any) => void) | null = null;
  let disconnectListener: ((reason: string) => void) | null = null;

  // Set up a ping interval to keep the connection alive
  const pingInterval = setInterval(() => {
    if (socket && socket.connected) {
      // Check if we've missed too many pongs
      if (missedPongs >= MAX_MISSED_PONGS) {
        logDebug(`Missed ${missedPongs} pongs in a row, forcing reconnection`);

        // Force a reconnection if we've missed too many pongs
        socket.disconnect().connect();

        // Reset counter
        missedPongs = 0;
        return;
      }

      // Check if it's been too long since the last pong
      const timeSinceLastPong = Date.now() - lastPongTime;
      if (timeSinceLastPong > 45000) { // 45 seconds
        logDebug(`No pong received in ${timeSinceLastPong}ms, forcing reconnection`);

        // Force a reconnection if it's been too long
        socket.disconnect().connect();

        // Reset time
        lastPongTime = Date.now();
        return;
      }

      logDebug('Sending ping to server');
      socket.emit(SocketEvents.PING);

      // Increment missed pongs - will be decremented when we get a pong
      missedPongs++;
    } else {
      // Clear interval if socket is disconnected
      clearPingInterval();
    }
  }, 20000); // Send ping every 20 seconds

  // Add to active intervals for global cleanup
  activePingIntervals.add(pingInterval);

  // Function to clear this specific ping interval
  const clearPingInterval = () => {
    if (pingInterval) {
      clearInterval(pingInterval);
      activePingIntervals.delete(pingInterval);
      logDebug('Cleared ping interval');
    }
    
    // Remove event listeners
    if (pongListener && socket) {
      socket.off(SocketEvents.PONG, pongListener);
      pongListener = null;
    }
    if (disconnectListener && socket) {
      socket.off(SocketEvents.DISCONNECT, disconnectListener);
      disconnectListener = null;
    }
  };

  // Listen for pong responses
  pongListener = () => {
    logDebug('Received pong from server');
    // Reset missed pongs counter and update last pong time
    missedPongs = 0;
    lastPongTime = Date.now();
  };
  socket.on(SocketEvents.PONG, pongListener);

  // Clean up interval on disconnect
  disconnectListener = () => {
    clearPingInterval();
    logDebug('Cleared ping interval due to disconnect event');
  };
  socket.on(SocketEvents.DISCONNECT, disconnectListener);

  return pingInterval;
};

// Global cleanup function for all ping intervals
export const cleanupAllPingIntervals = () => {
  activePingIntervals.forEach(interval => {
    clearInterval(interval);
  });
  activePingIntervals.clear();
  logDebug('Cleaned up all ping intervals');
};

// Get the socket instance
export const getSocket = (): Socket | null => {
  return socketInstance;
};

// Clean up socket connection - only use this when the app is being completely unloaded
// We don't want to disconnect during normal page navigation
export const cleanupSocketConnection = () => {
  // In a Next.js app with client-side navigation, we generally don't want to disconnect
  // the socket during normal navigation. Only disconnect when the app is being completely unloaded.
  if (typeof window !== 'undefined') {
    // Add an event listener for beforeunload to disconnect the socket when the page is actually closed
    window.addEventListener('beforeunload', () => {
      if (socketInstance) {
        logDebug('App unloading, disconnecting socket');
        socketInstance.disconnect();
        socketInstance = null;
      }
    }, { once: true });
  }
};

// Check if we're in a serverless environment
export const isInServerlessEnvironment = (): boolean => {
  return isServerlessEnvironment;
};

// Export types and events
export { SocketEvents, Socket };
