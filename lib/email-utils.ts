import { auth, db } from '@/lib/firebase-admin';

/**
 * Get admin email addresses from Firebase
 * @returns Array of admin email addresses
 */
export async function getAdminEmails(): Promise<string[]> {
  try {
    // Query users with admin role
    const usersSnapshot = await db.collection('users')
      .where('role', '==', 'admin')
      .get();

    if (usersSnapshot.empty) {
      // Fallback to users with admin role in custom claims
      const allUsers = await auth.listUsers();
      const adminUsers = [];

      for (const user of allUsers.users) {
        if (user.email && user.customClaims && user.customClaims.role === 'admin') {
          adminUsers.push(user.email);
        }
      }

      return adminUsers.length > 0 ? adminUsers : ['<EMAIL>'];
    }

    // Extract emails from user documents
    const adminEmails: string[] = [];
    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      if (userData.email) {
        adminEmails.push(userData.email);
      }
    });

    return adminEmails;
  } catch (error) {
    console.error('Error fetching admin emails:', error);
    // Return a default admin email as fallback
    return ['<EMAIL>'];
  }
}

/**
 * Get user email by user ID
 * @param userId Firebase user ID
 * @returns User's email address
 */
export async function getUserEmail(userId: string): Promise<string | null> {
  try {
    const userRecord = await auth.getUser(userId);
    // Convert undefined to null to match the return type
    return userRecord.email || null;
  } catch (error) {
    console.error(`Error fetching email for user ${userId}:`, error);
    return null;
  }
}

/**
 * Get notification recipients for a scan status change
 * @param scanUserId User ID of the scan owner
 * @returns Array of email addresses to notify
 */
export async function getNotificationRecipients(scanUserId: string): Promise<string[]> {
  try {
    // Get the client's email
    const clientEmail = await getUserEmail(scanUserId);

    // Get admin emails
    const adminEmails = await getAdminEmails();

    // Combine and filter out null values
    const recipients = [clientEmail, ...adminEmails].filter(
      (email): email is string => email !== null
    );

    // Remove duplicates
    return [...new Set(recipients)];
  } catch (error) {
    console.error('Error getting notification recipients:', error);
    return ['<EMAIL>']; // Fallback
  }
}
import { Resend } from 'resend';
import { StatusChangeEmail } from '@/emails/StatusChangeEmail';
import { ProgressUpdateEmail } from '@/emails/ProgressUpdateEmail';
import ReactDOMServer from 'react-dom/server';
import React from 'react';

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailDetails {
  scanId: string;
  oldStatus?: string | null;
  newStatus?: string;
  progressUpdate?: number;
  scanDetails: {
    target: string;
    asset_type: string;
  };
  triggeringUserId: string;
}

export async function sendNotificationEmail(details: EmailDetails) {
  const { scanId, oldStatus, newStatus, progressUpdate, scanDetails, triggeringUserId } = details;

  const scanDoc = await db.collection('scans').doc(scanId).get();
  const scanData = scanDoc.data();
  const originalCreatorId = scanData?.userId;

  const adminEmails = await getAdminEmails();
  const recipientsSet = new Set(adminEmails);

  const triggererEmail = await getUserEmail(triggeringUserId);
  if (triggererEmail) {
    recipientsSet.add(triggererEmail);
  }

  if (originalCreatorId) {
    const originalCreatorEmail = await getUserEmail(originalCreatorId);
    if (originalCreatorEmail) {
      recipientsSet.add(originalCreatorEmail);
    }
  }

  const recipientsList = Array.from(recipientsSet);

  if (recipientsList.length === 0) {
    console.log('No recipients found for email notification.');
    return;
  }

  let subject: string;
  let html: string;

  const formatStatus = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'in-progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 're-test': return 'Re-test';
      default: return status;
    }
  };

  if (progressUpdate !== undefined) {
    subject = `Pen Test Progress Update: ${progressUpdate}% Complete`;
    html = ReactDOMServer.renderToStaticMarkup(
      React.createElement(ProgressUpdateEmail, {
        scanName: scanDetails.target,
        assetType: scanDetails.asset_type,
        progress: progressUpdate,
        scanId: scanId,
      })
    );
  } else if (newStatus) {
    if (oldStatus) {
      subject = `Scan Status Update: ${formatStatus(newStatus)}`;
    } else {
      subject = `New Scan Created: ${formatStatus(newStatus)}`;
    }
    html = ReactDOMServer.renderToStaticMarkup(
      React.createElement(StatusChangeEmail, {
        scanName: scanDetails.target,
        assetType: scanDetails.asset_type,
        oldStatus: oldStatus || 'New',
        newStatus: newStatus,
        scanId: scanId,
      })
    );
  } else {
    console.error('Email not sent: newStatus or progressUpdate must be defined.');
    return;
  }

  for (const email of recipientsList) {
    try {
      await resend.emails.send({
        from: 'DeepScan <<EMAIL>>',
        to: email,
        subject,
        html,
        replyTo: '<EMAIL>'
      });
      console.log(`✅ Email sent successfully to ${email}`);
    } catch (error) {
      console.error(`❌ Failed to send email to ${email}:`, error);
    }
  }
}
