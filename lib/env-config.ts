/**
 * Environment configuration utility
 * 
 * This file provides utilities for working with environment-specific configurations.
 * It helps determine which environment is active and loads the appropriate configuration.
 */

/**
 * Determine if the application is running in development mode
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Determine if the application is running in production mode
 */
export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

/**
 * Get the current environment name
 */
export const getEnvironment = (): string => {
  return process.env.NODE_ENV || 'development';
};

/**
 * Log the current environment
 */
export const logEnvironment = (): void => {
  console.log(`Application running in ${getEnvironment()} environment`);
};

/**
 * Get Firebase project ID for the current environment
 */
export const getFirebaseProjectId = (): string => {
  return process.env.FIREBASE_PROJECT_ID || '';
};

/**
 * Get Firebase storage bucket for the current environment
 */
export const getFirebaseStorageBucket = (): string => {
  return process.env.FIREBASE_STORAGE_BUCKET || '';
};
