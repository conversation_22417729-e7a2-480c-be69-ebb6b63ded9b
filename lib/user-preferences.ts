import { db } from '@/lib/firebase-admin';

/**
 * User notification preferences interface
 */
export interface NotificationPreferences {
  emailNotifications: boolean;
  scanCompletedNotifications: boolean;
  messageNotifications: boolean;
}

/**
 * Get user notification preferences from Firestore
 * @param userId User ID
 * @returns User notification preferences or default values
 */
export async function getUserNotificationPreferences(userId: string): Promise<NotificationPreferences> {
  try {
    // Get user preferences from Firestore
    const userDoc = await db.collection('user-preferences').doc(userId).get();
    
    if (userDoc.exists) {
      const data = userDoc.data();
      return {
        emailNotifications: data?.emailNotifications ?? true,
        scanCompletedNotifications: data?.scanCompletedNotifications ?? true,
        messageNotifications: data?.messageNotifications ?? true
      };
    }
    
    // Return default preferences if not found
    return {
      emailNotifications: true,
      scanCompletedNotifications: true,
      messageNotifications: true
    };
  } catch (error) {
    console.error('Error getting user notification preferences:', error);
    // Return default preferences on error
    return {
      emailNotifications: true,
      scanCompletedNotifications: true,
      messageNotifications: true
    };
  }
}

/**
 * Save user notification preferences to Firestore
 * @param userId User ID
 * @param preferences Notification preferences to save
 * @returns Success status
 */
export async function saveUserNotificationPreferences(
  userId: string, 
  preferences: NotificationPreferences
): Promise<boolean> {
  try {
    // Save preferences to Firestore
    await db.collection('user-preferences').doc(userId).set(
      preferences, 
      { merge: true }
    );
    return true;
  } catch (error) {
    console.error('Error saving user notification preferences:', error);
    return false;
  }
}

/**
 * Check if a user has enabled email notifications
 * @param userId User ID
 * @returns Boolean indicating if email notifications are enabled
 */
export async function isEmailNotificationsEnabled(userId: string): Promise<boolean> {
  try {
    const preferences = await getUserNotificationPreferences(userId);
    return preferences.emailNotifications;
  } catch (error) {
    console.error('Error checking email notification status:', error);
    return true; // Default to enabled on error
  }
}
