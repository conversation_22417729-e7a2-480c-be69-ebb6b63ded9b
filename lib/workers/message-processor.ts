// Web Worker for processing messages without blocking UI
self.onmessage = function(e) {
  const { type, data } = e.data;
  
  switch (type) {
    case 'PROCESS_MESSAGES':
      const { messages, searchQuery } = data;
      const filtered = messages.filter((msg: any) => 
        msg.role === 'user' && 
        typeof msg.content === 'string' && 
        msg.content.toLowerCase().includes(searchQuery.toLowerCase())
      );
      self.postMessage({ type: 'MESSAGES_PROCESSED', data: filtered });
      break;
      
    case 'DETECT_SCAN_INFO':
      const { messageList } = data;
      const hasAppType = messageList.some((m: any) => 
        typeof m.content === 'string' && 
        /\b(web|mobile|api|application)\b/i.test(m.content)
      );
      const hasUrl = messageList.some((m: any) => 
        typeof m.content === 'string' && 
        /https?:\/\/|\.com|\.org/i.test(m.content)
      );
      self.postMessage({ 
        type: 'SCAN_INFO_DETECTED', 
        data: { hasAppType, hasUrl } 
      });
      break;
  }
};