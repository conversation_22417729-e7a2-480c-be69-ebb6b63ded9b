import path from "path";

// Maximum file size (10MB in bytes)
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Allowed file types
export const ALLOWED_FILE_EXTENSIONS = [
  // PDF
  ".pdf",
  // Word documents
  ".doc",
  ".docx",
  // Text files (for testing/development)
  ".txt",
  // Other common formats
  ".json",
  ".csv",
  ".xml",
];

// Content type mapping
const CONTENT_TYPE_MAP: Record<string, string> = {
  ".pdf": "application/pdf",
  ".doc": "application/msword",
  ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ".txt": "text/plain",
  ".json": "application/json",
  ".csv": "text/csv",
  ".xml": "application/xml",
};

/**
 * Determine content type based on file extension
 */
export function getContentType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  return CONTENT_TYPE_MAP[ext] || "application/octet-stream";
}

/**
 * Validate file size
 */
export function validateFileSize(size: number): boolean {
  return size <= MAX_FILE_SIZE;
}

/**
 * Validate file extension
 */
export function validateFileExtension(filename: string): boolean {
  const ext = path.extname(filename).toLowerCase();
  return ALLOWED_FILE_EXTENSIONS.includes(ext);
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + " bytes";
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
  return (bytes / (1024 * 1024)).toFixed(1) + " MB";
}

/**
 * Sanitize filename to make it safe for storage
 * Removes special characters, spaces, etc.
 */
export function sanitizeFileName(filename: string): string {
  // Get the base name and extension
  const ext = path.extname(filename);
  const baseName = path.basename(filename, ext);

  // Sanitize the base name (remove special chars, spaces, etc.)
  const sanitizedBaseName = baseName
    .replace(/[^a-zA-Z0-9-_]/g, '_') // Replace special chars with underscore
    .replace(/_+/g, '_')            // Replace multiple underscores with a single one
    .toLowerCase()
    .trim();

  // Ensure we have at least some valid characters
  const finalBaseName = sanitizedBaseName || 'file';

  // Return sanitized name with original extension
  return `${finalBaseName}${ext.toLowerCase()}`;
}

/**
 * Generate a unique filename with timestamp prefix
 */
export function generateUniqueFileName(originalFilename: string): string {
  const sanitizedName = sanitizeFileName(originalFilename);
  return `${Date.now()}_${sanitizedName}`;
}

/**
 * Get human-readable error message for file upload errors
 */
export function getFileErrorMessage(error: any): string {
  // Network errors
  if (error.code === "ECONNRESET" || error.code === "ETIMEDOUT") {
    return "Network error while uploading file. Please try again.";
  }

  // Storage quota errors
  if (error.code === 403 || (error.message && error.message.includes("quota"))) {
    return "Storage quota exceeded. Please contact support.";
  }

  // Authentication errors
  if (error.code === 401 || error.code === "PERMISSION_DENIED") {
    return "You don't have permission to upload files. Please log in again.";
  }

  // Return original error message or a generic one
  return error.message || "An error occurred while uploading the file. Please try again.";
}
