/**
 * Utility functions for user management
 */

/**
 * Extract organization name from an email address
 * @param email User's email address
 * @returns Organization name (domain without TLD) or null if not found
 */
export function extractOrganizationFromEmail(email: string): string | null {
  if (!email || typeof email !== 'string') {
    return null;
  }

  try {
    // Split the email by @ to get the domain part
    const parts = email.split('@');
    if (parts.length !== 2) {
      return null;
    }

    const domain = parts[1];

    // Split the domain by dots and get the organization name (second-level domain)
    const domainParts = domain.split('.');
    if (domainParts.length < 2) {
      return null;
    }

    // Return the organization name (second-level domain) with first letter capitalized
    // For example, for "<EMAIL>", return "Company"
    const orgName = domainParts[0];

    // Capitalize the first letter
    return orgName.charAt(0).toUpperCase() + orgName.slice(1);
  } catch (error) {
    console.error('Error extracting organization from email:', error);
    return null;
  }
}
import { db } from './firebase-admin';

/**
 * Get all users with the 'admin' role
 * @returns A promise that resolves to an array of admin user emails
 */
export async function getAdminUserEmails(): Promise<string[]> {
  try {
    const adminUsersSnapshot = await db.collection('users').where('role', '==', 'admin').get();
    if (adminUsersSnapshot.empty) {
      console.log('No admin users found.');
      return [];
    }
    const adminEmails = adminUsersSnapshot.docs.map(doc => doc.data().email);
    return adminEmails;
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return [];
  }
}

/**
 * Get a user's email by their ID
 * @param uid The user's ID
 * @returns A promise that resolves to the user's email or null if not found
 */
export async function getUserEmailById(uid: string): Promise<string | null> {
  try {
    const userDoc = await db.collection('users').doc(uid).get();
    if (!userDoc.exists) {
      console.log(`No user found with ID: ${uid}`);
      return null;
    }
    return userDoc.data()?.email || null;
  } catch (error) {
    console.error(`Error fetching user with ID ${uid}:`, error);
    return null;
  }
}
