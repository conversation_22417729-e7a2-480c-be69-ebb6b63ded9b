// Define socket event types - this file is safe to import on both client and server
export enum SocketEvents {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',

  // Message events
  NEW_MESSAGE = 'new_message',
  MESSAGE_RESPONSE = 'message_response',
  MESSAGE_READ = 'message_read',

  // Room events - these should match the raw strings used in server.js
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',

  // Error events
  ERROR = 'error',

  // Scan events
  SCAN_CREATED = 'scan-created',
  SCAN_UPDATED = 'scan-updated',
  SCAN_DELETED = 'scan-deleted',

  // Ping/pong for connection health
  PING = 'ping',
  PONG = 'pong',
}
