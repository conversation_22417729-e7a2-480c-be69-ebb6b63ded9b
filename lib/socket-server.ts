// This file should only be imported on the server side
import type { NextApiRequest, NextApiResponse } from 'next';
import { Server as NetServer } from 'http';
// Only import socket.io on the server side
import { Server as SocketIOServer } from 'socket.io';

// Import the shared socket events
import { SocketEvents } from './socket-events';

// Extended NextApiResponse with socket server
export type NextApiResponseWithSocket = NextApiResponse & {
  socket: {
    server: NetServer & {
      io?: SocketIOServer;
    };
  };
};

// Socket API handler for Next.js
export default async function SocketHandler(_req: NextApiRequest, res: NextApiResponseWithSocket) {
  try {
    // Check if we're in a serverless environment (like Netlify)
    const isServerless = process.env.NETLIFY || process.env.VERCEL;

    // If we're in a serverless environment, we can't use Socket.io
    if (isServerless) {
      console.log('Running in serverless environment, Socket.io is disabled');
      return res.status(200).json({
        message: 'Socket server not available in serverless environment',
        isServerless: true,
        fallbackMode: 'polling'
      });
    }

    // Check if res.socket exists
    if (!res.socket) {
      console.warn('SocketHandler: res.socket is undefined');
      return res.status(200).json({ message: 'Socket server not available (missing socket)' });
    }

    // Check if res.socket.server exists
    if (!res.socket.server) {
      console.warn('SocketHandler: res.socket.server is undefined');
      return res.status(200).json({ message: 'Socket server not available (missing server)' });
    }

    // Only initialize if not already initialized
    if (!res.socket.server.io) {
      console.log('Initializing Socket.io server...');

      try {
        // Create a new Socket.io server with improved configuration for stability
        const io = new SocketIOServer(res.socket.server, {
          path: '/api/socket',
          addTrailingSlash: false,
          transports: ['websocket', 'polling'], // Support both WebSocket and polling
          cors: {
            origin: '*', // Allow all origins in development
            methods: ['GET', 'POST'],
            credentials: true
          },
          // Ping timeout settings - increased for better stability
          pingTimeout: 60000,        // 60 seconds (increased from 30s)
          pingInterval: 10000,       // 10 seconds (decreased from 25s for more frequent pings)
          // Connection timeout
          connectTimeout: 45000,     // 45 seconds (increased from 30s)
          // Allow upgrades
          allowUpgrades: true,
          // Increase buffer size for larger payloads
          maxHttpBufferSize: 1e8,    // 100 MB
          // Disable per-message deflate compression to reduce CPU usage
          perMessageDeflate: false,
          // Disable cluster mode for development
          adapter: undefined,
        });

        // Store the Socket.io server instance
        res.socket.server.io = io;

        // Also store in our global variable for easier access
        setGlobalSocketIO(io);

        // Set up event handlers with improved connection management
        io.on('connection', (socket) => {
          console.log(`Client connected: ${socket.id}`);

          // Track socket state
          let joinedRooms = new Set<string>();
          let userId: string | null = null;

          // Join rooms based on user ID for targeted updates
          socket.on(SocketEvents.JOIN_ROOM, (data) => {
            const { userId: newUserId, role } = data;

            // Store user info
            userId = newUserId;

            // Create room key
            const userRoomKey = `user-${userId}`;

            // Only join if not already joined
            if (!joinedRooms.has(userRoomKey)) {
              console.log(`User ${userId} joined their room`);
              socket.join(userRoomKey);
              joinedRooms.add(userRoomKey);

              // Send acknowledgment to client
              socket.emit(SocketEvents.ROOM_JOINED, { userId, success: true });
            }

            // If user is admin, join admin room
            if (role === 'admin' && !joinedRooms.has('admin-room')) {
              socket.join('admin-room');
              joinedRooms.add('admin-room');
            }

            // If user is manager, join team room
            if (role === 'manager') {
              const teamRoomKey = `team-${userId}`;
              if (!joinedRooms.has(teamRoomKey)) {
                socket.join(teamRoomKey);
                joinedRooms.add(teamRoomKey);
              }
            }
          });

          socket.on(SocketEvents.LEAVE_ROOM, (leaveUserId) => {
            const roomKey = `user-${leaveUserId}`;
            if (joinedRooms.has(roomKey)) {
              console.log(`User ${leaveUserId} left their room`);
              socket.leave(roomKey);
              joinedRooms.delete(roomKey);
            }
          });

          // Handle ping events to keep connection alive
          socket.on(SocketEvents.PING, () => {
            socket.emit(SocketEvents.PONG);
          });

          // Handle errors
          socket.on('error', (error) => {
            console.error(`Socket error for ${socket.id}:`, error);
          });

          socket.on(SocketEvents.DISCONNECT, (reason) => {
            console.log(`Client disconnected: ${socket.id}, reason: ${reason}`);

            // Clean up any resources if needed
            joinedRooms.clear();
          });
        });

        console.log('Socket.io server initialized successfully');
      } catch (error) {
        console.error('Error initializing Socket.io server:', error);
        return res.status(200).json({ message: 'Socket server initialization failed' });
      }
    }

    return res.status(200).json({ message: 'Socket server ready' });
  } catch (error) {
    console.error('Unhandled error in SocketHandler:', error);
    return res.status(200).json({ message: 'Socket server error', error: String(error) });
  }
}

// Get the socket.io instance from response
export const getSocketIO = (res: NextApiResponseWithSocket) => {
  try {
    // Check if res.socket exists
    if (!res.socket) {
      console.warn('getSocketIO: res.socket is undefined');
      return null;
    }

    // Check if res.socket.server exists
    if (!res.socket.server) {
      console.warn('getSocketIO: res.socket.server is undefined');
      return null;
    }

    // Return the io instance (which might be undefined)
    return res.socket.server.io;
  } catch (error) {
    console.error('Error in getSocketIO:', error);
    return null;
  }
};

// Global variable to store the socket.io server instance
let globalSocketIO: SocketIOServer | null = null;

// Set the global socket.io instance
export const setGlobalSocketIO = (io: SocketIOServer) => {
  globalSocketIO = io;
};

// Get the global socket.io instance
export const getGlobalSocketIO = () => {
  return globalSocketIO;
};

// These functions will emit WebSocket events
export const emitNewMessage = (io: SocketIOServer | null, userId: string, message: any) => {
  if (!io) {
    console.warn('emitNewMessage: Socket.io instance is null');
    return;
  }

  try {
    io.to(`user-${userId}`).emit(SocketEvents.NEW_MESSAGE, message);
  } catch (error) {
    console.error('Error in emitNewMessage:', error);
  }
};

export const emitMessageResponse = (io: SocketIOServer | null, userId: string, messageId: string, response: any) => {
  if (!io) {
    console.warn('emitMessageResponse: Socket.io instance is null');
    return;
  }

  try {
    io.to(`user-${userId}`).emit(SocketEvents.MESSAGE_RESPONSE, { messageId, response });
  } catch (error) {
    console.error('Error in emitMessageResponse:', error);
  }
};

export const emitMessageRead = (io: SocketIOServer | null, userId: string, messageId: string) => {
  if (!io) {
    console.warn('emitMessageRead: Socket.io instance is null');
    return;
  }

  try {
    io.to(`user-${userId}`).emit(SocketEvents.MESSAGE_READ, { messageId });
  } catch (error) {
    console.error('Error in emitMessageRead:', error);
  }
};

// New functions for scan updates
export const emitScanCreated = (io: SocketIOServer | null, scanData: any) => {
  // If io is null, do nothing
  if (!io) {
    console.warn('emitScanCreated: Socket.io instance is null');
    return;
  }

  try {
    const { userId } = scanData;

    // Emit to the specific user
    if (userId) {
      io.to(`user-${userId}`).emit(SocketEvents.SCAN_CREATED, scanData);
    }

    // If this is a team scan, also emit to the team room
    if (scanData.managerId) {
      io.to(`team-${scanData.managerId}`).emit(SocketEvents.SCAN_CREATED, scanData);
    }

    // For admin updates, broadcast to all connected admins
    io.to('admin-room').emit(SocketEvents.SCAN_CREATED, scanData);
  } catch (error) {
    console.error('Error in emitScanCreated:', error);
  }
};

export const emitScanUpdated = (io: SocketIOServer | null, scanData: any) => {
  // If io is null, do nothing
  if (!io) {
    console.warn('emitScanUpdated: Socket.io instance is null');
    return;
  }

  try {
    const { userId } = scanData;

    // Emit to the specific user
    if (userId) {
      io.to(`user-${userId}`).emit(SocketEvents.SCAN_UPDATED, scanData);
    }

    // If this is a team scan, also emit to the team room
    if (scanData.managerId) {
      io.to(`team-${scanData.managerId}`).emit(SocketEvents.SCAN_UPDATED, scanData);
    }

    // For admin updates, broadcast to all connected admins
    io.to('admin-room').emit(SocketEvents.SCAN_UPDATED, scanData);
  } catch (error) {
    console.error('Error in emitScanUpdated:', error);
  }
};

export const emitScanDeleted = (io: SocketIOServer | null, scanData: any) => {
  // If io is null, do nothing
  if (!io) {
    console.warn('emitScanDeleted: Socket.io instance is null');
    return;
  }

  try {
    const { userId } = scanData;

    // Emit to the specific user
    if (userId) {
      io.to(`user-${userId}`).emit(SocketEvents.SCAN_DELETED, scanData);
    }

    // If this is a team scan, also emit to the team room
    if (scanData.managerId) {
      io.to(`team-${scanData.managerId}`).emit(SocketEvents.SCAN_DELETED, scanData);
    }

    // For admin updates, broadcast to all connected admins
    io.to('admin-room').emit(SocketEvents.SCAN_DELETED, scanData);
  } catch (error) {
    console.error('Error in emitScanDeleted:', error);
  }
};

// Wrapper functions that use the global socket instance

export const emitNewMessageGlobal = (userId: string, message: any) => {
  const io = getGlobalSocketIO();
  emitNewMessage(io, userId, message);
};

export const emitMessageResponseGlobal = (userId: string, messageId: string, response: any) => {
  const io = getGlobalSocketIO();
  emitMessageResponse(io, userId, messageId, response);
};

export const emitMessageReadGlobal = (userId: string, messageId: string) => {
  const io = getGlobalSocketIO();
  emitMessageRead(io, userId, messageId);
};

export const emitScanCreatedGlobal = (scanData: any) => {
  const io = getGlobalSocketIO();
  emitScanCreated(io, scanData);
};

export const emitScanUpdatedGlobal = (scanData: any) => {
  const io = getGlobalSocketIO();
  emitScanUpdated(io, scanData);
};

export const emitScanDeletedGlobal = (scanData: any) => {
  const io = getGlobalSocketIO();
  emitScanDeleted(io, scanData);
};
