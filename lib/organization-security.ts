/**
 * Security utilities for organization-wide data access
 */

import { db } from "@/lib/firebase-admin";
import { getUserOrganization, isUserInOrganization } from "@/lib/organization-utils";

export interface AuditLogEntry {
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  organizationName?: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

/**
 * Log organization access for audit purposes
 */
export async function logOrganizationAccess(
  userId: string,
  userEmail: string,
  action: string,
  resource: string,
  resourceId?: string,
  organizationName?: string,
  success: boolean = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    // Create audit entry, filtering out undefined values
    const auditEntry: AuditLogEntry = {
      userId,
      userEmail,
      action,
      resource,
      timestamp: new Date().toISOString(),
      success
    };

    // Only add optional fields if they have values
    if (resourceId !== undefined) {
      auditEntry.resourceId = resourceId;
    }
    if (organizationName !== undefined) {
      auditEntry.organizationName = organizationName;
    }
    if (ipAddress !== undefined) {
      auditEntry.ipAddress = ipAddress;
    }
    if (userAgent !== undefined) {
      auditEntry.userAgent = userAgent;
    }
    if (errorMessage !== undefined) {
      auditEntry.errorMessage = errorMessage;
    }

    // Store audit log in Firestore
    await db.collection('audit_logs').add(auditEntry);
    
    console.log(`[AUDIT] ${action} on ${resource} by ${userEmail} - ${success ? 'SUCCESS' : 'FAILED'}`);
  } catch (error) {
    console.error('Error logging organization access:', error);
    // Don't throw error to avoid breaking the main functionality
  }
}

/**
 * Validate organization access for a user
 */
export async function validateOrganizationAccess(
  userId: string,
  userEmail: string,
  requestedOrganization?: string
): Promise<{ 
  isValid: boolean; 
  userOrganization: string | null; 
  errorMessage?: string 
}> {
  try {
    // Get user's organization
    const userOrganization = await getUserOrganization(userEmail);
    
    if (!userOrganization) {
      return {
        isValid: false,
        userOrganization: null,
        errorMessage: 'User organization could not be determined'
      };
    }
    
    // If a specific organization is requested, validate access
    if (requestedOrganization) {
      const hasAccess = await isUserInOrganization(userEmail, requestedOrganization);
      
      if (!hasAccess) {
        return {
          isValid: false,
          userOrganization,
          errorMessage: `User does not have access to organization: ${requestedOrganization}`
        };
      }
    }
    
    return {
      isValid: true,
      userOrganization
    };
  } catch (error) {
    console.error('Error validating organization access:', error);
    return {
      isValid: false,
      userOrganization: null,
      errorMessage: 'Error validating organization access'
    };
  }
}

/**
 * Check rate limiting for organization data access
 */
export async function checkOrganizationRateLimit(
  userId: string,
  action: string,
  windowMinutes: number = 60,
  maxRequests: number = 100
): Promise<{ 
  isAllowed: boolean; 
  remainingRequests: number; 
  resetTime: Date 
}> {
  try {
    const windowStart = new Date(Date.now() - (windowMinutes * 60 * 1000));
    const rateLimitKey = `${userId}:${action}`;
    
    // Query recent requests from audit logs
    const recentRequests = await db.collection('audit_logs')
      .where('userId', '==', userId)
      .where('action', '==', action)
      .where('timestamp', '>=', windowStart.toISOString())
      .get();
    
    const requestCount = recentRequests.size;
    const remainingRequests = Math.max(0, maxRequests - requestCount);
    const resetTime = new Date(Date.now() + (windowMinutes * 60 * 1000));
    
    return {
      isAllowed: requestCount < maxRequests,
      remainingRequests,
      resetTime
    };
  } catch (error) {
    console.error('Error checking rate limit:', error);
    // Allow request if rate limiting check fails
    return {
      isAllowed: true,
      remainingRequests: maxRequests,
      resetTime: new Date(Date.now() + (windowMinutes * 60 * 1000))
    };
  }
}

/**
 * Sanitize organization data before sending to client
 */
export function sanitizeOrganizationData(data: any): any {
  if (!data) return data;
  
  // Remove sensitive fields
  const sanitized = { ...data };
  
  // Remove internal IDs and sensitive information
  if (sanitized.members) {
    sanitized.members = sanitized.members.map((member: any) => ({
      userId: member.userId,
      email: member.email,
      displayName: member.displayName,
      joinedAt: member.joinedAt,
      // Remove sensitive fields like internal roles, permissions, etc.
    }));
  }
  
  // Remove any internal metadata
  delete sanitized._internal;
  delete sanitized.adminNotes;
  delete sanitized.internalId;
  
  return sanitized;
}

/**
 * Validate scan access within organization
 */
export async function validateScanAccess(
  userId: string,
  userEmail: string,
  scanId: string,
  organizationName: string
): Promise<{ isValid: boolean; errorMessage?: string }> {
  try {
    // Check if user belongs to the organization
    const orgAccess = await validateOrganizationAccess(userId, userEmail, organizationName);
    
    if (!orgAccess.isValid) {
      return {
        isValid: false,
        errorMessage: orgAccess.errorMessage
      };
    }
    
    // Get the scan document
    const scanDoc = await db.collection('scans').doc(scanId).get();
    
    if (!scanDoc.exists) {
      return {
        isValid: false,
        errorMessage: 'Scan not found'
      };
    }
    
    const scanData = scanDoc.data();
    
    // Check if scan belongs to someone in the same organization
    if (scanData?.userEmail) {
      const scanUserOrg = await getUserOrganization(scanData.userEmail);
      
      if (scanUserOrg !== organizationName) {
        return {
          isValid: false,
          errorMessage: 'Scan does not belong to your organization'
        };
      }
    }
    
    return { isValid: true };
  } catch (error) {
    console.error('Error validating scan access:', error);
    return {
      isValid: false,
      errorMessage: 'Error validating scan access'
    };
  }
}

/**
 * Get organization access summary for a user
 */
export async function getOrganizationAccessSummary(
  userId: string,
  userEmail: string
): Promise<{
  organization: string | null;
  accessLevel: 'none' | 'member' | 'admin';
  permissions: string[];
  lastAccess?: string;
}> {
  try {
    const userOrganization = await getUserOrganization(userEmail);
    
    if (!userOrganization) {
      return {
        organization: null,
        accessLevel: 'none',
        permissions: []
      };
    }
    
    // Get user's last organization access from audit logs
    const lastAccessQuery = await db.collection('audit_logs')
      .where('userId', '==', userId)
      .where('organizationName', '==', userOrganization)
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get();
    
    const lastAccess = lastAccessQuery.empty 
      ? undefined 
      : lastAccessQuery.docs[0].data().timestamp;
    
    // Basic permissions for organization members
    const permissions = [
      'view_organization_scans',
      'view_organization_vulnerabilities',
      'view_organization_members'
    ];
    
    return {
      organization: userOrganization,
      accessLevel: 'member',
      permissions,
      lastAccess
    };
  } catch (error) {
    console.error('Error getting organization access summary:', error);
    return {
      organization: null,
      accessLevel: 'none',
      permissions: []
    };
  }
}