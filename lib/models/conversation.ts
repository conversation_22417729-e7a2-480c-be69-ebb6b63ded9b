// Types for chat conversations

export interface TextContent {
  text: string;
}

export interface ScanCardContent {
  isScanCard: true;
  scanId: string;
  assetType: string;
  target: string;
  status: "pending" | "in-progress" | "completed" | "re-test";
  timestamp: string; // ISO string
  name?: string; // Add optional name property
}

export interface DownloadCardContent {
  isDownloadCard: true;
  reportUrl: string;
  assetType?: string;
  scanId?: string; // Add scanId to DownloadCardContent
}

export interface AiResponseContent {
  isAiResponse?: true;
  text: string;
  scanInfoFlags?: {
    appType: boolean;
    appName: boolean;
    appUrl: boolean;
  };
}

export interface FileUploadContent {
  isSystemFileUpload: true;
  fileName: string;
  fileSize: string; // Formatted string like "1.2 MB"
  status: "uploading" | "uploaded" | "failed";
  fileUrl?: string; // URL after successful upload
}

export interface SystemNotificationContent {
  isSystemNotification: true;
  text: string;
  severity: 'info' | 'warning' | 'error' | 'success';
}

export type MessageContent =
  | string
  | TextContent
  | ScanCardContent
  | DownloadCardContent
  | AiResponseContent
  | FileUploadContent
  | SystemNotificationContent;

export interface MessageType {
  id: string;
  role: "user" | "assistant" | "system";
  content: MessageContent;
  timestamp: string; // ISO string format
}

export interface FileInfoType {
  url: string;
  originalName: string;
  size: number;
  uploadedAt: string; // ISO string format
  contentType?: string;
}

export interface ConversationType {
  id: string;
  userId: string; // Firebase Auth user ID
  title: string;
  messages: MessageType[];
  fileUrls: string[];
  fileInfo: FileInfoType[];
  createdAt: string; // ISO string format
  updatedAt: string; // ISO string format
  lastMessagePreview?: string; // Preview of the last message for quick display
  associatedScanIds?: string[]; // IDs of scans created from this conversation
}

// Helper functions for conversion between Firestore and client models
export const toFirestore = (conversation: ConversationType): any => {
  return {
    ...conversation,
    // Ensure dates are stored as strings
    createdAt: conversation.createdAt,
    updatedAt: new Date().toISOString(),
    // Generate a preview of the last message
    lastMessagePreview: getLastMessagePreview(conversation.messages),
  };
};

export const fromFirestore = (data: any, id: string): ConversationType => {
  return {
    id,
    userId: data.userId,
    title: data.title,
    messages: data.messages,
    fileUrls: data.fileUrls || [],
    fileInfo: data.fileInfo || [],
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
    lastMessagePreview: data.lastMessagePreview,
  };
};

// Helper to get a preview of the last message
const getLastMessagePreview = (messages: MessageType[]): string => {
  if (messages.length === 0) return "";

  // Find the last user message for a more relevant preview
  const lastUserMessage = [...messages].reverse().find(m => m.role === "user");

  // If no user message found, use the last message
  if (!lastUserMessage) {
    const lastMessage = messages[messages.length - 1];
    const content = lastMessage.content;

    // Check if content is a string or an object
    if (typeof content === "string") {
      return content.length > 100 ? `${content.substring(0, 100)}...` : content;
    } else if ('isScanCard' in content && content.isScanCard) {
      return `[Scan Request: ${content.assetType || "Unknown"} - ${content.target || "Unknown target"}]`;
    } else if ('isDownloadCard' in content && content.isDownloadCard) {
      return `[Download: ${content.assetType || "Report"}]`;
    } else if ('isSystemFileUpload' in content && content.isSystemFileUpload) {
      return `[File Upload: ${content.fileName}]`;
    } else if ('isSystemNotification' in content && content.isSystemNotification) {
      return `[Notification: ${content.text}]`;
    } else if ('text' in content && typeof content.text === 'string') {
      return content.text.length > 100 ? `${content.text.substring(0, 100)}...` : content.text;
    }
    return "[Message contains structured data]";
  }


  // Handle user message content
  const content = lastUserMessage.content;

  // Check if content is a string or an object
  if (typeof content === "string") {
    return content.length > 100 ? `${content.substring(0, 100)}...` : content;
  } else if ('isScanCard' in content && content.isScanCard) {
    return `[Scan Request: ${content.assetType || "Unknown"} - ${content.target || "Unknown target"}]`;
  } else if ('isDownloadCard' in content && content.isDownloadCard) {
    return `[Download: ${content.assetType || "Report"}]`;
  } else if ('isSystemFileUpload' in content && content.isSystemFileUpload) {
    return `[File Upload: ${content.fileName}]`;
  } else if ('isSystemNotification' in content && content.isSystemNotification) {
    return `[Notification: ${content.text}]`;
  } else if ('text' in content && typeof content.text === 'string') {
    return content.text.length > 100 ? `${content.text.substring(0, 100)}...` : content.text;
  }
  return "[Message contains structured data]";
};
