/**
 * Utility functions for scan data processing and optimization
 */

import { UserInfo, ScanType } from "@/types/scan-types";

// For development: It's good practice to remove or conditionally enable extensive logging in production.
const DEV_MODE = process.env.NODE_ENV === 'development';

// Cache for organization grouping to avoid redundant processing
const organizationGroupingCache = new Map<string, any>();

/**
 * Process and group users by organization with caching
 * @param users Array of user information objects
 * @param cacheKey Unique key for caching the result
 * @returns Grouped organizations data
 */
export function processAndGroupByOrganization(users: UserInfo[], cacheKey: string) {
  // Check if we have a cached result
  if (organizationGroupingCache.has(cacheKey)) {
    return organizationGroupingCache.get(cacheKey);
  }

  // Process the data if not cached
  const organizationMap: Record<string, UserInfo[]> = {};

  // First pass: create organization groups
  users.forEach(user => {
    const org = user.organization || 'Unknown Organization';
    if (!organizationMap[org]) {
      organizationMap[org] = [];
    }
    organizationMap[org].push(user);
  });

  // Convert to array format
  const organizations = Object.keys(organizationMap).map(name => ({
    name,
    users: organizationMap[name]
  }));

  // Sort organizations alphabetically
  organizations.sort((a, b) => a.name.localeCompare(b.name));

  // Cache the result
  organizationGroupingCache.set(cacheKey, organizations);

  return organizations;
}

/**
 * Clear the organization grouping cache
 * @param cacheKey Optional specific cache key to clear, or clear all if not provided
 */
export function clearOrganizationCache(cacheKey?: string) {
  if (cacheKey) {
    organizationGroupingCache.delete(cacheKey);
  } else {
    organizationGroupingCache.clear();
  }
}

/**
 * Calculate status counts for a user's scans
 * @param scans Array of scans to count statuses from
 * @returns Object with counts for each status
 */
export function calculateStatusCounts(scans: ScanType[]) {
  return scans.reduce((counts, scan) => {
    // Count by status
    if (scan.status === 'pending') counts.pending++;
    else if (scan.status === 'in-progress') counts.inProgress++;
    else if (scan.status === 're-test') counts.reTest++;
    else if (scan.status === 'completed') counts.completed++;

    // Count critical vulnerabilities
    if (scan.criticalVulnerabilities > 0) {
      counts.critical++;
    }

    return counts;
  }, {
    pending: 0,
    inProgress: 0,
    reTest: 0,
    completed: 0,
    critical: 0
  });
}

/**
 * Debounce function to prevent excessive API calls
 * @param func Function to debounce
 * @param wait Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
