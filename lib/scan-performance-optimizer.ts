/**
 * Performance optimization utilities for scan data fetching and caching
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  etag: string;
}

interface OptimizedCacheConfig {
  personalScansTTL: number;
  organizationScansTTL: number;
  userDataTTL: number;
  maxCacheSize: number;
}

class ScanPerformanceOptimizer {
  private cache = new Map<string, CacheEntry<any>>();
  private config: OptimizedCacheConfig = {
    personalScansTTL: 5 * 60 * 1000, // 5 minutes
    organizationScansTTL: 10 * 60 * 1000, // 10 minutes
    userDataTTL: 30 * 60 * 1000, // 30 minutes
    maxCacheSize: 100
  };

  /**
   * Enhanced caching with longer TTL and better invalidation
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.config.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      etag: this.generateETag(data)
    };

    this.cache.set(key, entry);
  }

  get<T>(key: string, maxAge?: number): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const age = Date.now() - entry.timestamp;
    const ttl = maxAge || this.getTTLForKey(key);

    if (age > ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Optimized organization data fetching with aggressive caching
   */
  async getOrganizationScansOptimized(
    userId: string,
    userEmail: string,
    forceRefresh = false
  ): Promise<any> {
    const cacheKey = `org-scans:${userId}`;
    
    if (!forceRefresh) {
      const cached = this.get(cacheKey, this.config.organizationScansTTL);
      if (cached) {
        console.log('Using cached organization scans for instant switch');
        return cached;
      }
    }

    // Implement optimized fetching logic here
    // This would replace the heavy database queries in the API
    return null;
  }

  /**
   * Batch user data fetching with intelligent caching
   */
  async batchFetchUserData(userIds: string[]): Promise<Map<string, any>> {
    const userDataMap = new Map();
    const uncachedUserIds: string[] = [];

    // Check cache first
    for (const userId of userIds) {
      const cacheKey = `user-data:${userId}`;
      const cached = this.get(cacheKey, this.config.userDataTTL);
      if (cached) {
        userDataMap.set(userId, cached);
      } else {
        uncachedUserIds.push(userId);
      }
    }

    // Fetch uncached data in optimized batches
    if (uncachedUserIds.length > 0) {
      // Implementation would go here
      console.log(`Fetching ${uncachedUserIds.length} uncached user records`);
    }

    return userDataMap;
  }

  /**
   * Preload data for faster view switching
   */
  async preloadViewData(userId: string, userRole: string): Promise<void> {
    const promises: Promise<any>[] = [];

    // Preload personal scans if not cached
    const personalKey = `personal-scans:${userId}`;
    if (!this.get(personalKey)) {
      promises.push(this.preloadPersonalScans(userId));
    }

    // Preload organization scans if user has access
    if (userRole === 'client' || userRole === 'admin') {
      const orgKey = `org-scans:${userId}`;
      if (!this.get(orgKey)) {
        promises.push(this.preloadOrganizationScans(userId));
      }
    }

    await Promise.all(promises);
  }

  private async preloadPersonalScans(userId: string): Promise<void> {
    // Implementation for background preloading
  }

  private async preloadOrganizationScans(userId: string): Promise<void> {
    // Implementation for background preloading
  }

  private getTTLForKey(key: string): number {
    if (key.includes('personal-scans')) return this.config.personalScansTTL;
    if (key.includes('org-scans')) return this.config.organizationScansTTL;
    if (key.includes('user-data')) return this.config.userDataTTL;
    return 5 * 60 * 1000; // Default 5 minutes
  }

  private generateETag(data: any): string {
    return `"${Date.now()}-${JSON.stringify(data).length}"`;
  }

  /**
   * Clear cache entries by pattern
   */
  clearByPattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): { size: number; hitRate: number; keys: string[] } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses
      keys: Array.from(this.cache.keys())
    };
  }
}

export const scanOptimizer = new ScanPerformanceOptimizer();

/**
 * Optimized query builder for different user roles
 */
export class OptimizedQueryBuilder {
  static buildPersonalScansQuery(userId: string) {
    return {
      collection: 'scans',
      where: [['userId', '==', userId]],
      orderBy: [['requestedAt', 'desc']],
      limit: 50 // Limit initial load
    };
  }

  static buildOrganizationScansQuery(organizationDomain: string) {
    return {
      collection: 'scans',
      where: [['userEmail', 'array-contains-any', [organizationDomain]]],
      orderBy: [['requestedAt', 'desc']],
      limit: 100 // Higher limit for org view
    };
  }

  static buildTeamScansQuery(teamMemberIds: string[]) {
    // Split into batches of 10 for Firestore 'in' query limit
    const batches = [];
    for (let i = 0; i < teamMemberIds.length; i += 10) {
      batches.push({
        collection: 'scans',
        where: [['userId', 'in', teamMemberIds.slice(i, i + 10)]],
        orderBy: [['requestedAt', 'desc']]
      });
    }
    return batches;
  }
}

/**
 * Background data synchronization
 */
export class BackgroundSync {
  private syncInterval: NodeJS.Timeout | null = null;

  startSync(userId: string, userRole: string): void {
    if (this.syncInterval) return;

    this.syncInterval = setInterval(async () => {
      try {
        await scanOptimizer.preloadViewData(userId, userRole);
      } catch (error) {
        console.error('Background sync error:', error);
      }
    }, 2 * 60 * 1000); // Sync every 2 minutes
  }

  stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}

export const backgroundSync = new BackgroundSync();