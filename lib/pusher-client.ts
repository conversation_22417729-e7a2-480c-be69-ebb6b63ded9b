'use client';

import Pusher from 'pusher-js';

// Singleton Pusher instance
let pusherInstance: Pusher | null = null;

// Initialize Pusher connection
export const initPusherConnection = (): Pusher => {
  if (pusherInstance) {
    return pusherInstance;
  }

  pusherInstance = new Pusher(process.env.NEXT_PUBLIC_PUSHER_KEY!, {
    cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
    forceTLS: true
  });

  return pusherInstance;
};

// Get the Pusher instance
export const getPusher = (): Pusher | null => {
  return pusherInstance;
};

// Send message via Netlify function
export const sendPusherMessage = async (channel: string, event: string, data: any) => {
  try {
    const response = await fetch('/.netlify/functions/pusher-message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel,
        event,
        data
      })
    });

    if (!response.ok) {
      throw new Error('Failed to send message');
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending Pusher message:', error);
    throw error;
  }
};

// Cleanup Pusher connection
export const cleanupPusherConnection = () => {
  if (pusherInstance) {
    pusherInstance.disconnect();
    pusherInstance = null;
  }
};