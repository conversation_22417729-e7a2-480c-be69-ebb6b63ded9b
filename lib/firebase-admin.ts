import { initializeApp, getApps, cert, App } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import { getStorage } from "firebase-admin/storage";
import { getAuth } from "firebase-admin/auth";

// Determine which environment we're in
const isDevelopment = process.env.NODE_ENV === 'development';
console.log(`Firebase Admin initializing in ${isDevelopment ? 'development' : 'production'} mode`);

// Initialize Firebase Admin only once
let adminApp: App;

if (!getApps().length) {
  try {
    // Check if we have all required environment variables
    if (!process.env.FIREBASE_PROJECT_ID ||
        !process.env.FIREBASE_CLIENT_EMAIL ||
        !process.env.FIREBASE_PRIVATE_KEY) {
      console.error("Missing required Firebase environment variables");

      // In development, provide more detailed error information
      if (isDevelopment) {
        console.error("Environment variables status:");
        console.error(`- FIREBASE_PROJECT_ID: ${process.env.FIREBASE_PROJECT_ID ? "Set" : "Missing"}`);
        console.error(`- FIREBASE_CLIENT_EMAIL: ${process.env.FIREBASE_CLIENT_EMAIL ? "Set" : "Missing"}`);
        console.error(`- FIREBASE_PRIVATE_KEY: ${process.env.FIREBASE_PRIVATE_KEY ? "Set" : "Missing"}`);
        console.error(`- FIREBASE_STORAGE_BUCKET: ${process.env.FIREBASE_STORAGE_BUCKET ? "Set" : "Missing"}`);
      }

      throw new Error("Firebase Admin initialization failed: Missing required environment variables");
    }

    // Initialize with environment variables
    adminApp = initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
      }),
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
    });

    console.log("Firebase Admin initialized successfully");
  } catch (error) {
    console.error("Error initializing Firebase Admin:", error);

    // In development, we'll create a mock app to prevent crashes
    if (isDevelopment) {
      console.warn("Creating a mock Firebase Admin app for development");
      // @ts-ignore - This is a mock implementation for development only
      adminApp = { isDevMock: true };
    } else {
      throw error; // In production, we want to fail fast
    }
  }
} else {
  adminApp = getApps()[0];
  console.log("Using existing Firebase Admin app instance");
}

// Export initialized services
export const db = getFirestore(adminApp);
export const storage = getStorage(adminApp);
export const auth = getAuth(adminApp);

// Helper function to get storage bucket
export const getBucket = () => {
  const bucketName = process.env.FIREBASE_STORAGE_BUCKET?.replace("gs://", "");
  if (!bucketName) {
    throw new Error("FIREBASE_STORAGE_BUCKET environment variable is not set or invalid");
  }
  return storage.bucket(bucketName);
};

export default adminApp;
