import { db } from "@/lib/firebase-admin";
import { ConversationType, MessageType, toFirestore, fromFirestore } from "@/lib/models/conversation";
import { slugify } from "@/lib/utils";

const COLLECTION_NAME = "conversations";
const PAGE_SIZE = 10;

export class ConversationService {
  /**
   * Get a conversation by ID
   */
  static async getConversation(conversationId: string, userId: string): Promise<ConversationType | null> {
    try {
      const docRef = db.collection(COLLECTION_NAME).doc(conversationId);
      const doc = await docRef.get();

      if (!doc.exists) return null;

      const data = doc.data();
      // Security check - ensure user can only access their own conversations
      if (data?.userId !== userId) return null;

      return fromFirestore(data, doc.id);
    } catch (error) {
      console.error("Error getting conversation:", error);
      return null;
    }
  }

  /**
   * Get all conversations for a user with pagination
   */
  static async getUserConversations(
    userId: string,
    page = 1,
    pageSize = PAGE_SIZE
  ): Promise<{ conversations: ConversationType[]; hasMore: boolean }> {
    try {
      const query = db.collection(COLLECTION_NAME)
        .where("userId", "==", userId)
        .orderBy("updatedAt", "desc")
        .limit(pageSize + 1); // Get one extra to check if there are more

      // Apply pagination
      if (page > 1) {
        const lastDoc = await this.getLastDocOfPage(userId, page - 1, pageSize);
        if (lastDoc) {
          query.startAfter(lastDoc);
        }
      }

      const snapshot = await query.get();

      // Check if there are more results
      const hasMore = snapshot.docs.length > pageSize;
      const docs = hasMore ? snapshot.docs.slice(0, pageSize) : snapshot.docs;

      const conversations = docs.map(doc => fromFirestore(doc.data(), doc.id));

      return { conversations, hasMore };
    } catch (error) {
      console.error("Error getting user conversations:", error);
      return { conversations: [], hasMore: false };
    }
  }

  /**
   * Helper to get the last document of a page for pagination
   */
  private static async getLastDocOfPage(
    userId: string,
    page: number,
    pageSize: number
  ) {
    if (page < 1) return null;

    try {
      const snapshot = await db.collection(COLLECTION_NAME)
        .where("userId", "==", userId)
        .orderBy("updatedAt", "desc")
        .limit(page * pageSize)
        .get();

      if (snapshot.empty || snapshot.docs.length < page * pageSize) {
        return null;
      }

      return snapshot.docs[snapshot.docs.length - 1];
    } catch (error) {
      console.error("Error getting last doc of page:", error);
      return null;
    }
  }

  /**
   * Create a new conversation
   */
  static async createConversation(conversation: Omit<ConversationType, "id">): Promise<ConversationType | null> {
    try {
      const timestamp = new Date().getTime();
      const slug = slugify(conversation.title);
      const conversationId = `${slug}-${conversation.userId}-${timestamp}`;

      await db.collection(COLLECTION_NAME).doc(conversationId).set(toFirestore({
        ...conversation,
        id: conversationId,
      }));

      const newConversation = await this.getConversation(conversationId, conversation.userId);
      return newConversation;
    } catch (error) {
      console.error("Error creating conversation:", error);
      return null;
    }
  }

  /**
   * Update an existing conversation
   */
  static async updateConversation(
    conversationId: string,
    userId: string,
    updates: Partial<ConversationType>
  ): Promise<boolean> {
    try {
      // First check if the conversation exists and belongs to the user
      const conversation = await this.getConversation(conversationId, userId);
      if (!conversation) return false;

      // Apply updates
      const updatedConversation = {
        ...conversation,
        ...updates,
      };

      await db.collection(COLLECTION_NAME)
        .doc(conversationId)
        .update(toFirestore(updatedConversation));

      return true;
    } catch (error) {
      console.error("Error updating conversation:", error);
      return false;
    }
  }

  /**
   * Add a message to a conversation
   */
  static async addMessage(
    conversationId: string,
    userId: string,
    message: MessageType
  ): Promise<boolean> {
    try {
      // First check if the conversation exists and belongs to the user
      const conversation = await this.getConversation(conversationId, userId);
      if (!conversation) return false;

      // Add the message to the messages array
      const updatedMessages = [...conversation.messages, message];

      await db.collection(COLLECTION_NAME)
        .doc(conversationId)
        .update({
          messages: updatedMessages,
          updatedAt: new Date().toISOString(),
          lastMessagePreview: message.role === "user"
            ? (typeof message.content === 'string' && message.content.length > 100 ? `${message.content.substring(0, 100)}...` : message.content)
            : conversation.lastMessagePreview,
        });

      return true;
    } catch (error) {
      console.error("Error adding message to conversation:", error);
      return false;
    }
  }



  /**
   * Delete a conversation
   */
  static async deleteConversation(conversationId: string, userId: string): Promise<boolean> {
    try {
      // First check if the conversation exists and belongs to the user
      const conversation = await this.getConversation(conversationId, userId);
      if (!conversation) return false;

      await db.collection(COLLECTION_NAME).doc(conversationId).delete();
      return true;
    } catch (error) {
      console.error("Error deleting conversation:", error);
      return false;
    }
  }
}
