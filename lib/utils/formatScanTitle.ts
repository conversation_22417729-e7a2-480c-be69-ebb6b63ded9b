import { formatAssetType } from "./formatAssetType";

/**
 * Formats a scan title from asset_type or other scan properties
 * Ensures consistent capitalization and formatting across the application
 */
export function formatScanTitle(scan: {
  name?: string;
  scope?: string;
  asset_type?: string;
  conversationTitle?: string;
  title?: string;
  application_name?: string;
  asset_name?: string;
  [key: string]: any;
}): string {
  // Prioritize conversationTitle if it's not a generic default
  if (scan.conversationTitle &&
      scan.conversationTitle !== "Pentest Scan Request" &&
      scan.conversationTitle !== "Pentest Request" &&
      scan.conversationTitle !== "Security Scan Request") {
    return scan.conversationTitle;
  }

  // Prioritize application_name or asset_name if available from backend (actual scan name)
  if (scan.application_name) return scan.application_name;
  if (scan.asset_name) return scan.asset_name;

  // If scan has an explicit name or scope, use that
  if (scan.name) return scan.name;
  if (scan.scope) return scan.scope;

  // If scan has a target that's not a URL, use that
  if (scan.target &&
      typeof scan.target === 'string' &&
      !scan.target.includes('://') &&
      !scan.target.includes('.com') &&
      !scan.target.includes('.org') &&
      !scan.target.includes('.net')) {
    return `${scan.target} Scan`;
  }

  // If scan has an asset_type, format it properly
  if (scan.asset_type) {
    return `${formatAssetType(scan.asset_type)} Scan`;
  }

  // Fallback to a default title
  return "Pentest Request";
}
