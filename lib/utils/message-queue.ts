"use client"

import { v4 as uuidv4 } from "uuid";

// Message status types
export type MessageStatus = 'pending' | 'sending' | 'sent' | 'failed';

// Message priority levels
export type MessagePriority = 'high' | 'normal' | 'low';

// Message in the queue
export interface QueuedMessage {
  id: string;
  conversationId: string;
  content: any; // The actual message content
  type: 'user' | 'assistant' | 'system' | 'file';
  status: MessageStatus;
  priority: MessagePriority;
  timestamp: Date;
  retries: number;
  maxRetries: number;
}

// Configuration for the queue
interface MessageQueueConfig {
  batchSize: number;
  maxRetries: number;
  retryDelay: number; // in milliseconds
  processingInterval: number; // in milliseconds
}

// Default configuration
const DEFAULT_CONFIG: MessageQueueConfig = {
  batchSize: 3,
  maxRetries: 3,
  retryDelay: 2000, // 2 seconds
  processingInterval: 1000, // 1 second
};

export class MessageQueue {
  private queue: QueuedMessage[] = [];
  private processing: boolean = false;
  private config: MessageQueueConfig;
  private processorInterval: NodeJS.Timeout | null = null;
  private processorCallback: ((messages: QueuedMessage[]) => Promise<void>) | null = null;
  private statusChangeCallback: ((message: QueuedMessage) => void) | null = null;

  constructor(config: Partial<MessageQueueConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Set the processor callback that will handle sending messages
  public setProcessor(callback: (messages: QueuedMessage[]) => Promise<void>): void {
    this.processorCallback = callback;
  }

  // Set callback for status changes
  public onStatusChange(callback: (message: QueuedMessage) => void): void {
    this.statusChangeCallback = callback;
  }

  // Add a message to the queue
  public enqueue(
    conversationId: string,
    content: any,
    type: 'user' | 'assistant' | 'system' | 'file',
    priority: MessagePriority = 'normal'
  ): string {
    const id = uuidv4();
    const message: QueuedMessage = {
      id,
      conversationId,
      content,
      type,
      status: 'pending',
      priority,
      timestamp: new Date(),
      retries: 0,
      maxRetries: this.config.maxRetries,
    };

    // Add to queue based on priority
    if (priority === 'high') {
      this.queue.unshift(message); // Add to the beginning
    } else {
      this.queue.push(message); // Add to the end
    }

    // Start processing if not already running
    this.startProcessing();

    // Notify about the new message
    this.notifyStatusChange(message);

    return id;
  }

  // Start the queue processor
  private startProcessing(): void {
    if (this.processing || this.processorInterval) {
      return;
    }

    this.processing = true;
    this.processorInterval = setInterval(() => {
      this.processQueue();
    }, this.config.processingInterval);
  }

  // Stop the queue processor
  public stopProcessing(): void {
    if (this.processorInterval) {
      clearInterval(this.processorInterval);
      this.processorInterval = null;
    }
    this.processing = false;
  }

  // Process the queue
  private async processQueue(): Promise<void> {
    if (!this.processorCallback || this.queue.length === 0) {
      return;
    }

    // Sort the queue by priority
    this.queue.sort((a, b) => {
      const priorityOrder = { high: 0, normal: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });

    // Get messages that are pending or failed but ready for retry
    const now = new Date();
    const eligibleMessages = this.queue.filter(
      (msg) =>
        msg.status === 'pending' ||
        (msg.status === 'failed' &&
          msg.retries < msg.maxRetries &&
          now.getTime() - msg.timestamp.getTime() > this.config.retryDelay)
    );

    // Take a batch of messages
    const batch = eligibleMessages.slice(0, this.config.batchSize);
    if (batch.length === 0) {
      return;
    }

    // Mark messages as sending
    batch.forEach((msg) => {
      msg.status = 'sending';
      this.notifyStatusChange(msg);
    });

    try {
      // Process the batch
      await this.processorCallback(batch);

      // Mark messages as sent and remove from queue
      batch.forEach((msg) => {
        msg.status = 'sent';
        this.notifyStatusChange(msg);
        this.removeMessage(msg.id);
      });
    } catch (error) {
      console.error('Error processing message batch:', error);

      // Mark messages as failed
      batch.forEach((msg) => {
        msg.status = 'failed';
        msg.retries += 1;
        msg.timestamp = new Date(); // Reset timestamp for retry delay
        this.notifyStatusChange(msg);
      });
    }
  }

  // Remove a message from the queue
  private removeMessage(id: string): void {
    this.queue = this.queue.filter((msg) => msg.id !== id);
    
    // Stop processing if queue is empty
    if (this.queue.length === 0) {
      this.stopProcessing();
    }
  }

  // Notify about status changes
  private notifyStatusChange(message: QueuedMessage): void {
    if (this.statusChangeCallback) {
      this.statusChangeCallback(message);
    }
  }

  // Get all messages for a conversation
  public getConversationMessages(conversationId: string): QueuedMessage[] {
    return this.queue.filter((msg) => msg.conversationId === conversationId);
  }

  // Get a specific message by ID
  public getMessage(id: string): QueuedMessage | undefined {
    return this.queue.find((msg) => msg.id === id);
  }

  // Clear all messages for a conversation
  public clearConversation(conversationId: string): void {
    this.queue = this.queue.filter((msg) => msg.conversationId !== conversationId);
  }

  // Clear the entire queue
  public clear(): void {
    this.queue = [];
    this.stopProcessing();
  }
}

// Create a singleton instance
export const messageQueue = new MessageQueue();
