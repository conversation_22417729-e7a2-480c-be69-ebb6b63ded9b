/**
 * Formats an asset type string by removing underscores and applying Title Case
 * Ensures consistent formatting across the application
 */
export function formatAssetType(assetType: string | undefined | null): string {
  if (!assetType) return "Not specified";
  
  // Replace underscores with spaces
  const assetTypeWithSpaces = assetType.replace(/_/g, ' ');
  
  // Capitalize each word (Title Case)
  return assetTypeWithSpaces
    .split(' ')
    .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
