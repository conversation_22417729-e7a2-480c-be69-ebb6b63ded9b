const Pusher = require('pusher');

const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true
});

exports.handler = async (event, context) => {
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const { channel, event: eventName, data } = JSON.parse(event.body);

    if (!channel || !eventName || !data) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing required fields: channel, event, data' })
      };
    }

    // Trigger the event
    await pusher.trigger(channel, eventName, data);

    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, message: 'Event triggered successfully' })
    };
  } catch (error) {
    console.error('Pusher error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to trigger event' })
    };
  }
};