"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useScans } from './ScanContext'; // Import useScans hook

interface ScanCountContextType {
    totalScans: number;
}

const ScanCountContext = createContext<ScanCountContextType | undefined>(undefined);

export const ScanCountProvider = ({ children }: { children: ReactNode }) => {
    const { scans } = useScans();
    const totalScans = scans.length;

    return (
        <ScanCountContext.Provider value={{ totalScans }}>
            {children}
        </ScanCountContext.Provider>
    );
};

export const useScanCount = () => {
    const context = useContext(ScanCountContext);
    if (context === undefined) {
        throw new Error('useScanCount must be used within a ScanCountProvider');
    }
    return context;
};
