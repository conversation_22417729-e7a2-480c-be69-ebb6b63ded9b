"use client";

import React, { createContext, useContext, useState, useEffect, useRef, ReactNode, useReducer, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { usePusher } from '@/hooks/usePusher';

// Define the TeamMessage type
export interface TeamMessage {
  id: string;
  userId: string;
  userEmail: string;
  message: string;
  timestamp: string;
  status: "read" | "unread";
  response?: string;
  responseTimestamp?: string;
  isReplyTo?: string; // Reference to the original message this is a reply to
}

// Define state for reducer
interface MessagesState {
  messages: TeamMessage[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  clientMessages: TeamMessage[];
  clientMessagesLoading: boolean;
  lastUpdated: Date | null;
}

// Define actions for reducer
type MessagesAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CLIENT_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_MESSAGES'; payload: TeamMessage[] }
  | { type: 'SET_CLIENT_MESSAGES'; payload: TeamMessage[] }
  | { type: 'ADD_MESSAGE'; payload: TeamMessage }
  | { type: 'ADD_CLIENT_MESSAGE'; payload: TeamMessage }
  | { type: 'UPDATE_MESSAGE'; payload: { messageId: string; updates: Partial<TeamMessage> } }
  | { type: 'DELETE_MESSAGE'; payload: string }
  | { type: 'SET_LAST_UPDATED'; payload: Date }
  | { type: 'RESET_STATE' };

// Reducer for atomic state updates
const messagesReducer = (state: MessagesState, action: MessagesAction): MessagesState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_CLIENT_LOADING':
      return { ...state, clientMessagesLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_MESSAGES':
      const unreadCount = action.payload.filter(msg => msg.status === 'unread').length;
      return { 
        ...state, 
        messages: action.payload, 
        unreadCount,
        loading: false,
        error: null,
        lastUpdated: new Date()
      };
    
    case 'SET_CLIENT_MESSAGES':
      return { 
        ...state, 
        clientMessages: action.payload,
        clientMessagesLoading: false,
        error: null,
        lastUpdated: new Date()
      };
    
    case 'ADD_MESSAGE':
      // Check if message already exists to prevent duplicates
      const messageExists = state.messages.some(msg => msg.id === action.payload.id);
      if (messageExists) return state;
      
      return {
        ...state,
        messages: [action.payload, ...state.messages],
        unreadCount: state.unreadCount + (action.payload.status === 'unread' ? 1 : 0),
        lastUpdated: new Date()
      };
    
    case 'ADD_CLIENT_MESSAGE':
      // Check if message already exists to prevent duplicates
      const clientMessageExists = state.clientMessages.some(msg => msg.id === action.payload.id);
      if (clientMessageExists) return state;
      
      return {
        ...state,
        clientMessages: [action.payload, ...state.clientMessages],
        lastUpdated: new Date()
      };
    
    case 'UPDATE_MESSAGE':
      const updatedMessages = state.messages.map(msg =>
        msg.id === action.payload.messageId 
          ? { ...msg, ...action.payload.updates }
          : msg
      );
      
      const updatedClientMessages = state.clientMessages.map(msg =>
        msg.id === action.payload.messageId 
          ? { ...msg, ...action.payload.updates }
          : msg
      );
      
      // Recalculate unread count if status was updated
      const newUnreadCount = action.payload.updates.status === 'read'
        ? Math.max(0, state.unreadCount - 1)
        : state.unreadCount;
      
      return {
        ...state,
        messages: updatedMessages,
        clientMessages: updatedClientMessages,
        unreadCount: newUnreadCount,
        lastUpdated: new Date()
      };
    
    case 'DELETE_MESSAGE':
      const messageToDelete = state.messages.find(msg => msg.id === action.payload);
      const filteredMessages = state.messages.filter(msg => msg.id !== action.payload);
      
      return {
        ...state,
        messages: filteredMessages,
        unreadCount: messageToDelete?.status === 'unread' 
          ? Math.max(0, state.unreadCount - 1) 
          : state.unreadCount,
        lastUpdated: new Date()
      };
    
    case 'SET_LAST_UPDATED':
      return { ...state, lastUpdated: action.payload };
    
    case 'RESET_STATE':
      return {
        messages: [],
        unreadCount: 0,
        loading: false,
        error: null,
        clientMessages: [],
        clientMessagesLoading: false,
        lastUpdated: null
      };
    
    default:
      return state;
  }
};

// Initial state
const initialState: MessagesState = {
  messages: [],
  unreadCount: 0,
  loading: true,
  error: null,
  clientMessages: [],
  clientMessagesLoading: true,
  lastUpdated: null
};

// Define the context type
interface TeamMessagesContextType {
  messages: TeamMessage[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchMessages: () => Promise<void>;
  markAsRead: (messageId: string) => Promise<void>;
  respondToMessage: (messageId: string, response: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  clientMessages: TeamMessage[];
  clientMessagesLoading: boolean;
  fetchClientMessages: () => Promise<void>;
  lastUpdated: Date | null;
  isConnected: boolean;
  isServerless: boolean;
}

// Create the context
const TeamMessagesContext = createContext<TeamMessagesContextType | undefined>(undefined);

// Create the provider component
export const TeamMessagesProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(messagesReducer, initialState);
  const { user, role, loading: authLoading } = useAuth();
  const { subscribe, isConnected, isServerless } = usePusher();

  // Set up polling for serverless environments
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Function to fetch messages with atomic updates
  const fetchMessages = useCallback(async () => {
    if (!user || role !== 'admin') {
      dispatch({ type: 'SET_MESSAGES', payload: [] });
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const idToken = await user.getIdToken();
      const response = await fetch('/api/team-messages', {
        headers: {
          'Authorization': `Bearer ${idToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status}`);
      }

      const data = await response.json();
      dispatch({ type: 'SET_MESSAGES', payload: data.messages || [] });
    } catch (err: any) {
      console.error('Error fetching team messages:', err);
      dispatch({ type: 'SET_ERROR', payload: err.message });
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [user, role]);

  // Function to mark a message as read with atomic updates
  const markAsRead = useCallback(async (messageId: string) => {
    if (!user || role !== 'admin') return;

    try {
      const idToken = await user.getIdToken();
      const response = await fetch('/api/team-messages', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          messageId,
          status: 'read',
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update message: ${response.status}`);
      }

      // Update local state atomically
      dispatch({ 
        type: 'UPDATE_MESSAGE', 
        payload: { messageId, updates: { status: 'read' } } 
      });
    } catch (err: any) {
      console.error('Error marking message as read:', err);
      dispatch({ type: 'SET_ERROR', payload: err.message });
    }
  }, [user, role]);

  // Function to respond to a message with atomic updates
  const respondToMessage = useCallback(async (messageId: string, response: string) => {
    if (!user || role !== 'admin' || !response.trim()) return;

    try {
      const idToken = await user.getIdToken();
      const apiResponse = await fetch('/api/team-messages', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          messageId,
          status: 'read',
          response,
        }),
      });

      if (!apiResponse.ok) {
        throw new Error(`Failed to respond to message: ${apiResponse.status}`);
      }

      // Update local state atomically
      const now = new Date().toISOString();
      dispatch({ 
        type: 'UPDATE_MESSAGE', 
        payload: { 
          messageId, 
          updates: { status: 'read', response, responseTimestamp: now } 
        } 
      });
    } catch (err: any) {
      console.error('Error responding to message:', err);
      dispatch({ type: 'SET_ERROR', payload: err.message });
    }
  }, [user, role]);

  // Function to delete a message with atomic updates
  const deleteMessage = useCallback(async (messageId: string) => {
    if (!user || role !== 'admin') return;

    try {
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/team-messages?messageId=${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${idToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete message: ${response.status}`);
      }

      // Update local state atomically
      dispatch({ type: 'DELETE_MESSAGE', payload: messageId });
    } catch (err: any) {
      console.error('Error deleting message:', err);
      dispatch({ type: 'SET_ERROR', payload: err.message });
      throw err; // Re-throw to allow the component to handle the error
    }
  }, [user, role]);

  // Function to fetch client messages with atomic updates
  const fetchClientMessages = useCallback(async () => {
    if (!user) {
      dispatch({ type: 'SET_CLIENT_MESSAGES', payload: [] });
      return;
    }

    // Don't set loading to true if we already have messages
    // This prevents the UI from flashing while refreshing
    if (state.clientMessages.length === 0) {
      dispatch({ type: 'SET_CLIENT_LOADING', payload: true });
    }

    try {
      const idToken = await user.getIdToken();
      const response = await fetch('/api/client-messages', {
        headers: {
          'Authorization': `Bearer ${idToken}`,
          'Cache-Control': 'no-cache', // Ensure we get fresh data
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch client messages: ${response.status}`);
      }

      const data = await response.json();
      dispatch({ type: 'SET_CLIENT_MESSAGES', payload: data.messages || [] });
    } catch (err: any) {
      console.error('Error fetching client messages:', err);
      dispatch({ type: 'SET_ERROR', payload: err.message });
      dispatch({ type: 'SET_CLIENT_LOADING', payload: false });
    }
  }, [user, state.clientMessages.length]);

  // Track if we've already set up listeners
  const listenersSetupRef = useRef(false);
  const initialDataFetchedRef = useRef(false);

  // Set up WebSocket subscriptions for real-time updates or polling for serverless environments
  useEffect(() => {
    // Skip if user is not logged in
    if (!user) {
      dispatch({ type: 'SET_LOADING', payload: false });
      return;
    }

    // Initial data fetch - only if not already fetched
    if (!initialDataFetchedRef.current) {
      initialDataFetchedRef.current = true;

      if (role === 'admin') {
        fetchMessages();
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }

      // Fetch client messages for all authenticated users
      fetchClientMessages();
    }

    // If we're in a serverless environment, set up polling instead of WebSockets
    if (isServerless) {
      console.log('Setting up polling for messages in serverless environment');

      // Clear any existing polling interval
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }

      // Set up polling every 15 seconds
      const interval = setInterval(() => {
        console.log('Polling for new messages');
        if (role === 'admin') {
          fetchMessages();
        }
        fetchClientMessages();
      }, 15000); // Poll every 15 seconds

      setPollingInterval(interval);

      // Clean up interval on unmount
      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }

    // For non-serverless environments, use WebSockets
    if (!isServerless && isConnected) {
      // Prevent duplicate listener setup
      if (listenersSetupRef.current) {
        return;
      }

      listenersSetupRef.current = true;

      // Subscribe to admin messages channel for new messages
      const adminChannelUnsub = role === 'admin' ? subscribe('admin-messages', 'new-message', (newMessage: TeamMessage) => {
        console.log('New message received:', newMessage);
        dispatch({ type: 'ADD_MESSAGE', payload: newMessage });
      }) : () => {};

      // Subscribe to user-specific channel for message responses and read events
      const userChannelResponseUnsub = subscribe(`user-${user.uid}`, 'message-response', (data: any) => {
        console.log('Message response received:', data);
        const { messageId, response, responseTimestamp } = data;

        // Update both admin and client messages atomically
        dispatch({ 
          type: 'UPDATE_MESSAGE', 
          payload: { 
            messageId, 
            updates: { 
              response, 
              responseTimestamp 
            } 
          } 
        });
      });

      const userChannelReadUnsub = subscribe(`user-${user.uid}`, 'message-read', (data: any) => {
        console.log('Message read event received:', data);
        const { messageId } = data;

        // Update message status atomically
        dispatch({ 
          type: 'UPDATE_MESSAGE', 
          payload: { messageId, updates: { status: 'read' } } 
        });
      });

      // Clean up subscriptions when component unmounts
      return () => {
        adminChannelUnsub();
        userChannelResponseUnsub();
        userChannelReadUnsub();
      };
    }
  }, [user, role, isConnected, isServerless, pollingInterval, fetchMessages, fetchClientMessages]);

  // Fetch messages when the component mounts and auth state changes
  useEffect(() => {
    if (!authLoading && !user) {
      // Reset state when user logs out
      dispatch({ type: 'RESET_STATE' });

      // Reset refs when user logs out
      listenersSetupRef.current = false;
      initialDataFetchedRef.current = false;
    }
  }, [user, authLoading]);

  return (
    <TeamMessagesContext.Provider
      value={{
        messages: state.messages,
        unreadCount: state.unreadCount,
        loading: state.loading,
        error: state.error,
        fetchMessages,
        markAsRead,
        respondToMessage,
        deleteMessage,
        clientMessages: state.clientMessages,
        clientMessagesLoading: state.clientMessagesLoading,
        fetchClientMessages,
        lastUpdated: state.lastUpdated,
        isConnected,
        isServerless,
      }}
    >
      {children}
    </TeamMessagesContext.Provider>
  );
};

// Create a hook to use the context
export const useTeamMessages = () => {
  const context = useContext(TeamMessagesContext);
  if (context === undefined) {
    throw new Error('useTeamMessages must be used within a TeamMessagesProvider');
  }
  return context;
};
