"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useScans } from '@/context/ScanContext';
import { toast } from 'sonner';

// Define notification types
export type NotificationType = 'status_change' | 'new_scan' | 'system';

// Define notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  scanId?: string;
  oldStatus?: string;
  newStatus?: string;
  assetType?: string;
}

// Define context type
interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
}

// Create context
const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined);

// Provider component
export const NotificationsProvider = ({ children }: { children: ReactNode }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [clearedNotifications, setClearedNotifications] = useState<Set<string>>(new Set());
  const { user } = useAuth();
  const { scans } = useScans();
  
  // Store previous scan data to detect changes - moved outside useEffect
  const prevScansRef = React.useRef<Map<string, { status: string; exists: boolean }>>(new Map());

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Load notifications and cleared notifications from localStorage on mount
  useEffect(() => {
    if (user) {
      try {
        // Load notifications
        const savedNotifications = localStorage.getItem(`notifications_${user.uid}`);
        if (savedNotifications) {
          const parsedNotifications = JSON.parse(savedNotifications);
          // Convert string timestamps back to Date objects
          const formattedNotifications = parsedNotifications.map((n: any) => ({
            ...n,
            timestamp: new Date(n.timestamp)
          }));
          setNotifications(formattedNotifications);
        }

        // Load cleared notifications set
        const savedClearedNotifications = localStorage.getItem(`cleared_notifications_${user.uid}`);
        if (savedClearedNotifications) {
          const parsedClearedNotifications = JSON.parse(savedClearedNotifications);
          setClearedNotifications(new Set(parsedClearedNotifications));
        }
      } catch (error) {
        console.error('Error loading notifications from localStorage:', error);
      }
    }
    
    // Mark initial load as complete after a short delay
    const timer = setTimeout(() => {
      setIsInitialLoad(false);
    }, 2000); // 2 seconds to allow initial data to load
    
    return () => clearTimeout(timer);
  }, [user]);

  // Save notifications to localStorage when they change
  useEffect(() => {
    if (user) {
      try {
        if (notifications.length === 0) {
          localStorage.removeItem(`notifications_${user.uid}`);
        } else {
          localStorage.setItem(`notifications_${user.uid}`, JSON.stringify(notifications));
        }
      } catch (error) {
        console.error('Error saving notifications to localStorage:', error);
      }
    }
  }, [notifications, user]);

  // Save cleared notifications to localStorage when they change
  useEffect(() => {
    if (user) {
      try {
        if (clearedNotifications.size === 0) {
          localStorage.removeItem(`cleared_notifications_${user.uid}`);
        } else {
          localStorage.setItem(`cleared_notifications_${user.uid}`, JSON.stringify(Array.from(clearedNotifications)));
        }
      } catch (error) {
        console.error('Error saving cleared notifications to localStorage:', error);
      }
    }
  }, [clearedNotifications, user]);

// When notifications are cleared, also clear from localStorage
  useEffect(() => {
    if (user && notifications.length === 0 && !isInitialLoad) {
      try {
        localStorage.removeItem(`notifications_${user.uid}`);
      } catch (error) {
        console.error('Error clearing notifications from localStorage:', error);
      }
    }
  }, [notifications, user, isInitialLoad]);
  // Add a new notification with enhanced duplicate prevention
  const addNotification = React.useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    // Create a unique key for this notification type to check if it was cleared
    const notificationKey = notification.type === 'status_change' && notification.scanId
      ? `${notification.type}_${notification.scanId}_${notification.newStatus}`
      : notification.type === 'new_scan' && notification.scanId
      ? `${notification.type}_${notification.scanId}`
      : `${notification.type}_${notification.title}_${notification.message}`;

    // Check if this notification was previously cleared
    if (clearedNotifications.has(notificationKey)) {
      console.log(`Skipping notification that was previously cleared:`, notificationKey);
      return;
    }

    // Enhanced duplicate prevention logic
    const isDuplicate = notifications.some(n => {
      // For status change notifications
      if (notification.type === 'status_change' && n.type === 'status_change') {
        return n.scanId === notification.scanId &&
               n.newStatus === notification.newStatus &&
               (new Date().getTime() - n.timestamp.getTime()) < 5 * 60 * 1000; // 5 minutes
      }
      
      // For new scan notifications
      if (notification.type === 'new_scan' && n.type === 'new_scan') {
        return n.scanId === notification.scanId &&
               (new Date().getTime() - n.timestamp.getTime()) < 10 * 60 * 1000; // 10 minutes
      }
      
      // For system notifications, check title and message
      if (notification.type === 'system' && n.type === 'system') {
        return n.title === notification.title &&
               n.message === notification.message &&
               (new Date().getTime() - n.timestamp.getTime()) < 30 * 60 * 1000; // 30 minutes
      }
      
      return false;
    });

    if (isDuplicate) {
      console.log(`Preventing duplicate notification:`, notification);
      return;
    }

    // Create the new notification
    const newNotification: Notification = {
      ...notification,
      id: Math.random().toString(36).substring(2, 11),
      timestamp: new Date(),
      read: false
    };

    // Add the notification
    setNotifications(prev => [newNotification, ...prev]);

    // Show appropriate toast notifications
    switch (notification.type) {
      case 'status_change':
        toast.info(`Pentest status updated: ${notification.assetType} is now ${notification.newStatus}`);
        break;
      case 'new_scan':
        toast.success(`New pentest created: ${notification.assetType}`);
        break;
      case 'system':
        toast.info(notification.title);
        break;
    }
  }, [notifications, clearedNotifications]);

  // Track scan status changes and new scans
  useEffect(() => {
    // Don't create notifications during initial load
    if (isInitialLoad) return;
    
    scans.forEach(scan => {
      const prevScanData = prevScansRef.current.get(scan.id);
      
      if (!prevScanData) {
        // New scan detected - only notify if it's not the initial load and we have previous data
        if (prevScansRef.current.size > 0 && !isInitialLoad) {
          addNotification({
            type: 'new_scan',
            title: 'New Pentest Created',
            message: `A new ${scan.asset_type} pentest has been created for ${scan.target}`,
            scanId: scan.id,
            assetType: scan.asset_type
          });
        }
        prevScansRef.current.set(scan.id, { status: scan.status, exists: true });
      } else if (prevScanData.status !== scan.status) {
        // Status change detected - but let ChatScanCard handle this to avoid duplicates
        prevScansRef.current.set(scan.id, { status: scan.status, exists: true });
      }
    });
    
    // Clean up deleted scans from our tracking
    const currentScanIds = new Set(scans.map(s => s.id));
    for (const [scanId] of prevScansRef.current) {
      if (!currentScanIds.has(scanId)) {
        prevScansRef.current.delete(scanId);
      }
    }
  }, [scans, addNotification, isInitialLoad]);

  // Mark a notification as read
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // Clear all notifications and remember what was cleared
  const clearNotifications = () => {
    // Create keys for all current notifications to remember they were cleared
    const notificationKeys = notifications.map(n => {
      if (n.type === 'status_change' && n.scanId) {
        return `${n.type}_${n.scanId}_${n.newStatus}`;
      } else if (n.type === 'new_scan' && n.scanId) {
        return `${n.type}_${n.scanId}`;
      } else {
        return `${n.type}_${n.title}_${n.message}`;
      }
    });

    // Add these keys to the cleared notifications set
    setClearedNotifications(prev => {
      const newSet = new Set(prev);
      notificationKeys.forEach(key => newSet.add(key));
      return newSet;
    });

    // Clear the notifications
    setNotifications([]);
  };

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        addNotification
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
};

// Hook to use the notifications context
export const useNotifications = () => {
  const context = useContext(NotificationsContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationsProvider');
  }
  return context;
};
