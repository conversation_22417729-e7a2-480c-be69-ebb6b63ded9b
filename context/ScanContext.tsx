"use client";

import React, { createContext, useContext, useReducer, useMemo, useCallback, useEffect, useRef, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { User } from "firebase/auth";
import { usePusher } from '@/hooks/usePusher';
import { SocketEvents } from '@/lib/socket-events';
import { ScanType, UserInfo } from '@/types/scan-types';

// --- Constants ---
const DEV_MODE = process.env.NODE_ENV === 'development';
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const POLLING_INTERVAL = 10 * 60 * 1000; // 10 minutes

// --- Types (More specific for better safety) ---
interface ApiScan {
    id?: string;
    scan_id?: string;
    userId: string;
    scope?: string;
    name?: string;
    status?: 'pending' | 'in-progress' | 're-test' | 'completed';
    targetDetails?: string;
    target?: string;
    requestedAt?: string | Date;
    completedAt?: string | Date;
    criticalVulnerabilities?: number;
    highVulnerabilities?: number;
    mediumVulnerabilities?: number;
    lowVulnerabilities?: number;
    userName?: string;
    userEmail?: string;
    // Add other potential properties from your API to get full type safety
}

interface ApiOrganizationData {
    organization: string;
    // ... other properties
}

interface FetchSuccessPayload {
    view: 'personal' | 'grouped' | 'organization';
    scans: ScanType[];
    organizationData?: ApiOrganizationData;
    users?: UserInfo[];
}

interface ScanContextType {
    scans: ScanType[];
    loading: boolean;
    error: string | null;
    fetchData: (view: 'personal' | 'organization', forceRefresh?: boolean) => Promise<void>;
    deleteScan: (scanId: string) => Promise<{ message: string; filesDeleted: number; vulnerabilitiesDeleted?: number } | undefined>;
    updateScan: (scanId: string, updates: Partial<ScanType>) => Promise<void>;
    updateScanLocally: (scanId: string, updates: Partial<ScanType>) => void;
    user: User | null;
    role: 'client' | 'admin' | 'manager' | null;
    organizationData: ApiOrganizationData | null;
    groupedUsers: UserInfo[];
    isGroupedView: boolean;
    isOrganizationView: boolean;
    initialLoadComplete: boolean;
    scanCount: number;
}

// --- State & Reducer (Refined) ---
interface ScanState {
    scans: Record<string, ScanType>;
    view: 'personal' | 'grouped' | 'organization';
    organizationData: ApiOrganizationData | null;
    groupedUserIds: string[];
    loading: boolean;
    error: string | null;
    lastFetch: {
        personal: number;
        organization: number;
    };
    initialLoadComplete: boolean;
}

type ScanAction =
    | { type: 'FETCH_START' }
    | { type: 'FETCH_SUCCESS'; payload: FetchSuccessPayload }
    | { type: 'FETCH_ERROR'; payload: string }
    | { type: 'UPDATE_SCAN'; payload: { scanId: string; updates: Partial<ScanType> } }
    | { type: 'DELETE_SCAN'; payload: string }
    | { type: 'RESET_STATE' };

const initialState: ScanState = {
    scans: {},
    view: 'personal',
    organizationData: null,
    groupedUserIds: [],
    loading: false,
    error: null,
    lastFetch: { personal: 0, organization: 0 },
    initialLoadComplete: false,
};

const normalizeScans = (scans: ScanType[]): Record<string, ScanType> =>
    scans.reduce((acc, scan) => { acc[scan.id] = scan; return acc; }, {} as Record<string, ScanType>);

const scanReducer = (state: ScanState, action: ScanAction): ScanState => {
    switch (action.type) {
        case 'FETCH_START':
            if (state.loading) return state; // Declarative fetch guard
            return { ...state, loading: true, error: null };
        case 'FETCH_SUCCESS': {
            const { view, scans, organizationData, users } = action.payload;
            return {
                ...state,
                loading: false,
                view,
                scans: normalizeScans(scans),
                organizationData: organizationData || null,
                groupedUserIds: users?.map(u => u.userId) || [],
                lastFetch: { ...state.lastFetch, [view]: Date.now() },
                initialLoadComplete: true,
            };
        }
        case 'FETCH_ERROR':
            return { ...state, loading: false, error: action.payload };
        case 'UPDATE_SCAN': {
            const { scanId, updates } = action.payload;
            const currentScan = state.scans[scanId];
            if (!currentScan) {
                if (DEV_MODE) console.warn(`[ScanContext] UPDATE_SCAN: Scan with id ${scanId} not found.`);
                return state;
            }

            const updatedScan = { ...currentScan, ...updates };
            const newScans = { ...state.scans, [scanId]: updatedScan };

            return { ...state, scans: newScans };
        }
        case 'DELETE_SCAN': {
            const { [action.payload]: deletedScan, ...remainingScans } = state.scans;
            if (!deletedScan) return state;
            return { ...state, scans: remainingScans };
        }
        case 'RESET_STATE':
            return initialState;
        default:
            return state;
    }
};

// --- Utility Functions ---
const mapApiScanToScanType = (scan: ApiScan): ScanType => ({
    ...scan,
    id: scan.id || scan.scan_id || Math.random().toString(36).substring(2, 15),
    name: scan.scope || scan.name || "Untitled Scan",
    status: scan.status || "pending",
    target: scan.targetDetails || scan.target || "",
    requestedAt: new Date(scan.requestedAt || Date.now()),
    completedAt: scan.completedAt ? new Date(scan.completedAt) : undefined,
    criticalVulnerabilities: scan.criticalVulnerabilities || 0,
    highVulnerabilities: scan.highVulnerabilities || 0,
    mediumVulnerabilities: scan.mediumVulnerabilities || 0,
    lowVulnerabilities: scan.lowVulnerabilities || 0,
});

const authenticatedFetch = async (url: string, user: User, options: { forceRefresh?: boolean } = {}) => {
    const idToken = await user.getIdToken();
    const headers: HeadersInit = { 'Authorization': `Bearer ${idToken}` };
    if (options.forceRefresh) {
        headers['Cache-Control'] = 'no-cache';
    }
    const res = await fetch(url, { headers });
    if (!res.ok) {
        const errorData = await res.json().catch(() => ({ error: `Request failed: ${res.status}` }));
        throw new Error(errorData.error);
    }
    return res.json();
};

// --- Context and Provider ---
const ScanContext = createContext<ScanContextType | undefined>(undefined);

export const ScanProvider = ({ children }: { children: ReactNode }) => {
    const [state, dispatch] = useReducer(scanReducer, initialState);
    const { user, role, loading: authLoading } = useAuth();
    const { subscribe, isConnected } = usePusher();
    const lastFetchRef = useRef({ personal: 0, organization: 0 });

    const fetchData = useCallback(async (view: 'personal' | 'organization', forceRefresh = false) => {
        if (!user) return;

        const cacheValid = Date.now() - lastFetchRef.current[view] < CACHE_TTL;
        if (!forceRefresh && cacheValid) {
            if (DEV_MODE) console.log(`[Cache] Using cached ${view} scans.`);
            return;
        }

        dispatch({ type: 'FETCH_START' });
        try {
            const url = view === 'organization'
                ? `/api/scans?userId=${user.uid}&role=${role}&includeOrganization=true`
                : `/api/scans?userId=${user.uid}&role=${role}`;
            
            console.log('ScanContext fetchData - view:', view, 'url:', url);
            
            const data = await authenticatedFetch(url, user, { forceRefresh });

            if (data.result?.groupedByOrganization && data.result?.organizations) {
                // Handle organization-grouped data (for admins)
                const organizationsData = data.result.organizations.map((org: any) => ({
                    name: org.name,
                    users: org.users.map((u: any) => ({
                        ...u,
                        scans: (u.scans || []).map(mapApiScanToScanType),
                    }))
                }));
                const allScans = organizationsData.flatMap((org: any) =>
                    org.users.flatMap((user: any) => user.scans)
                );
                dispatch({
                    type: 'FETCH_SUCCESS',
                    payload: {
                        view: 'organization',
                        scans: allScans,
                        organizationData: organizationsData
                    }
                });
            } else if (data.result?.groupedByUser) {
                // Handle user-grouped data (for managers)
                const usersData = (data.result.users || []).map((u: any) => ({
                    ...u, scans: (u.scans || []).map(mapApiScanToScanType),
                }));
                dispatch({ type: 'FETCH_SUCCESS', payload: { view: 'grouped', scans: usersData.flatMap((u: UserInfo) => u.scans), users: usersData } });
            } else {
                // Handle regular scan data (for clients)
                const scans = (Array.isArray(data.result?.scans) ? data.result.scans : Array.isArray(data.result) ? data.result : []).map(mapApiScanToScanType);
                const newView = data.result?.organizationData ? 'organization' : view;
                dispatch({ type: 'FETCH_SUCCESS', payload: { view: newView, scans, organizationData: data.result?.organizationData } });
            }
            // Update ref for cache validation
            lastFetchRef.current[view] = Date.now();
        } catch (e: any) {
            dispatch({ type: 'FETCH_ERROR', payload: e.message || `Unknown error fetching ${view} data` });
        }
    }, [user, role, state.view]);

    const deleteScan = useCallback(async (scanId: string) => {
        if (!user) throw new Error("Authentication required.");
        try {
            const idToken = await user.getIdToken();
            const res = await fetch(`/api/scans?scanId=${scanId}`, { method: "DELETE", headers: { 'Authorization': `Bearer ${idToken}` } });
            if (!res.ok) {
                const errorText = await res.text();
                throw new Error(errorText || "Deletion failed on server.");
            }
            await fetchData(state.view as 'personal' | 'organization', true);
            return await res.json();
        } catch (e: any) {
            dispatch({ type: 'FETCH_ERROR', payload: e.message || "Deletion failed" });
            throw e;
        }
    }, [user, fetchData, state.view]);

    const updateScanLocally = useCallback((scanId: string, updates: Partial<ScanType>) => {
        if (DEV_MODE) console.log(`[ScanContext] Dispatching UPDATE_SCAN for ${scanId}`, updates);
        dispatch({ type: 'UPDATE_SCAN', payload: { scanId, updates } });
    }, []);

    const updateScan = useCallback(async (scanId: string, updates: Partial<ScanType>) => {
        if (!user) throw new Error("Authentication required.");

        const originalScan = state.scans[scanId];
        if (!originalScan) {
            console.error(`[ScanContext] Cannot update scan: Scan with ID ${scanId} not found.`);
            return;
        }

        // Optimistic update
        dispatch({ type: 'UPDATE_SCAN', payload: { scanId, updates } });

        try {
            const idToken = await user.getIdToken();
            const response = await fetch(`/api/scans`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${idToken}` },
                body: JSON.stringify({ scanId, ...updates }),
            });

            if (!response.ok) {
                // Revert on failure
                dispatch({ type: 'UPDATE_SCAN', payload: { scanId, updates: originalScan } });
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to update scan (${response.status})`);
            }

            // Force a refetch from the server to ensure data consistency
            await fetchData(state.view as 'personal' | 'organization', true);

        } catch (error: any) {
            // Revert on any error
            dispatch({ type: 'UPDATE_SCAN', payload: { scanId, updates: originalScan } });
            console.error("Error updating scan:", error);
            // Optionally re-throw or handle the error in the UI
            throw error;
        }
    }, [user, fetchData, state.scans, state.view]);

    useEffect(() => {
        if (user && !authLoading && !state.initialLoadComplete) {
            const initialView = role === 'client' ? 'organization' : 'personal';
            fetchData(initialView, true);
        } else if (!user && !authLoading) {
            dispatch({ type: 'RESET_STATE' });
        }
    }, [user, role, authLoading, state.initialLoadComplete, fetchData]);

    useEffect(() => {
        if (!user || !isConnected) return;
        const handleScanUpdate = (scanData: ApiScan) => updateScanLocally(mapApiScanToScanType(scanData).id, mapApiScanToScanType(scanData));
        const handleScanDelete = (scanData: { id: string }) => dispatch({ type: 'DELETE_SCAN', payload: scanData.id });
        const handleScanCreate = () => fetchData(state.view as 'personal' | 'organization', true);

        const unsubCreate = subscribe(SocketEvents.SCAN_CREATED, handleScanCreate);
        const unsubUpdate = subscribe(SocketEvents.SCAN_UPDATED, handleScanUpdate);
        const unsubDelete = subscribe(SocketEvents.SCAN_DELETED, handleScanDelete);

        return () => { unsubCreate(); unsubUpdate(); unsubDelete(); };
    }, [user, isConnected, subscribe, updateScanLocally, fetchData, state.view]);

    useEffect(() => {
        if (user && !isConnected) {
            const intervalId = setInterval(() => fetchData(state.view as 'personal' | 'organization'), POLLING_INTERVAL);
            return () => clearInterval(intervalId);
        }
    }, [user, isConnected, fetchData, state.view]);

    const allScansArray = useMemo(() => Object.values(state.scans), [state.scans]);

    const groupedUsers = useMemo(() => {
        if (state.view !== 'grouped') return [];
        const scansByUserId = allScansArray.reduce((acc, scan) => {
            if (scan.userId) (acc[scan.userId] = acc[scan.userId] || []).push(scan);
            return acc;
        }, {} as Record<string, ScanType[]>);
        return state.groupedUserIds.map(userId => ({
            userId,
            name: scansByUserId[userId]?.[0]?.userName || 'Unknown User',
            email: scansByUserId[userId]?.[0]?.userEmail || '',
            scans: scansByUserId[userId] || [],
        }));
    }, [allScansArray, state.view, state.groupedUserIds]);

    const contextValue = useMemo<ScanContextType>(() => ({
        scans: allScansArray,
        loading: state.loading,
        error: state.error,
        fetchData,
        deleteScan,
        updateScan,
        updateScanLocally,
        user,
        role,
        organizationData: state.organizationData,
        groupedUsers,
        isGroupedView: state.view === 'grouped',
        isOrganizationView: state.view === 'organization',
        initialLoadComplete: state.initialLoadComplete,
        scanCount: allScansArray.length,
    }), [state, allScansArray, fetchData, deleteScan, updateScan, updateScanLocally, user, role, groupedUsers]);

    return (
        <ScanContext.Provider value={contextValue}>
            {children}
        </ScanContext.Provider>
    );
};

export const useScans = () => {
    const context = useContext(ScanContext);
    if (context === undefined) {
        throw new Error('useScans must be used within a ScanProvider');
    }
    return context;
};