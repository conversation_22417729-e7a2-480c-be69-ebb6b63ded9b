"use client";

import React, { createContext, useContext, useState, useEffect, useCallback, useRef, useMemo, useTransition } from "react";
import { useAuth } from "@/hooks/useAuth"; // Assuming User type is exported from useAuth
import { ConversationType, MessageType, FileInfoType } from "@/lib/models/conversation"; // Ensure this path is correct
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";

const DEV_MODE = process.env.NODE_ENV === 'development';

interface ConversationsContextType {
  conversations: ConversationType[];
  activeConversationId: string | null;
  activeConversation: ConversationType | null;
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadingMore: boolean;
  historyLoaded: boolean;
  setActiveConversationId: (id: string | null) => void;
  createNewConversation: () => Promise<string | null>;
  deleteConversation: (id: string) => Promise<boolean>;
  addMessageToConversation: (message: Omit<MessageType, "id" | "timestamp">) => Promise<string>;
  updateMessageContent: (conversationId: string, messageId: string, content: string) => Promise<boolean>;
  addFileToConversation: (fileInfo: Omit<FileInfoType, "uploadedAt">) => Promise<boolean>;
  updateConversationTitle: (id: string, title: string) => Promise<boolean>;
  loadMoreConversations: () => Promise<boolean>;
  loadConversationHistory: () => Promise<void>;
}

const ConversationsContext = createContext<ConversationsContextType | null>(null);

export const useConversations = () => {
  const context = useContext(ConversationsContext);
  if (!context) {
    throw new Error("useConversations must be used within a ConversationsProvider");
  }
  return context;
};

export const ConversationsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const [conversations, setConversations] = useState<ConversationType[]>([]);
  const [activeConversationId, setActiveConversationIdState] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [historyLoaded, setHistoryLoaded] = useState(false);

  const initialSetupDoneRef = useRef(false);
  const conversationsCacheRef = useRef<Map<string, ConversationType>>(new Map());

  // Deduplicate conversations to prevent React key errors
  const deduplicatedConversations = useMemo(() => {
    const seen = new Set();
    return conversations.filter(conv => {
      if (seen.has(conv.id)) {
        if (DEV_MODE) console.warn('Duplicate conversation detected:', conv.id);
        return false;
      }
      seen.add(conv.id);
      return true;
    });
  }, [conversations]);

  const activeConversation = useMemo(() => {
    // if (DEV_MODE) console.log("Recalculating activeConversation");
    return activeConversationId
      ? deduplicatedConversations.find(c => c.id === activeConversationId) || null
      : null;
  }, [deduplicatedConversations, activeConversationId]);

  const setActiveConversationId = useCallback((id: string | null) => {
    if (DEV_MODE) console.log("Setting active conversation ID to:", id);
    setActiveConversationIdState(id);
  }, []);

  const fetchConversations = useCallback(async (page = 1, isInitialFetch = false, signal?: AbortSignal) => {
    if (!user) return { success: false, count: 0, conversations: [] };

    // Check cache first for better performance
    const cacheKey = `conversations-${user.uid}-${page}`;
    const cachedData = conversationsCacheRef.current.get(cacheKey);
    const cacheAge = cachedData ? Date.now() - (cachedData as any).timestamp : Infinity;
    
    if (cachedData && cacheAge < 30000 && !isInitialFetch) { // 30 second cache
      if (DEV_MODE) console.log('Using cached conversations data');
      if (page === 1) {
        setConversations((cachedData as any).conversations || []);
        if (((cachedData as any).conversations?.length > 0) && !activeConversationId && isInitialFetch) {
          setActiveConversationId((cachedData as any).conversations[0].id);
        }
      } else {
        setConversations(prev => [...prev, ...((cachedData as any).conversations || [])]);
      }
      setHasMore((cachedData as any).hasMore || false);
      setCurrentPage(page);
      return { success: true, count: (cachedData as any).conversations?.length || 0, conversations: (cachedData as any).conversations || [] };
    }

    if (page === 1) setLoading(true);
    else setLoadingMore(true);
    setError(null);

    try {
      if (DEV_MODE) console.log(`Fetching conversations, page: ${page}, user: ${user.uid}`);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations?page=${page}`, {
        headers: {
          Authorization: `Bearer ${idToken}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        },
        signal,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to fetch conversations: ${response.status} - ${errorData.error || response.statusText}`);
      }

      const data = await response.json();
      
      // Cache the response
      conversationsCacheRef.current.set(cacheKey, {
        ...data,
        timestamp: Date.now()
      });

      startTransition(() => {
        if (page === 1) {
          setConversations(data.conversations || []);
          if ((data.conversations?.length > 0) && !activeConversationId && isInitialFetch) {
            if (DEV_MODE) console.log("Setting active conversation to first fetched:", data.conversations[0].id);
            setActiveConversationId(data.conversations[0].id);
          }
        } else {
          setConversations(prev => [...prev, ...(data.conversations || [])]);
        }
        setHasMore(data.hasMore || false);
        setCurrentPage(page);
      });

      return { success: true, count: data.conversations?.length || 0, conversations: data.conversations || [] };
    } catch (error: any) {
      if (error.name === 'AbortError') {
        if (DEV_MODE) console.log('Fetch conversations aborted');
        return { success: false, count: 0, conversations: [], aborted: true };
      }
      if (DEV_MODE) console.error("Error fetching conversations:", error);
      setError(error.message);
      return { success: false, count: 0, conversations: [] };
    } finally {
      if (!signal?.aborted) {
        if (page === 1) setLoading(false);
        else setLoadingMore(false);
      }
    }
  }, [user, activeConversationId, setActiveConversationId]);

  const createNewConversation = useCallback(async (): Promise<string | null> => {
    if (!user) {
      if (DEV_MODE) console.error("Cannot create conversation: user is not authenticated");
      toast.error("You must be logged in to create a conversation");
      return null;
    }
    
    const wasLoading = loading;
    if (!wasLoading) setLoading(true);
    setError(null);
    
    try {
      if (DEV_MODE) console.log("Creating new conversation");
      const idToken = await user.getIdToken();
      const initialMessage: MessageType = {
        id: uuidv4(),
        role: "assistant",
        content: "Hello! Welcome to GrowthGuard's security services portal.\nI'm your AI assistant for penetration testing services. Let's get started with your Pentest request. To begin, please tell me what type of asset you want to test: Web Application, Mobile Application, API, Network, or Cloud Infrastructure?",
        timestamp: new Date().toISOString(),
      };
      const newConversationPayload: Partial<ConversationType> = { // Use Partial for payload
        userId: user.uid,
        title: "Pentest Request", // Updated default title
        messages: [initialMessage],
        fileUrls: [],
        fileInfo: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const response = await fetch("/api/conversations", {
        method: "POST", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` },
        body: JSON.stringify(newConversationPayload), cache: 'no-store',
      });

      const responseData = await response.json();
      if (!response.ok) throw new Error(`Failed to create conversation: ${response.status} - ${responseData?.error || 'Unknown server error'}`);

      const newConversationData: ConversationType = responseData.conversation;
      if (DEV_MODE) console.log("New conversation created on server:", newConversationData);

      setConversations(prev => [newConversationData, ...prev]);
      setActiveConversationId(newConversationData.id);
      if (conversations.length > 0) toast.success("New conversation started!");
      return newConversationData.id;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error creating conversation:", error);
      toast.error(`Failed to create new conversation: ${error.message}`);
      setError(error.message);
      return null;
    } finally {
      if (!wasLoading) setLoading(false);
    }
  }, [user, setActiveConversationId, loading, conversations.length]);

  const loadMoreConversations = useCallback(async (): Promise<boolean> => {
    if (loadingMore || !hasMore || !user) return false;
    if (DEV_MODE) console.log("Loading more conversations, current page:", currentPage);
    const result = await fetchConversations(currentPage + 1);
    return result.success;
  }, [loadingMore, hasMore, user, currentPage, fetchConversations]);

  const deleteConversation = useCallback(async (id: string): Promise<boolean> => {
    if (!user) {
      toast.error("You must be logged in to delete a conversation");
      return false;
    }

    const originalConversations = [...conversations]; // Shallow copy for rollback
    const conversationToDelete = originalConversations.find(c => c.id === id);
    if (!conversationToDelete) return false;

    setConversations(prev => prev.filter(c => c.id !== id));
    let nextActiveIdToSet: string | null = null;
    if (activeConversationId === id) {
      const remainingAfterFilter = originalConversations.filter(c => c.id !== id);
      nextActiveIdToSet = remainingAfterFilter.length > 0 ? remainingAfterFilter[0].id : null;
      setActiveConversationId(nextActiveIdToSet);
    }

    try {
      if (DEV_MODE) console.log("Deleting conversation:", id);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations/${id}`, {
        method: "DELETE", headers: { Authorization: `Bearer ${idToken}` }, cache: 'no-store',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to delete conversation: ${response.status} - ${errorData?.error || 'Server error'}`);
      }
      toast.success("Conversation deleted");
      
      // Clear cache to prevent stale data on refresh
      conversationsCacheRef.current.clear();

      // Check current state after optimistic update
      const currentConvos = conversations.filter(c => c.id !== id);
      if (nextActiveIdToSet === null && currentConvos.length === 0) {
         if (DEV_MODE) console.log("No conversations left after delete, creating new one.");
         await createNewConversation();
      }
      return true;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error deleting conversation:", error);
      toast.error(`Failed to delete conversation: ${error.message}`);
      setConversations(originalConversations);
      if (activeConversationId === id || (nextActiveIdToSet !== null && activeConversationId !== nextActiveIdToSet)) {
           setActiveConversationId(id);
      }
      return false;
    }
  }, [user, conversations, activeConversationId, setActiveConversationId, createNewConversation]);

  const updateConversationTitle = useCallback(async (id: string, title: string): Promise<boolean> => {
    if (!user) return false;

    const originalConversations = [...conversations];
    const originalConv = originalConversations.find(c => c.id === id);
    const originalTitle = originalConv?.title;

    setConversations(prev =>
      prev.map(conv =>
        conv.id === id ? { ...conv, title, updatedAt: new Date().toISOString() } : conv
      )
    );

    try {
      if (DEV_MODE) console.log("Updating title for conversation:", id, "to:", title);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations/${id}`, {
        method: "PUT", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({ title }), cache: 'no-store',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to update title: ${response.status} - ${errorData?.error || 'Server error'}`);
      }


      toast.success("Conversation title updated!");
      return true;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error updating title:", error);
      toast.error(`Failed to update title: ${error.message}`);
      setConversations(prev =>
        prev.map(conv =>
          conv.id === id ? { ...conv, title: originalTitle || "Pentest Request" } : conv
        )
      );
      return false;
    }
  }, [user, conversations]);


  const addMessageToConversation = useCallback(async (
    messageData: Omit<MessageType, "id" | "timestamp">
  ): Promise<string> => {
    if (!user || !activeConversationId) throw new Error("Cannot add message: user or conversation is missing");

    const newMessage: MessageType = { ...messageData, id: uuidv4(), timestamp: new Date().toISOString() };
    const originalConversations = [...conversations];

    // Optimistic update with transition
    startTransition(() => {
      setConversations(prev =>
        prev.map(conv =>
          conv.id === activeConversationId
            ? { ...conv, messages: [...conv.messages, newMessage], updatedAt: new Date().toISOString() }
            : conv
        )
      );
    });

    try {
      if (DEV_MODE) console.log("Adding message to conversation:", activeConversationId, newMessage);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations/${activeConversationId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ message: newMessage }),
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(`Failed to add message: ${response.status} - ${responseData?.error || 'Server error'}`);

      // Clear cache after successful message addition
      conversationsCacheRef.current.clear();
      
      return newMessage.id;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error adding message:", error);
      toast.error(`Failed to send message: ${error.message}`);
      startTransition(() => {
        setConversations(originalConversations);
      });
      throw error;
    }
  }, [user, activeConversationId, conversations]);

  const updateMessageContent = useCallback(async (
    conversationId: string, messageId: string, content: string
  ): Promise<boolean> => {
    if (!user) return false;

    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, messages: conv.messages.map(msg => msg.id === messageId ? { ...msg, content } : msg), updatedAt: new Date().toISOString() }
          : conv
      )
    );

    try {
      // if (DEV_MODE) console.log("Persisting (potentially final) message content:", conversationId, messageId);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations/${conversationId}/messages/${messageId}`, {
        method: "PUT", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({ content }), cache: 'no-store',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (DEV_MODE) console.error(`Failed to persist message update: ${response.status} - ${errorData?.error || 'Server error'}`);
        return false;
      }
      return true;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error persisting message update:", error);
      return false;
    }
  }, [user]);

  const addFileToConversation = useCallback(async (
    fileInfo: Omit<FileInfoType, "uploadedAt">
  ): Promise<boolean> => {
    if (!user || !activeConversationId) return false;

    const newFileInfo: FileInfoType = { ...fileInfo, uploadedAt: new Date().toISOString() };
    const originalConversations = [...conversations];
    const currentActiveConv = originalConversations.find(c => c.id === activeConversationId);
    if (!currentActiveConv) return false;

    setConversations(prev =>
      prev.map(conv =>
        conv.id === activeConversationId
          ? { ...conv, fileUrls: [...(conv.fileUrls || []), newFileInfo.url], fileInfo: [...(conv.fileInfo || []), newFileInfo], updatedAt: new Date().toISOString() }
          : conv
      )
    );

    try {
      if (DEV_MODE) console.log("Adding file to conversation:", activeConversationId, newFileInfo);
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/conversations/${activeConversationId}`, {
        method: "PUT", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({ fileUrls: [...(currentActiveConv.fileUrls || []), newFileInfo.url], fileInfo: [...(currentActiveConv.fileInfo || []), newFileInfo] }),
        cache: 'no-store',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Failed to add file: ${response.status} - ${errorData?.error || 'Server error'}`);
      }
      return true;
    } catch (error: any) {
      if (DEV_MODE) console.error("Error adding file:", error);
      toast.error(`Failed to add file: ${error.message}`);
      setConversations(originalConversations);
      return false;
    }
  }, [user, activeConversationId, conversations]);

  // Lazy load conversations only when needed
  const loadConversationHistory = useCallback(async () => {
    if (historyLoaded || loading || !user) return;
    
    setHistoryLoaded(true);
    const result = await fetchConversations(1, true);
    
    if (result.success) {
      if (result.conversations.length === 0) {
        if (DEV_MODE) console.log("No conversations found on server, creating first one");
        await createNewConversation();
      } else {
        // Open the most recent conversation (first in array since sorted by updatedAt desc)
        if (DEV_MODE) console.log("Opening most recent conversation:", result.conversations[0].id);
        setActiveConversationId(result.conversations[0].id);
      }
    }
  }, [historyLoaded, loading, user, fetchConversations, createNewConversation, setActiveConversationId]);

  useEffect(() => {
    if (user && !authLoading && !initialSetupDoneRef.current) {
      initialSetupDoneRef.current = true;
      if (DEV_MODE) console.log("User authenticated, loading conversations");
      loadConversationHistory();
    } else if (!user && !authLoading) {
      if (DEV_MODE) console.log("User logged out, resetting conversations state.");
      setConversations([]);
      setActiveConversationIdState(null);
      setError(null);
      setCurrentPage(1);
      setHasMore(false);
      setLoading(false);
      setLoadingMore(false);
      setHistoryLoaded(false);
      initialSetupDoneRef.current = false;
    }
  }, [user, authLoading, loadConversationHistory]);

  const contextValue = useMemo(() => ({
    conversations: deduplicatedConversations,
    activeConversationId,
    activeConversation,
    loading: loading || isPending,
    error,
    hasMore,
    loadingMore,
    historyLoaded,
    setActiveConversationId,
    createNewConversation,
    deleteConversation,
    addMessageToConversation,
    updateMessageContent,
    addFileToConversation,
    updateConversationTitle,
    loadMoreConversations,
    loadConversationHistory
  }), [
    deduplicatedConversations,
    activeConversationId,
    activeConversation,
    loading,
    isPending,
    error,
    hasMore,
    loadingMore,
    historyLoaded,
    setActiveConversationId,
    createNewConversation,
    deleteConversation,
    addMessageToConversation,
    updateMessageContent,
    addFileToConversation,
    updateConversationTitle,
    loadMoreConversations,
    loadConversationHistory
  ]);



  return (
    <ConversationsContext.Provider value={contextValue}>
      {children}
    </ConversationsContext.Provider>
  );
};