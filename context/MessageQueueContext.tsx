"use client"

import Re<PERSON>, { createContext, useContext, useC<PERSON>back, useMemo } from "react";
import { useAuth } // Assuming User type is exported from useAuth
    from "@/hooks/useAuth";
import { useConversations } from "@/context/ConversationsContext";
import { toast } from "sonner";
import { formatFileSize } from "@/lib/file-utils";
import { MessageType, AiResponseContent, FileInfoType } from "@/lib/models/conversation"; // Ensure this path is correct

interface MessageQueueContextType {
  sendMessage: (content: string) => Promise<void>;
  uploadFile: (file: File) => Promise<void>;
}

const MessageQueueContext = createContext<MessageQueueContextType | null>(null);

export const useMessageQueue = () => {
  const context = useContext(MessageQueueContext);
  if (!context) {
    throw new Error("useMessageQueue must be used within a MessageQueueProvider");
  }
  return context;
};

// Helper to prepare message content for AI API
const prepareContentForAI = (content: MessageType['content']): string => {
    if (typeof content === 'string') {
        return content;
    }
    if (content && typeof content === 'object') {
        if ('text' in content && typeof (content as AiResponseContent).text === 'string') {
            return (content as AiResponseContent).text;
        }
        if ('isScanCard' in content) {
            const sc = content as any; // Cast to any for simplicity here, or use proper type guard
            return `[User initiated a scan for ${sc.assetType}: ${sc.target}]`;
        }
        if ('isDownloadCard' in content) {
            return `[System provided a download link for a report]`;
        }
        if ('isSystemFileUpload' in content) {
            const fuc = content as any;
            return `[User uploaded file: ${fuc.fileName}]`;
        }
        if ('isSystemNotification' in content) {
            return `[System notification: ${(content as any).text}]`;
        }
        // Fallback for other object types - consider a more specific representation
        return "[Structured message content]";
    }
    return "[Unknown message content]";
};


export const MessageQueueProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const {
    activeConversationId,
    addMessageToConversation,
    addFileToConversation,
    createNewConversation,
    conversations, // Used for history in fetchAIResponse
    updateMessageContent, // For streaming (if implemented)
  } = useConversations();


  const fetchAIResponse = useCallback(async (conversationId: string, userContent: string) => {
    try {
      const conversation = conversations.find(conv => conv.id === conversationId);
      const messagesForApi: { role: string; content: string }[] = [];

      if (conversation?.messages) {
        messagesForApi.push(...conversation.messages.map(msg => ({
          role: msg.role,
          content: prepareContentForAI(msg.content)
        })));
      }

      // Ensure latest user message is present, avoid exact duplicates if already in history (edge case)
      const uniqueMessagesForApi = messagesForApi.filter(m => !(m.role === 'user' && m.content === userContent));
      uniqueMessagesForApi.push({ role: "user", content: userContent });

      uniqueMessagesForApi.push({ role: "system", content: "Do not include JSON summaries in your chat responses." });
      uniqueMessagesForApi.push({ role: "system", content: "When the conversation reaches the point of asking for credentials or sensitive access information, instead of requesting them directly, state clearly that 'The team will contact you separately for any necessary credentials or sensitive access information.'"});

      // console.log(`Sending ${uniqueMessagesForApi.length} messages to API for conversation ${conversationId}`);

      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ messages: uniqueMessagesForApi }),
      });

      if (!res.ok) {
        let errorData;
        try { errorData = await res.json(); } catch (e) { errorData = { error: `API error: ${res.status}` }; }
        const errorMessage = errorData.error ? `Error: ${errorData.error}${errorData.details ? ` - ${errorData.details}` : ''}` : `API error: ${res.status}`;
        throw new Error(errorMessage);
      }

      const responseData = await res.json();
      let content = responseData.content || '';
      const jsonCodeBlockRegex = /```json\s*(\{[\s\S]*?\})\s*```/i;
      if (jsonCodeBlockRegex.test(content)) {
        content = content.replace(jsonCodeBlockRegex, '').trim();
      }

      let scanInfoFlags = null;
      const scanInfoMatch = content.match(/<scan_info>([\s\S]*?)<\/scan_info>/);
      if (scanInfoMatch && scanInfoMatch[1]) {
        try {
          scanInfoFlags = JSON.parse(scanInfoMatch[1].trim());
          content = content.replace(/<scan_info>[\s\S]*?<\/scan_info>/, '').trim();
        } catch (error) { console.error("Error parsing scan info flags:", error); }
      }

      await addMessageToConversation({
        role: "assistant",
        content: scanInfoFlags ? { text: content, scanInfoFlags } : content,
      });

    } catch (error) {
      console.error("Error fetching AI response:", error);
      let errorMessage = "Sorry, I encountered an error processing your request.";
      if (error instanceof Error) {
        if (error.message.includes("OpenAI API")) errorMessage = `${errorMessage} There was an issue with the AI service. ${error.message}`;
        else if (error.message.includes("API key")) errorMessage = `${errorMessage} There was an authentication issue with the AI service.`;
        else errorMessage = `${errorMessage} ${error.message}`;
      }
      toast.error("Failed to get AI response. Please try again later.");
      try {
        await addMessageToConversation({ role: "assistant", content: errorMessage });
      } catch (saveError) { console.error("Failed to save error message to conversation:", saveError); }
    }
  }, [conversations, addMessageToConversation]); // `conversations` is a key dependency here

  const sendMessage = useCallback(async (content: string): Promise<void> => {
    if (!user) {
      toast.error("User not authenticated");
      throw new Error("User not authenticated");
    }
    let currentConversationId = activeConversationId;
    try {
      if (!currentConversationId) {
        const newId = await createNewConversation();
        if (!newId) throw new Error("Failed to create or identify conversation");
        currentConversationId = newId;
      }
      // Intentionally not awaiting fetchAIResponse to allow user message to save first for better UX
      // but start AI fetch immediately.
      fetchAIResponse(currentConversationId, content);
      await addMessageToConversation({ role: "user", content: content });
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error(`Failed to send message: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }, [user, activeConversationId, createNewConversation, addMessageToConversation, fetchAIResponse]);


  const uploadFileInBackground = useCallback(async (conversationId: string, messageId: string, file: File) => {
    const idToken = await user?.getIdToken();
    if (!idToken) {
        toast.error("Authentication error. Please log in again.");
        const failedContent = {
            isSystemFileUpload: true,
            fileName: file.name,
            fileSize: formatFileSize(file.size),
            status: 'failed',
        };
        await updateMessageContent(conversationId, messageId, JSON.stringify(failedContent));
        return;
    }

    try {
        const formData = new FormData();
        formData.append('file', file);
        const response = await fetch('/api/chat-upload', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${idToken}` },
            body: formData,
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `Failed to upload file: ${response.status}`);
        }

        const result = await response.json();
        const fileUrl = result?.fileUrl;
        const storedFileInfo: Partial<FileInfoType> = result?.fileInfo || {};

        const fileInfoToAdd: FileInfoType = {
            url: fileUrl || '',
            originalName: file.name,
            size: file.size,
            contentType: file.type,
            uploadedAt: new Date().toISOString(),
            ...storedFileInfo,
        };
        await addFileToConversation(fileInfoToAdd);

        const successContent = {
            isSystemFileUpload: true,
            fileName: file.name,
            fileSize: formatFileSize(file.size),
            status: 'uploaded',
            fileUrl: fileUrl,
        };
        await updateMessageContent(conversationId, messageId, JSON.stringify(successContent));
        toast.success(`File "${file.name}" uploaded.`);

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error uploading file:", error);
        toast.error(`Failed to upload file "${file.name}": ${errorMessage}`);
        
        const errorContent = {
            isSystemFileUpload: true,
            fileName: file.name,
            fileSize: formatFileSize(file.size),
            status: 'failed',
        };
        await updateMessageContent(conversationId, messageId, JSON.stringify(errorContent));
    }
  }, [user, addFileToConversation, updateMessageContent]);

  const uploadFile = useCallback(async (file: File): Promise<void> => {
    if (!user) {
        toast.error("User not authenticated");
        throw new Error("User not authenticated");
    }
    let currentConversationId = activeConversationId;
    if (!currentConversationId) {
        const newId = await createNewConversation();
        if (!newId) {
            toast.error("Failed to start a new conversation for file upload.");
            throw new Error("No active or new conversation for file upload");
        }
        currentConversationId = newId;
    }

    // Add a temporary "uploading" message and get its ID
    const tempMessageId = await addMessageToConversation({
        role: "system", // Or "user"
        content: {
            isSystemFileUpload: true,
            fileName: file.name,
            fileSize: formatFileSize(file.size),
            status: 'uploading',
        },
    });

    if (!tempMessageId) {
        toast.error("Failed to create a temporary message for file upload.");
        return;
    }

    // Start the actual upload in the background.
    uploadFileInBackground(currentConversationId, tempMessageId, file).catch(err => {
        console.error("Upload process failed:", err);
    });
  }, [user, activeConversationId, createNewConversation, addMessageToConversation, uploadFileInBackground]);


  const contextValue = useMemo(() => ({
    sendMessage,
    uploadFile,
  }), [sendMessage, uploadFile]);

  return (
    <MessageQueueContext.Provider value={contextValue}>
      {children}
    </MessageQueueContext.Provider>
  );
};