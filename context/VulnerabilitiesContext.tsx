"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from "react";
import { Vulnerability, VulnerabilityFilter, VulnerabilitySort, VulnerabilitySeverity } from "@/types/vulnerability-types";
import { useAuth } from "@/hooks/useAuth";

interface VulnerabilitiesContextType {
  vulnerabilities: Vulnerability[];
  loading: boolean;
  error: string | null;
  filters: VulnerabilityFilter;
  sort: VulnerabilitySort;
  filteredVulnerabilities: Vulnerability[];
  totalVulnerabilities: number;
  fetchVulnerabilities: (includeOrganization?: boolean) => Promise<void>;
  setFilters: (filters: VulnerabilityFilter) => void;
  setSort: (sort: VulnerabilitySort) => void;
  clearFilters: () => void;
  isOrganizationView: boolean;
}

const VulnerabilitiesContext = createContext<VulnerabilitiesContextType | undefined>(undefined);

export function VulnerabilitiesProvider({ children }: { children: ReactNode }) {
  const { user, loading: authLoading } = useAuth();
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<VulnerabilityFilter>({});
  const [isOrganizationView, setIsOrganizationView] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<{ personal: number; organization: number }>({
    personal: 0,
    organization: 0
  });
  const activeRequestRef = useRef<Promise<void> | null>(null);
  const [sort, setSort] = useState<VulnerabilitySort>({
    field: "severity",
    direction: "asc", // We'll handle the custom order in the sort function
  });

  const severityOrder: Record<VulnerabilitySeverity, number> = {
    critical: 1,
    high: 2,
    medium: 3,
    low: 4,
  };

  // Helper function for exponential backoff retry
  const fetchWithRetry = async (url: string, options: RequestInit, maxRetries: number = 3): Promise<Response> => {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, options);
        
        // If rate limited, wait and retry
        if (response.status === 429 && attempt < maxRetries) {
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff: 1s, 2s, 4s
          console.log(`Rate limited, retrying in ${waitTime}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }
        
        return response;
      } catch (error) {
        if (attempt === maxRetries) throw error;
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`Request failed, retrying in ${waitTime}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    throw new Error('Max retries exceeded');
  };

  // Fetch vulnerabilities from the API
  const fetchVulnerabilities = async (includeOrganization: boolean = false) => {
    if (authLoading || !user) return;

    // Check if there's already an active request
    if (activeRequestRef.current) {
      console.log("Request already in progress, waiting for completion...");
      return activeRequestRef.current;
    }

    // Check cache (5 minutes TTL)
    const cacheKey = includeOrganization ? 'organization' : 'personal';
    const lastFetch = lastFetchTime[cacheKey];
    const now = Date.now();
    const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

    if (lastFetch && (now - lastFetch) < CACHE_TTL && isOrganizationView === includeOrganization) {
      console.log(`Using cached ${cacheKey} vulnerabilities data`);
      return;
    }

    setLoading(true);
    setError(null);

    // Create and store the request promise
    const requestPromise = (async () => {
      try {
        const idToken = await user.getIdToken();
        const url = includeOrganization
          ? "/api/vulnerabilities?includeOrganization=true"
          : "/api/vulnerabilities";
          
        const response = await fetchWithRetry(url, {
          headers: {
            Authorization: `Bearer ${idToken}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch vulnerabilities");
        }

        const data = await response.json();
        setVulnerabilities(data.vulnerabilities);
        setIsOrganizationView(includeOrganization);
        
        // Update cache timestamp
        setLastFetchTime(prev => ({
          ...prev,
          [cacheKey]: now
        }));
      } catch (err) {
        console.error("Error fetching vulnerabilities:", err);
        setError("Failed to load vulnerabilities. Please try again later.");
      } finally {
        setLoading(false);
        activeRequestRef.current = null; // Clear the active request
      }
    })();

    activeRequestRef.current = requestPromise;
    return requestPromise;
  };

  // Fetch vulnerabilities on mount
  useEffect(() => {
    if (!authLoading && user) {
      fetchVulnerabilities();
    }
  }, [user, authLoading]);

  // Apply filters and sorting to vulnerabilities
  const filteredVulnerabilities = React.useMemo(() => {
    let result = [...vulnerabilities];

    // Apply filters
    if (filters.severity && filters.severity.length > 0) {
      const lowerCaseSeverities = filters.severity.map(s => s.toLowerCase());
      result = result.filter((vuln) => lowerCaseSeverities.includes(vuln.severity.toLowerCase()));
    }

    if (filters.status && filters.status.length > 0) {
      const lowerCaseSelectedStatuses = filters.status.map(s => s.toLowerCase());
      result = result.filter((vuln) => lowerCaseSelectedStatuses.includes((vuln.status || "open").toLowerCase()));
    }

    if (filters.scanId) {
      result = result.filter((vuln) => vuln.scanId === filters.scanId);
    }

    if (filters.scanName) {
      result = result.filter((vuln) => vuln.scanName === filters.scanName);
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter((vuln) => {
        const searchableFields = [
          vuln.name,
          vuln.description,
          vuln.severity,
          vuln.status,
          vuln.affectedComponent,
          (vuln as any).scanName, // Assuming scanName exists on Vulnerability
          (vuln as any).target, // Assuming target exists on Vulnerability
        ];
        return searchableFields.some(
          (field) => field && String(field).toLowerCase().includes(searchLower)
        );
      });
    }

    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      result = result.filter((vuln) => {
        const createdAt = new Date(vuln.createdAt);
        return createdAt >= start && createdAt <= end;
      });
    }

    // Apply sorting
    result.sort((a, b) => {
      const aValue = a[sort.field];
      const bValue = b[sort.field];

      if (sort.field === "severity") {
        const aSeverity = a.severity.toLowerCase() as VulnerabilitySeverity;
        const bSeverity = b.severity.toLowerCase() as VulnerabilitySeverity;
        return sort.direction === "asc"
          ? severityOrder[aSeverity] - severityOrder[bSeverity]
          : severityOrder[bSeverity] - severityOrder[aSeverity];
      } else if (typeof aValue === "string" && typeof bValue === "string") {
        return sort.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return sort.direction === "asc"
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sort.direction === "asc" ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    return result;
  }, [vulnerabilities, filters, sort]);

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
  };

  const value = {
    vulnerabilities,
    loading,
    error,
    filters,
    sort,
    filteredVulnerabilities,
    totalVulnerabilities: vulnerabilities.length,
    fetchVulnerabilities,
    setFilters,
    setSort,
    clearFilters,
    isOrganizationView,
  };

  return (
    <VulnerabilitiesContext.Provider value={value}>
      {children}
    </VulnerabilitiesContext.Provider>
  );
}

export function useVulnerabilities() {
  const context = useContext(VulnerabilitiesContext);
  if (context === undefined) {
    throw new Error("useVulnerabilities must be used within a VulnerabilitiesProvider");
  }
  return context;
}
