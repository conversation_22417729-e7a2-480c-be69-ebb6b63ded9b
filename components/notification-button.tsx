"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNotifications } from '@/context/NotificationsContext';
import { NotificationsPanel } from '@/components/notifications-panel';
import { cn } from '@/lib/utils';

export function NotificationButton() {
  const { unreadCount } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div ref={dropdownRef} className="relative">
      <Button
        variant="outline"
        size="icon"
        className={cn(
          "relative transition-all duration-200 dark:hover:bg-[#111827] dark:border-gray-700",
          unreadCount > 0 && "border-primary/50 bg-primary/5 shadow-sm",
          isOpen && "bg-primary/10 border-primary/60"
        )}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Bell className={cn(
          "h-[1.2rem] w-[1.2rem] transition-colors duration-200",
          unreadCount > 0 && "text-primary",
          isOpen && "text-primary"
        )} />
        {unreadCount > 0 && (
          <Badge
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary text-primary-foreground border-2 border-background animate-pulse"
            variant="default"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>
      {isOpen && (
        <div className="absolute right-0 mt-2 w-[420px] rounded-lg border bg-popover p-0 text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 z-50">
          <NotificationsPanel onClose={() => setIsOpen(false)} />
        </div>
      )}
    </div>
  );
}