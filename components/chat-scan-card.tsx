import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { formatAssetType } from "@/lib/utils/formatAssetType";
import { formatScanTitle } from "@/lib/utils/formatScanTitle";
import { ExternalLink, CheckCircle2, Clock, AlertCircle, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { useScans } from "@/context/ScanContext";
import { useNotifications } from "@/context/NotificationsContext";
import { useConversations } from "@/context/ConversationsContext";
import { usePusher } from "@/hooks/usePusher";
// Import from the client-safe events file
import { SocketEvents } from "@/lib/socket-events";
import { DownloadCardContent } from "@/lib/models/conversation";

interface ChatScanCardProps {
    scanId: string;
    assetType: string;
    target: string;
    status?: "pending" | "in-progress" | "completed" | "re-test";
    timestamp: Date | string;
    name?: string;
}

export function ChatScanCard({ scanId, assetType, target, status: initialStatus = "pending", timestamp, name: initialName }: ChatScanCardProps) {
    const router = useRouter();
    const { scans, fetchData, role } = useScans();
    const { addNotification } = useNotifications();
    const { activeConversationId, addMessageToConversation, conversations } = useConversations();
    const { subscribe, isConnected } = usePusher(); // Use Pusher hook for real-time updates

    // Format asset type for display using the utility function
    const formattedAssetType = formatAssetType(assetType);

    // State to track the current status
    const [currentStatus, setCurrentStatus] = useState<"pending" | "in-progress" | "completed" | "re-test">(initialStatus);
    const [currentName, setCurrentName] = useState<string | undefined>(initialName);

    // Use a ref to track the last status we've sent a message about
    // This prevents duplicate messages when polling
    const lastMessagedStatusRef = React.useRef<string>(initialStatus);

    // Use a ref to track if we've already sent a download card for this scan
    const downloadCardSentRef = React.useRef<boolean>(false);

    const refreshScans = React.useCallback(async () => {
        if (fetchData && role) {
            const view = role === 'client' ? 'organization' : 'personal';
            await fetchData(view, true);
        }
    }, [fetchData, role]);

    // Effect to update status when scans change
    useEffect(() => {
        // Find the scan in the scans context
        const scan = scans.find(s => s.id === scanId);
        if (scan) {
            console.log(`[ChatScanCard] Scan object found for ${scanId}:`, scan);
            if (scan.status) {
                const newStatus = scan.status as "pending" | "in-progress" | "completed" | "re-test";

                if (currentStatus !== newStatus) {
                    const oldStatus = currentStatus;
                    if (lastMessagedStatusRef.current !== newStatus) {
                        console.log(`Status change detected for pentest ${scanId}: ${oldStatus} -> ${newStatus}`);
                        lastMessagedStatusRef.current = newStatus;
                        
                        // Only add notification if this is a real status change (not initial load)
                        // Check if we've been tracking this scan for a while
                        const hasBeenTracking = lastMessagedStatusRef.current !== initialStatus;
                        
                        if (hasBeenTracking) {
                            addNotification({
                                type: 'status_change',
                                title: 'Pentest Status Updated',
                                message: `${assetType} Pentest status changed from ${oldStatus} to ${newStatus}`,
                                scanId: scanId,
                                oldStatus: oldStatus,
                                newStatus: newStatus,
                                assetType: assetType
                            });
                        }

                        if (activeConversationId) {
                            const handleStatusMessages = async () => {
                                try {
                                    if (newStatus === "completed" && !downloadCardSentRef.current) {
                                        const completedScan = scans.find(s => s.id === scanId);
                                        if (completedScan && completedScan.resultFileUrl) {
                                            const currentConversation = conversations.find(c => c.id === activeConversationId);
                                            if (currentConversation) {
                                                const existingDownloadCard = currentConversation.messages.some(msg => {
                                                    if (typeof msg.content === "object" && msg.content !== null && 'isDownloadCard' in msg.content) {
                                                        return msg.role === "system" &&
                                                            (msg.content as DownloadCardContent).isDownloadCard === true &&
                                                            (msg.content as DownloadCardContent).scanId === scanId;
                                                    }
                                                    return false;
                                                });
                                                if (!existingDownloadCard) {
                                                    console.log(`Adding download card for pentest ${scanId}`);
                                                    downloadCardSentRef.current = true;
                                                    await addMessageToConversation({
                                                        role: "system",
                                                        content: {
                                                            isDownloadCard: true,
                                                            scanId: scanId,
                                                            assetType: assetType,
                                                            reportUrl: completedScan.resultFileUrl,
                                                            fileName: `${formattedAssetType}_Pentest_Report.pdf`
                                                        }
                                                    });
                                                } else {
                                                    console.log(`Download card for Pentest ${scanId} already exists, skipping`);
                                                    downloadCardSentRef.current = true;
                                                }
                                            } else {
                                                console.warn(`Could not find active conversation ${activeConversationId} to check for existing download cards`);
                                            }
                                        }
                                    }
                                    setTimeout(() => {
                                        const messagesEnd = document.getElementById('messagesEndRef');
                                        if (messagesEnd) {
                                            messagesEnd.scrollIntoView({ behavior: 'smooth' });
                                        }
                                        setTimeout(() => {
                                            const messagesEndRetry = document.getElementById('messagesEndRef');
                                            if (messagesEndRetry) {
                                                messagesEndRetry.scrollIntoView({ behavior: 'smooth' });
                                            }
                                        }, 500);
                                    }, 100);
                                } catch (error) {
                                    console.error("Error adding messages to conversation:", error);
                                }
                            };
                            handleStatusMessages();
                        } else {
                            lastMessagedStatusRef.current = newStatus;
                        }
                    }
                    setCurrentStatus(newStatus);
                }
            }
            if (scan.name && scan.name !== currentName) {
                console.log(`[ChatScanCard] Updating name for ${scanId}: from ${currentName} to ${scan.name}`);
                setCurrentName(scan.name);
            }
        }
    }, [scans, scanId, currentStatus, assetType, addNotification, activeConversationId, addMessageToConversation, formattedAssetType, currentName, conversations]);

    // Use a ref to track if we've already done the initial fetch
    // This prevents duplicate fetches when the component remounts due to page refresh
    const initialFetchDoneRef = React.useRef<boolean>(false);

    // Set up WebSocket listeners for real-time scan updates
    useEffect(() => {
        // Initial fetch to get the latest scan data - only once when component mounts
        // and only if we haven't already done it in this session
        if (!initialFetchDoneRef.current) {
            const initialFetch = async () => {
                console.log(`Initial fetch for pentest ${scanId}`);
                await refreshScans();
                initialFetchDoneRef.current = true;
            };
            initialFetch();
        } else {
            console.log(`Skipping initial fetch for pentest ${scanId} - already done`);
        }

        // Only set up WebSocket listeners if we're connected and the scan is not completed
        if (isConnected && currentStatus !== "completed") {
            console.log(`Setting up WebSocket listeners for pentest ${scanId}`);

            // Listen for scan updates
            const unsubscribe = subscribe(SocketEvents.SCAN_UPDATED, (scanData: any) => {
                // Only process updates for this specific scan
                if (scanData.id === scanId) {
                    console.log(`Processing WebSocket update for pentest ${scanId}`);
                    // The status change will be handled by the useEffect that watches scans
                    // We don't need to call fetchScans() here as the WebSocket should provide the updated data
                }
            });

            // Clean up subscription when component unmounts
            return unsubscribe;
        } else if (!isConnected && currentStatus !== "completed") {
            // Fallback to polling if WebSockets are not connected
            console.log(`WebSocket not connected, using polling fallback for pentest ${scanId}`);

            // Set up interval for polling (every 5 minutes to reduce API calls)
            // Using a longer interval reduces the chance of duplicate updates
            const intervalId = setInterval(() => {
                console.log(`Polling for pentest ${scanId} updates`);
                refreshScans();
            }, 300000); // 5 minutes

            // Clean up interval when component unmounts
            return () => clearInterval(intervalId);
        }

        // Return empty cleanup function for completed scans
        return () => {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [scanId, isConnected]); // Remove currentStatus dependency to prevent re-subscribing on status changes

    // Format timestamp
    const formattedDate = typeof timestamp === 'string'
        ? format(new Date(timestamp), 'MMM d, yyyy HH:mm')
        : format(timestamp, 'MMM d, yyyy HH:mm');

    // Get status badge color
    const getStatusColor = () => {
        switch (currentStatus) {
            case "pending": return "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400";
            case "in-progress": return "bg-blue-500/20 text-blue-600 dark:text-blue-400";
            case "re-test": return "bg-purple-500/20 text-purple-600 dark:text-purple-400";
            case "completed": return "bg-green-500/20 text-green-600 dark:text-green-400";
            default: return "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400";
        }
    };

    // Get status icon
    const getStatusIcon = () => {
        switch (currentStatus) {
            case "pending": return <Clock className="h-3.5 w-3.5" />;
            case "in-progress": return <AlertCircle className="h-3.5 w-3.5" />;
            case "re-test": return <RefreshCw className="h-3.5 w-3.5" />;
            case "completed": return <CheckCircle2 className="h-3.5 w-3.5" />;
            default: return <Clock className="h-3.5 w-3.5" />;
        }
    };

    // Handle view scan details
    const handleViewScan = () => {
        // Navigate to the specific scan if possible
        router.push(`/scans?id=${scanId}`);
    };

    return (
        <Card className="w-full max-w-[800px] bg-background/50 border shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 pt-4">
                <div className="flex items-center justify-between mb-2 space-x-2">
                    <Badge
                        variant="outline"
                        className={cn("px-2 py-1 mb-2 text-xs flex items-center gap-2", getStatusColor())}
                    >
                        {getStatusIcon()}
                        <span>{currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}</span>
                    </Badge>
                </div>
                <CardTitle className="text-md font-medium"> {/* Changed from text-base to text-sm */}
                    {/* Try to find the scan in the scans context to get the conversation title */}
                    {(() => {
                        const scan = scans.find(s => s.id === scanId);

                        if (scan) {
                            // Use the formatScanTitle utility function for consistent title formatting
                            return formatScanTitle(scan);
                        }

                        // If scan not found in context yet, create a temporary scan object with available props
                        const tempScan = {
                            asset_type: assetType,
                            target: target,
                            name: currentName
                        };
                        // console.log("[ChatScanCard] Initial tempScan for formatScanTitle:", tempScan);
                        return formatScanTitle(tempScan);
                    })()}
                </CardTitle>
                <p className="text-xs text-muted-foreground mb-4">
                    Requested on {formattedDate}
                </p>
            </CardHeader>
            <CardContent className="pb-4">
                <div className="space-y-5">
                    <div className="grid grid-cols-2 gap-8 text-sm">
                        <div>
                            <p className="text-xs text-muted-foreground">Target</p>
                            <p className="font-medium truncate">{target || 'N/A'}</p>
                        </div>
                        <div>
                            <p className="text-xs text-muted-foreground">Asset Type</p>
                            <p className="font-medium truncate">{formattedAssetType}</p>
                        </div>
                    </div>

                    <Button
                        variant="outline"
                        size="sm"
                        className="w-full mt-3 text-xs h-8"
                        onClick={handleViewScan}
                    >
                        <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                        View Details
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
