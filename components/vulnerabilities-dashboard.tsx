"use client";

import { useState, Dispatch, SetStateAction, useCallback, memo, FC, ReactNode, useMemo, useDeferredValue, useTransition } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Loader2 } from "lucide-react";
import { VulnerabilityCard } from "./vulnerability-card";
import { VulnerabilityDetailModal } from "./vulnerability-detail-modal";
import { SelectionControls, useSelectionKeyboard } from "@/components/ui/selection-controls";
import { Vulnerability, VulnerabilitySeverity } from "@/types/vulnerability-types";
import { useVulnerabilities } from "@/context/VulnerabilitiesContext";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";

// --- Sub-Components (Extracted for Performance and Readability) ---

/**
 * A memoized component to display when no vulnerabilities match the current filters.
 */
const NoResults: FC<{ hasFilters: boolean; onClearFilters: () => void; }> = memo(({ hasFilters, onClearFilters }) => (
  <Card className="w-full border-border/50 shadow-sm">
    <CardContent className="flex flex-col items-center justify-center py-16 text-center">
      <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-6">
        <AlertCircle className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-xl font-semibold mb-2 text-foreground">No vulnerabilities found.</h3>
      <p className="text-muted-foreground max-w-md mb-8 leading-relaxed">
        {hasFilters
          ? "Try adjusting your search terms or filters to find what you're looking for."
          : "No vulnerabilities have been identified in your completed scan reports yet. Run a pentest to get started."}
      </p>
      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          hasFilters ? "max-h-screen opacity-100 mt-8" : "max-h-0 opacity-0"
        }`}
      >
        {hasFilters && (
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="hover:bg-violet/10 hover:border-violet/30 hover:text-violet transition-all duration-300"
          >
            Clear All Filters
          </Button>
        )}
      </div>
    </CardContent>
  </Card>
));
NoResults.displayName = "NoResults";

/**
 * A memoized component for handling bulk actions on selected vulnerabilities.
 * It manages its own internal state for the note.
 */
const BulkActionsPanel: FC<{
  selectedCount: number;
  isSubmitting: boolean;
  onBulkUpdate: (status: Vulnerability['status'], note: string) => void;
  role: string | null | undefined;
}> = memo(({ selectedCount, isSubmitting, onBulkUpdate, role }) => {
  const [note, setNote] = useState("");

  const handleUpdate = (status: Vulnerability['status']) => {
    onBulkUpdate(status, note);
  };

  return (
    <div className="p-6 ml-10 rounded-xl space-y-4 border border-violet/20 bg-gradient-to-r from-violet/5 via-violet/10 to-violet/5 backdrop-blur-sm shadow-md" id="bulk-actions-panel">
      <div className="flex items-center justify-between">
        <h3 className="text-md font-semibold text-foreground flex items-center gap-3">
          <span className="w-3 h-3 bg-violet rounded-full animate-pulse shadow-sm"></span>
          <span>Requesting Retest</span>
        </h3>
        <span className="px-2 py-1 bg-violet/15 text-violet rounded-full font-normal text-sm shadow-sm border border-violet/20">
          {selectedCount} selected
        </span>
      </div>
      
      <Textarea
        value={note}
        onChange={(e) => setNote(e.target.value)}
        placeholder={role !== "admin" ? "Add a note for the retest (required)..." : "Add a note (optional)..."}
        className="resize-none bg-background/50 border-border/50 focus:border-violet-50 focus:ring-violet-200 transition-all duration-300"
      />
      
      <div className="flex flex-col sm:flex-row gap-3">
        {role !== "admin" && (
          <Button 
            onClick={() => handleUpdate("Pending Retest")} 
            disabled={isSubmitting || !note} 
            className={`shadow-md shadow-violet-300 bg-violet text-white transition-all duration-300 ${
              note ? 'scale-[1.01] hover:scale-105 hover:bg-violet-600' : ''
            }`}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin duration-300" />}
            Submit for Retest
          </Button>
        )}
        {role === "admin" && (
          <>
            <Button onClick={() => handleUpdate("Open")} disabled={isSubmitting} variant="outline" className="flex-1 hover:bg-blue-50 hover:border-blue-300">
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Mark as Open
            </Button>
            <Button onClick={() => handleUpdate("Closed")} disabled={isSubmitting} className="flex-1 bg-green-600 hover:bg-green-700">
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Mark as Closed
            </Button>
          </>
        )}
      </div>
    </div>
  );
});
BulkActionsPanel.displayName = "BulkActionsPanel";


// --- Main Dashboard Component ---

interface VulnerabilitiesDashboardProps {
  searchQuery: string;
  setSearchQuery: Dispatch<SetStateAction<string>>;
  clearFilters: () => void;
}

export function VulnerabilitiesDashboard({
  searchQuery,
  setSearchQuery,
  clearFilters,
}: VulnerabilitiesDashboardProps) {
  const { vulnerabilities, loading, error, filteredVulnerabilities, fetchVulnerabilities } = useVulnerabilities();
  const { user, role } = useAuth();
  const { toast } = useToast();

  const [selectedVulnerability, setSelectedVulnerability] = useState<Vulnerability | null>(null);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [visibleCount, setVisibleCount] = useState(20);
  const [isPending, startTransition] = useTransition();
  
  // Defer heavy computations
  const deferredFilteredVulnerabilities = useDeferredValue(filteredVulnerabilities);
  
  // Progressive loading
  const visibleVulnerabilities = useMemo(() => 
    deferredFilteredVulnerabilities.slice(0, visibleCount), 
    [deferredFilteredVulnerabilities, visibleCount]
  );
  
  const loadMore = useCallback(() => {
    startTransition(() => {
      setVisibleCount(prev => Math.min(prev + 20, deferredFilteredVulnerabilities.length));
    });
  }, [deferredFilteredVulnerabilities.length]);

  const handleOpenDetailModal = useCallback((vulnerability: Vulnerability) => {
    setSelectedVulnerability(vulnerability);
  }, []);

  const handleCloseDetailModal = useCallback(() => {
    setSelectedVulnerability(null);
  }, []);

  const handleSelectionChange = useCallback((vulnerabilityId: string) => {
    setSelectedIds((prev) =>
      prev.includes(vulnerabilityId)
        ? prev.filter((id) => id !== vulnerabilityId)
        : [...prev, vulnerabilityId]
    );
  }, []);

  const handleSelectAll = useCallback(() => setSelectedIds(deferredFilteredVulnerabilities.map(v => v.id)), [deferredFilteredVulnerabilities]);
  const handleSelectNone = useCallback(() => setSelectedIds([]), []);

  useSelectionKeyboard(handleSelectAll, handleSelectNone, isSubmitting);

  const handleBulkUpdate = useCallback(async (status: Vulnerability['status'], note: string) => {
    if (!user || selectedIds.length === 0) return;

    if (status === "Pending Retest" && !note) {
      toast({ title: 'Note Required', description: 'A note is required when submitting for retest.', variant: 'destructive' });
      return;
    }

    setIsSubmitting(true);
    try {
      const idToken = await user.getIdToken();
      const response = await fetch('/api/vulnerabilities/bulk-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({ vulnerabilityIds: selectedIds, status, note }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update vulnerabilities`);
      }

      toast({ title: 'Vulnerabilities Updated', description: `${selectedIds.length} vulnerabilities have been updated to ${status}.` });
      setSelectedIds([]);
      fetchVulnerabilities();
    } catch (err: any) {
      toast({ title: 'Error', description: err.message || 'Failed to update vulnerabilities.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  }, [user, selectedIds, fetchVulnerabilities, toast]);

  const handleStatusChange = useCallback(async (id: string, status: Vulnerability['status'], note?: string) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      const idToken = await user.getIdToken();
      const response = await fetch('/api/vulnerabilities/bulk-update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({ vulnerabilityIds: [id], status, note }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update vulnerability`);
      }

      toast({ title: 'Vulnerability Updated', description: `The vulnerability status has been changed to ${status}.` });
      fetchVulnerabilities();
    } catch (err: any) {
      toast({ title: 'Error', description: err.message || 'Failed to update vulnerability.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  }, [user, fetchVulnerabilities, toast]);

  const handleRequestRetest = useCallback((vulnerability: Vulnerability) => {
    if (!selectedIds.includes(vulnerability.id)) {
      setSelectedIds(prev => [...prev, vulnerability.id]);
    }
    setTimeout(() => {
      document.getElementById('bulk-actions-panel')?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);
  }, [selectedIds]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <AlertCircle className="h-8 w-8 text-destructive mb-4" />
        <p className="text-destructive font-medium mb-2">Error Loading Vulnerabilities</p>
        <p className="text-muted-foreground">{error}</p>
        <Button variant="outline" className="mt-4" onClick={() => fetchVulnerabilities()}>Try Again</Button>
      </div>
    );
  }

  const hasActiveFilters = !!searchQuery || (vulnerabilities.length > 0 && filteredVulnerabilities.length !== vulnerabilities.length);

  return (
    <div className="space-y-6  w-full 2xl:max-w-7xl mx-auto">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4 bg-card/20 backdrop-blur-sm border border-border/30 rounded-lg">
          <div className="text-sm font-medium text-foreground">
            Showing <span className="font-semibold bg-gray-200 dark:bg-violet-900 px-2 py-1 mx-1 rounded-full">{deferredFilteredVulnerabilities.length}</span> of{' '}
            <span className="font-semibold">{vulnerabilities.length}</span> findings.
          </div>
          {deferredFilteredVulnerabilities.length > 0 && (
            <SelectionControls
              totalCount={deferredFilteredVulnerabilities.length}
              selectedCount={selectedIds.length}
              onSelectAll={handleSelectAll}
              onSelectNone={handleSelectNone}
              disabled={isSubmitting}
            />
          )}
        </div>

        {selectedIds.length > 0 && (
          <BulkActionsPanel
            selectedCount={selectedIds.length}
            isSubmitting={isSubmitting}
            onBulkUpdate={handleBulkUpdate}
            role={role}
          />
        )}

        {deferredFilteredVulnerabilities.length > 0 ? (
          <div className="space-y-4">
            {visibleVulnerabilities.map((vulnerability, index) => (
              <div 
                key={vulnerability.id} 
                className="animate-fadeIn" 
                style={{ animationDelay: `${Math.min(index * 25, 250)}ms` }}
              >
                <VulnerabilityCard
                  vulnerability={vulnerability}
                  onClick={() => handleOpenDetailModal(vulnerability)}
                  onStatusChange={handleStatusChange}
                  onRequestRetest={handleRequestRetest}
                  isSelected={selectedIds.includes(vulnerability.id)}
                  onSelectionChange={handleSelectionChange}
                  role={role}
                  showCheckbox={true}
                />
              </div>
            ))}
            {visibleCount < deferredFilteredVulnerabilities.length && (
              <div className="flex justify-center pt-6">
                <button
                  onClick={loadMore}
                  disabled={isPending}
                  className="px-6 py-3 bg-primary/10 hover:bg-primary/20 text-primary rounded-full transition-all duration-200 disabled:opacity-50"
                >
                  {isPending ? (
                    <><Loader2 className="h-4 w-4 mr-2 animate-spin inline" /> Loading...</>
                  ) : (
                    `Load More (${deferredFilteredVulnerabilities.length - visibleCount} remaining)`
                  )}
                </button>
              </div>
            )}
          </div>
        ) : (
          <NoResults hasFilters={hasActiveFilters} onClearFilters={() => { setSearchQuery(""); clearFilters(); }} />
        )}
      </div>

      {selectedVulnerability && (
        <VulnerabilityDetailModal
          vulnerability={selectedVulnerability}
          isOpen={!!selectedVulnerability}
          onClose={handleCloseDetailModal}
        />
      )}
    </div>
  );
}