"use client"

import React from 'react';
import { useNotifications, Notification } from '@/context/NotificationsContext';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format } from 'date-fns';
import { Bell, CheckCircle2, Clock, AlertCircle, Refresh<PERSON>w, Trash2, CheckCheck, Shield } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface NotificationsPanelProps {
  onClose: () => void;
}

export function NotificationsPanel({ onClose }: NotificationsPanelProps) {
  const { notifications, unreadCount, markAsRead, markAllAsRead, clearNotifications } = useNotifications();
  const router = useRouter();

  // Function to get status icon
  const getStatusIcon = (status: string | undefined) => {
    switch (status) {
      case "pending": return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in-progress": return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case "re-test": return <RefreshCw className="h-4 w-4 text-purple-500" />;
      case "completed": return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  // Function to get status color
  const getStatusColor = (status: string | undefined) => {
    switch (status) {
      case "pending": return "text-yellow-500 bg-yellow-500/10";
      case "in-progress": return "text-blue-500 bg-blue-500/10";
      case "re-test": return "text-purple-500 bg-purple-500/10";
      case "completed": return "text-green-500 bg-green-500/10";
      default: return "text-primary bg-primary/10";
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    markAsRead(notification.id);

    // Close the panel
    onClose();

    // Navigate to scan if scanId is available
    if (notification.scanId) {
      router.push(`/scans?id=${notification.scanId}`);
    }
  };

  return (
    <div className="w-full bg-background/95 backdrop-blur-sm border border-border/50 rounded-xl shadow-2xl overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-border/50 bg-gradient-to-r from-background to-muted/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Bell className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">Notifications</h3>
              <p className="text-sm text-muted-foreground">
                {unreadCount > 0
                  ? `${unreadCount} unread notification${unreadCount !== 1 ? 's' : ''}`
                  : 'All caught up!'}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
              className="h-10 w-10 text-xs hover:bg-primary/10 hover:text-primary transition-colors"
            >
              <CheckCheck className="h-3.5 w-3.5 mr-1.5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearNotifications}
              disabled={notifications.length === 0}
              className="h-10 w-10 text-xs hover:bg-destructive/10 hover:text-destructive transition-colors"
            >
              <Trash2 className="h-3.5 w-3.5 mr-1.5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-h-[500px] overflow-hidden">
        {notifications.length === 0 ? (
          <div className="text-center py-12 px-6">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/30 flex items-center justify-center">
              <Bell className="h-8 w-8 text-muted-foreground/50" />
            </div>
            <h4 className="font-medium text-foreground mb-2">No notifications yet</h4>
            <p className="text-sm text-muted-foreground">
              We'll notify you when something important happens
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[480px]">
            <div className="p-2">
              {notifications.map((notification, index) => (
                <div
                  key={notification.id}
                  className={cn(
                    "group relative p-4 m-2 rounded-xl cursor-pointer transition-all duration-200",
                    "border border-transparent hover:border-border/50",
                    notification.read
                      ? "bg-background hover:bg-muted/30"
                      : "bg-gradient-to-r from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15",
                    "hover:shadow-md hover:scale-[1.02] transform"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-colors",
                      notification.type === 'status_change'
                        ? getStatusColor(notification.newStatus)
                        : notification.type === 'new_scan'
                        ? "bg-blue-500/20 text-blue-600 dark:text-blue-400"
                        : "bg-primary/20 text-primary"
                    )}>
                      {notification.type === 'status_change'
                        ? getStatusIcon(notification.newStatus)
                        : notification.type === 'new_scan'
                        ? <Shield className="h-4 w-4" />
                        : <Bell className="h-4 w-4" />
                      }
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <h4 className="font-semibold text-foreground text-sm leading-tight">
                          {notification.title}
                        </h4>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          {!notification.read && (
                            <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
                          )}
                          <span className="text-xs text-muted-foreground whitespace-nowrap">
                            {format(notification.timestamp, 'MMM d, HH:mm')}
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground leading-relaxed mb-3">
                        {notification.message}
                      </p>
                      
                      {notification.type === 'status_change' && notification.oldStatus && notification.newStatus && (
                        <div className="flex items-center gap-2 p-2 bg-muted/30 rounded-lg">
                          <span className="text-xs text-muted-foreground">Status:</span>
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              "flex items-center gap-1 px-2 py-1 rounded-md text-xs",
                              getStatusColor(notification.oldStatus)
                            )}>
                              {getStatusIcon(notification.oldStatus)}
                              <span className="capitalize">{notification.oldStatus}</span>
                            </div>
                            <span className="text-muted-foreground">→</span>
                            <div className={cn(
                              "flex items-center gap-1 px-2 py-1 rounded-md text-xs",
                              getStatusColor(notification.newStatus)
                            )}>
                              {getStatusIcon(notification.newStatus)}
                              <span className="capitalize">{notification.newStatus}</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Hover indicator */}
                  <div className="absolute inset-0 rounded-xl border-2 border-primary/20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </div>
      
      {/* Footer */}
      {notifications.length > 0 && (
        <div className="px-6 py-3 border-t border-border/50 bg-muted/20">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              onClose();
              router.push('/notifications');
            }}
            className="w-full h-8 text-xs text-muted-foreground hover:text-foreground transition-colors"
          >
            View all notifications
          </Button>
        </div>
      )}
    </div>
  );
}
