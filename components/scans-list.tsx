"use client";

import React, { useState, useEffect, useMemo, useCallback, useTransition, memo, FC, useDeferredValue, Suspense } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScanCardModal } from "@/components/scan-card-modal";
import { But<PERSON> } from "@/components/ui/button";
import { GroupedScansList } from "@/components/grouped-scans-list";
import { Badge } from "@/components/ui/badge";
import { OrganizationGroupedScansList } from "@/components/organization-grouped-scans-list";
import { Input } from "@/components/ui/input";
import { ToggleSwitch } from "@/components/ui/toggle-switch";
import { Search, Plus, Clock, CheckCircle2, AlertCircle, RefreshCw, Loader2, Shield, X } from "lucide-react";
import { useScans } from "@/context/ScanContext";
import { useAuth } from "@/hooks/useAuth";
import { useConversations } from "@/context/ConversationsContext";
import { TooltipProvider } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { ScanType } from "@/types/scan-types";
import { debounce } from "@/lib/scan-utils";
import "@/styles/pentests-optimized.css";
import "@/styles/enhanced-scan-design.css";

// --- Helper Functions & Types ---

// Pure helper function, defined outside any component for performance.
const scanMatchesQuery = (scan: ScanType, query: string): boolean => {
    if (!query) return true;
    const lowerQuery = query.toLowerCase();
    
    // Check most common fields first for early return
    if (scan.name?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.target?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.status?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.asset_type?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.title?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.aiSummary?.summary?.toLowerCase().includes(lowerQuery)) return true;
    
    return false;
};

// --- Custom Hook for Data Processing ---

const useProcessedScans = (
    scans: ScanType[],
    groupedUsers: any[], // Replace with actual UserGroup type
    organizationData: any, // Replace with actual OrgData type
    role: string | null | undefined,
    isOrganizationView: boolean,
    debouncedSearchQuery: string
) => {
    const query = useMemo(() => debouncedSearchQuery.toLowerCase().trim(), [debouncedSearchQuery]);

    const filteredScans = useMemo(() => {
        if (!query) return scans;
        return scans.filter(scan => scanMatchesQuery(scan, query));
    }, [scans, query]);

    const { pendingScans, inProgressScans, reTestScans, completedScans } = useMemo(() => {
        const result: { pending: ScanType[], 'in-progress': ScanType[], 're-test': ScanType[], completed: ScanType[] } = {
            pending: [], 'in-progress': [], 're-test': [], completed: []
        };
        filteredScans.forEach(scan => {
            if (scan.status && result[scan.status as keyof typeof result]) {
                result[scan.status as keyof typeof result].push(scan);
            }
        });
        return {
            pendingScans: result.pending,
            inProgressScans: result['in-progress'],
            reTestScans: result['re-test'],
            completedScans: result.completed,
        };
    }, [filteredScans]);

    const filteredGroupedUsers = useMemo(() => {
        if (!groupedUsers?.length || !query) return groupedUsers;
        return groupedUsers.map(user => {
            const matchingScans = user.scans.filter((scan: ScanType) => scanMatchesQuery(scan, query));
            return matchingScans.length > 0 ? { ...user, scans: matchingScans } : null;
        }).filter(Boolean);
    }, [groupedUsers, query]);

    const filteredOrganizations = useMemo(() => {
        if (!organizationData) return [];
        
        // Handle both array format (from admin API) and object format (from client API)
        const orgsArray = Array.isArray(organizationData) ? organizationData : Object.values(organizationData);
        
        const orgs = orgsArray.map((org: any) => {
            // Check if org and org.users exist
            if (!org || !org.users || !Array.isArray(org.users)) {
                console.warn('Invalid organization data structure:', org);
                return null;
            }
            
            const filteredUsers = org.users.map((user: any) => {
                // Check if user and user.scans exist
                if (!user || !user.scans || !Array.isArray(user.scans)) {
                    console.warn('Invalid user data structure:', user);
                    return null;
                }
                
                const matchingScans = user.scans.filter((scan: ScanType) => scanMatchesQuery(scan, query));
                return matchingScans.length > 0 ? { ...user, scans: matchingScans } : null;
            }).filter(Boolean);

            if (filteredUsers.length > 0) {
                return { ...org, users: filteredUsers };
            }
            return null;
        }).filter(Boolean);
        return orgs;
    }, [organizationData, query]);

    return {
        allScans: filteredScans,
        pendingScans,
        inProgressScans,
        reTestScans,
        completedScans,
        filteredGroupedUsers,
        filteredOrganizations,
    };
};

// --- Reusable Presentational Components ---

const ProgressiveRenderScanList: FC<{ scans: ScanType[]; emptyMessage: string; onStatusChange: () => void; loading: boolean; }> = memo(({ scans, emptyMessage, onStatusChange, loading }) => {
    const [visibleCount, setVisibleCount] = useState(20);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    
    const loadMore = useCallback(() => {
        setIsLoadingMore(true);
        requestAnimationFrame(() => {
            setVisibleCount(prev => Math.min(prev + 20, scans.length));
            setIsLoadingMore(false);
        });
    }, [scans.length]);

    useEffect(() => {
        setVisibleCount(20);
    }, [scans]);
    
    // Intersection Observer for auto-loading
    const observerRef = useCallback((node: HTMLDivElement | null) => {
        if (!node || isLoadingMore || visibleCount >= scans.length) return;
        
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    loadMore();
                }
            },
            { threshold: 0.1 }
        );
        
        observer.observe(node);
        return () => observer.disconnect();
    }, [loadMore, isLoadingMore, visibleCount, scans.length]);

    const visibleScans = useMemo(() => scans.slice(0, visibleCount), [scans, visibleCount]);

    if (loading) {
        return <div className="grid grid-cols-1 gap-6">{Array.from({ length: 6 }).map((_, i) => <div key={i} className="h-56 rounded-xl bg-gradient-to-br from-muted/50 to-muted/30 animate-pulse border border-border/30" />)}</div>;
    }
    if (scans.length === 0) {
        return (
            <Card className="bg-gradient-to-br from-card via-card to-card/95 border border-border/50 rounded-xl">
                <CardContent className="py-16 text-center">
                    <div className="flex flex-col items-center space-y-4">
                        <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center border border-border/50">
                            <Shield className="h-8 w-8 text-muted-foreground/50" />
                        </div>
                        <div className="space-y-2">
                            <p className="text-lg font-medium text-muted-foreground">{emptyMessage}</p>
                            <p className="text-sm text-muted-foreground/60">Pentests will appear here when available</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }
    
    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
                {visibleScans.map((scan, index) => (
                    <div key={scan.id} className="animate-fadeIn" style={{ animationDelay: `${Math.min(index * 25, 250)}ms` }}>
                        <Suspense fallback={<div className="h-56 rounded-xl bg-gradient-to-br from-muted/50 to-muted/30 animate-pulse border border-border/30" />}>
                            <ScanCardModal scan={scan} onStatusChange={onStatusChange} />
                        </Suspense>
                    </div>
                ))}
            </div>
            {visibleCount < scans.length && (
                <div ref={observerRef} className="flex justify-center pt-4">
                    <Button variant="outline" onClick={loadMore} disabled={isLoadingMore} className="h-12 px-6 rounded-xl bg-gradient-to-r from-background to-background/95 border-border/50 hover:border-primary/50 shadow-sm transition-all duration-200">
                        {isLoadingMore ? (
                            <><Loader2 className="h-5 w-5 mr-2 animate-spin" /> <span className="font-medium">Loading...</span></>
                        ) : (
                            <span className="font-medium">`Load More (${scans.length - visibleCount} remaining)`</span>
                        )}
                    </Button>
                </div>
            )}
        </div>
    );
});
ProgressiveRenderScanList.displayName = 'ProgressiveRenderScanList';

const ScanListDisplay: FC<any> = memo(({ scans, groupedUsers, organizations, role, isGroupedView, isOrganizationView, loading, error, onStatusChange, activeTab }) => {
    if (error) return <div className="text-red-500 p-4">Error: {error}</div>;

    // For admin users, show organization view if we have organization data
    if (role === 'admin' && organizations && organizations.length > 0) {
        return <OrganizationGroupedScansList organizations={organizations} onStatusChange={onStatusChange} emptyMessage="No organization scans found." activeTab={activeTab} />;
    }
    
    // For admin users without organization data, show grouped user view
    if (role === 'admin' && isGroupedView) {
        return <GroupedScansList users={groupedUsers} onStatusChange={onStatusChange} emptyMessage="No user scans found." activeTab={activeTab} />;
    }

    // For client users with organization view enabled
    if (role === 'client' && isOrganizationView && organizations && organizations.length > 0) {
        return <OrganizationGroupedScansList organizations={organizations} onStatusChange={onStatusChange} emptyMessage="No organization scans found." activeTab={activeTab} />;
    }

    // For client users with grouped view
    if (role === 'client' && isGroupedView) {
        return <GroupedScansList users={groupedUsers} onStatusChange={onStatusChange} emptyMessage="No user scans found." activeTab={activeTab} />;
    }

    // Default: show individual scans
    return <ProgressiveRenderScanList scans={scans} emptyMessage="No scans found." onStatusChange={onStatusChange} loading={loading} />;
});
ScanListDisplay.displayName = 'ScanListDisplay';

// --- Main Component ---

export function ScansList() {
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
    const [isPending, startTransition] = useTransition();
    const { role } = useAuth();
    const { scans, loading, error, fetchData, groupedUsers, isGroupedView, isOrganizationView, organizationData } = useScans();
    
    // Defer heavy computations
    const deferredScans = useDeferredValue(scans);
    const deferredGroupedUsers = useDeferredValue(groupedUsers);
    const deferredOrganizationData = useDeferredValue(organizationData);
    const { createNewConversation } = useConversations();
    const [isCreatingConversation, setIsCreatingConversation] = useState(false);
    const [activeTab, setActiveTab] = useState("all");

    // Removed automatic fetch on role change to prevent conflicts with manual toggle

    const debouncedSetSearch = useCallback(debounce((query: string) => {
        startTransition(() => setDebouncedSearchQuery(query));
    }, 200), []);

    useEffect(() => {
        debouncedSetSearch(searchQuery);
    }, [searchQuery, debouncedSetSearch]);

    const { allScans, pendingScans, inProgressScans, reTestScans, completedScans, filteredGroupedUsers, filteredOrganizations } = useProcessedScans(
        deferredScans, deferredGroupedUsers, deferredOrganizationData, role, isOrganizationView, debouncedSearchQuery
    );

    const handleViewSwitch = useCallback(() => {
        const newView = isOrganizationView ? 'personal' : 'organization';
        console.log('Switching to view:', newView, 'from isOrganizationView:', isOrganizationView);
        fetchData(newView, true); // Force refresh when switching views
    }, [isOrganizationView, fetchData]);

    const handleNewScanClick = async () => {
        if (isCreatingConversation) return;
        setIsCreatingConversation(true);
        try {
            await createNewConversation();
            router.push('/');
        } catch (err) {
            toast.error("Failed to create new conversation");
        } finally {
            setIsCreatingConversation(false);
        }
    };

    const handleStatusChange = useCallback(() => {
        fetchData(isOrganizationView ? 'organization' : 'personal');
    }, [isOrganizationView, fetchData]);

    const renderScanList = (scansToShow: ScanType[], currentTab: string) => (
        <ScanListDisplay
            scans={scansToShow}
            groupedUsers={filteredGroupedUsers}
            organizations={filteredOrganizations}
            role={role}
            isGroupedView={isGroupedView}
            isOrganizationView={isOrganizationView}
            loading={loading}
            error={error}
            onStatusChange={handleStatusChange}
            activeTab={currentTab}
        />
    );

    return (
        <TooltipProvider>
            <div className="pentests-container p-6 2xl:px-0 w-full 2xl:max-w-7xl mx-auto">
                <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
                    <div className="flex items-center gap-4">
                        <div className="p-2 rounded-xl bg-primary/10 border border-primary/20 flex items-center justify-center">
                            <Shield className="h-6 w-6 text-primary" />
                        </div>
                        <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                            {role === "admin" ? "All Client Pentests" : "Your Pentests"}
                        </h2>
                    </div>
                    {role !== "admin" && (
                        <Button onClick={handleNewScanClick} disabled={isCreatingConversation} className="h-12 px-6 rounded-full bg-gradient-to-r from-[#843DF5] to-[#9333EA] text-white hover:from-[#7C3AED] hover:to-[#8B5CF6] shadow-lg hover:shadow-xl shadow-violet-300/50 transition-all duration-300 font-semibold">
                            {isCreatingConversation ? <Loader2 className="h-5 w-5 mr-2 animate-spin" /> : <Plus className="h-5 w-5 mr-2" />}
                            New Request
                        </Button>
                    )}
                </div>

                <Tabs defaultValue="all" onValueChange={setActiveTab}>
                    <div className="flex items-center justify-between gap-6 mb-8">
                        <div className="flex items-center gap-6">
                            {role === 'client' && <ToggleSwitch checked={isOrganizationView} onCheckedChange={handleViewSwitch} leftLabel="Personal" rightLabel="Organization" />}
                            <TabsList className="h-12 p-1 bg-gradient-to-r from-muted/50 to-muted/30 rounded-xl shadow-sm">
                                <TabsTrigger value="all" className="px-4 py-2 rounded-lg font-medium">All <Badge variant="outline" className="ml-2 bg-gradient-to-r from-slate-100 to-gray-100 dark:from-slate-800 dark:to-gray-800 text-slate-700 dark:text-slate-300 border-slate-300 dark:border-slate-600">{allScans.length}</Badge></TabsTrigger>
                                <TabsTrigger value="pending" className="px-4 py-2 rounded-lg font-medium"><Clock className="h-4 w-4 text-yellow-500 mr-2" /> Pending <Badge variant="outline" className="ml-2 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 text-yellow-700 dark:text-yellow-400 border-yellow-300 dark:border-yellow-700">{pendingScans.length}</Badge></TabsTrigger>
                                <TabsTrigger value="in-progress" className="px-4 py-2 rounded-lg font-medium"><AlertCircle className="h-4 w-4 text-blue-500 mr-2" /> In Progress <Badge variant="outline" className="ml-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-400 border-blue-300 dark:border-blue-700">{inProgressScans.length}</Badge></TabsTrigger>
                                <TabsTrigger value="re-test" className="px-4 py-2 rounded-lg font-medium"><RefreshCw className="h-4 w-4 text-purple-500 mr-2" /> Re-test <Badge variant="outline" className="ml-2 bg-gradient-to-r from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 text-purple-700 dark:text-purple-400 border-purple-300 dark:border-purple-700">{reTestScans.length}</Badge></TabsTrigger>
                                <TabsTrigger value="completed" className="px-4 py-2 rounded-lg font-medium"><CheckCircle2 className="h-4 w-4 text-green-500 mr-2" /> Completed <Badge variant="outline" className="ml-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-400 border-green-300 dark:border-green-700">{completedScans.length}</Badge></TabsTrigger>
                            </TabsList>
                        </div>
                        <div className="relative flex-1 max-w-lg">
                            <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                            <Input placeholder="Search Pentests..." className="h-12 pl-12 pr-12 rounded-xl bg-gradient-to-r from-background to-background/95 border-border/50 focus:border-primary/50 shadow-sm" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
                            {searchQuery && <Button variant="ghost" size="icon" className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 rounded-lg" onClick={() => setSearchQuery("")}><X className="h-4 w-4" /></Button>}
                        </div>
                    </div>

                    <TabsContent value="all" className="mt-6 rounded-md">{renderScanList(allScans, "all")}</TabsContent>
                    <TabsContent value="pending" className="mt-6 rounded-md">{renderScanList(pendingScans, "pending")}</TabsContent>
                    <TabsContent value="in-progress" className="mt-6 rounded-md">{renderScanList(inProgressScans, "in-progress")}</TabsContent>
                    <TabsContent value="re-test" className="mt-6 rounded-md">{renderScanList(reTestScans, "re-test")}</TabsContent>
                    <TabsContent value="completed" className="mt-6 rounded-md">{renderScanList(completedScans, "completed")}</TabsContent>
                </Tabs>
            </div>
        </TooltipProvider>
    );
}