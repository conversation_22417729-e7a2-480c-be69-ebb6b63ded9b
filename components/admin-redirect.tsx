"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

export function AdminRedirect() {
  const router = useRouter();
  const { user, role, loading } = useAuth();

  useEffect(() => {
    // Only redirect after auth is loaded
    if (!loading && user) {
      // Redirect admins to scans page
      if (role === "admin") {
        router.replace("/scans");
      }
      // Redirect managers to security dashboard instead of team dashboard
      else if (role === "manager") {
        router.replace("/dashboard");
      }
    }
  }, [loading, user, role, router]);

  // This component doesn't render anything
  return null;
}
