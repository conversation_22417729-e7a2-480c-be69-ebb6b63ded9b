"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

export type AssetType = "website" | "server" | "repository" | "cloud" | "other"

interface AssetTypeChatModalProps {
  onSubmit: (assetType: AssetType, target: string) => void
}

export function AssetTypeChatModal({ onSubmit }: AssetTypeChatModalProps) {
  const [open, setOpen] = useState(false)
  const [assetType, setAssetType] = useState<AssetType | "">("")
  const [target, setTarget] = useState("")
  const [step, setStep] = useState<"askType" | "askTarget">("askType")

  const handleNext = () => {
    if (step === "askType" && assetType) {
      setStep("askTarget")
    } else if (step === "askTarget" && target) {
      onSubmit(assetType as AssetType, target)
      setOpen(false)
      setAssetType("")
      setTarget("")
      setStep("askType")
    }
  }

  const handleOpenChange = (val: boolean) => {
    setOpen(val)
    if (!val) {
      setAssetType("")
      setTarget("")
      setStep("askType")
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="default" size="sm">
          New Scan
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Start a New Scan</DialogTitle>
          <DialogDescription>
            {step === "askType"
              ? "What type of asset would you like to scan?"
              : `What is the ${assetType} you want to scan?`}
          </DialogDescription>
        </DialogHeader>
        {step === "askType" && (
          <Select value={assetType} onValueChange={setAssetType as any}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select asset type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="website">Website</SelectItem>
              <SelectItem value="server">Server</SelectItem>
              <SelectItem value="repository">Repository</SelectItem>
              <SelectItem value="cloud">Cloud Resource</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        )}
        {step === "askTarget" && (
          <Input
            placeholder={`Enter the ${assetType} (e.g., URL, IP, repo URL, etc.)`}
            value={target}
            onChange={e => setTarget(e.target.value)}
          />
        )}
        <DialogFooter>
          <Button onClick={handleNext} disabled={step === "askType" ? !assetType : !target}>
            {step === "askType" ? "Next" : "Submit"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
