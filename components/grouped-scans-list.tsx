import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp, User, Mail, Clock, AlertCircle, RefreshCw, CheckCircle2, ShieldAlert, Activity, TrendingUp } from "lucide-react";
import { ScanCardModal } from "@/components/scan-card-modal";
import { UserInfo, ScanType } from "@/types/scan-types";
import { cn } from "@/lib/utils";
import "@/styles/grouped-scans.css";

interface GroupedScansListProps {
    users: UserInfo[];
    emptyMessage: string;
    onStatusChange: () => void;
    activeTab: string;
}

// Interface for status counts
interface StatusCounts {
    pending: number;
    inProgress: number;
    reTest: number;
    completed: number;
    critical: number;
}

export const GroupedScansList = React.memo(({ users, emptyMessage, onStatusChange, activeTab }: GroupedScansListProps) => {
    const [expandedUsers, setExpandedUsers] = useState<Record<string, boolean>>({});

    // Optimized toggle function with useCallback
    const toggleUserExpansion = useCallback((userId: string) => {
        setExpandedUsers(prev => ({
            ...prev,
            [userId]: !prev[userId]
        }));
    }, []);

    // Check if a user is expanded
    const isUserExpanded = useCallback((userId: string) => {
        return !!expandedUsers[userId];
    }, [expandedUsers]);

    // Memoized status counts calculation with caching
    const getStatusCounts = useMemo(() => {
        const cache = new Map<string, StatusCounts>();
        
        return (scans: ScanType[]): StatusCounts => {
            // Create cache key from scan IDs and statuses
            const cacheKey = scans.map(s => `${s.id}:${s.status}:${s.criticalVulnerabilities}`).join('|');
            
            if (cache.has(cacheKey)) {
                return cache.get(cacheKey)!;
            }

            const counts = scans.reduce((acc, scan) => {
                switch (scan.status) {
                    case 'pending':
                        acc.pending++;
                        break;
                    case 'in-progress':
                        acc.inProgress++;
                        break;
                    case 're-test':
                        acc.reTest++;
                        break;
                    case 'completed':
                        acc.completed++;
                        break;
                }
                
                if (scan.criticalVulnerabilities > 0) {
                    acc.critical++;
                }
                
                return acc;
            }, {
                pending: 0,
                inProgress: 0,
                reTest: 0,
                completed: 0,
                critical: 0
            } as StatusCounts);

            cache.set(cacheKey, counts);
            return counts;
        };
    }, []);

    const filteredUsersByTab = useMemo(() => {
        if (activeTab === 'all') {
            return users;
        }
        return users.map(user => {
            const filteredScans = user.scans.filter(scan => {
                if (activeTab === 'pending' && scan.status === 'pending') return true;
                if (activeTab === 'in-progress' && scan.status === 'in-progress') return true;
                if (activeTab === 're-test' && scan.status === 're-test') return true;
                if (activeTab === 'completed' && scan.status === 'completed') return true;
                return false;
            });
            return filteredScans.length > 0 ? { ...user, scans: filteredScans } : null;
        }).filter(Boolean);
    }, [users, activeTab]);

    if (users.length === 0) {
        return (
            <Card className="glass-effect enhanced-shadow">
                <CardContent className="flex flex-col items-center justify-center py-16">
                    <div className="flex flex-col items-center space-y-4">
                        <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center border border-border/50">
                            <Activity className="h-8 w-8 text-muted-foreground/50" />
                        </div>
                        <div className="text-center space-y-2">
                            <p className="text-lg font-medium text-muted-foreground">{emptyMessage}</p>
                            <p className="text-sm text-muted-foreground/60">Grouped scans will appear here when available</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {filteredUsersByTab.map((user, index) => user && (
                <Card
                    key={user.userId}
                    className={cn(
                        "group relative overflow-hidden transition-all duration-300 ease-out",
                        "bg-gradient-to-br from-card via-card to-card/95",
                        "border border-border/50 hover:border-border/80",
                        "shadow-sm hover:shadow-lg hover:shadow-primary/5",
                        "backdrop-blur-sm",
                        "animate-fade-in"
                    )}
                    style={{ animationDelay: `${index * 100}ms` }}
                >
                    {/* Modern gradient overlay */}
                    <CardHeader
                        className="pb-4 pt-6 px-6 cursor-pointer hover:bg-muted/30 transition-all duration-200 relative z-10"
                        onClick={() => toggleUserExpansion(user.userId)}
                    >
                        <div className="flex flex-col space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {/* Enhanced user avatar */}
                                    <div className="relative">
                                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-primary/10 via-accent/10 to-primary/5 flex items-center justify-center border border-border/50 shadow-sm">
                                            <User className="h-6 w-6 text-primary/70" />
                                        </div>
                                        {/* Activity indicator */}
                                        <div className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-background shadow-sm">
                                            <Activity className="h-2 w-2 text-white m-auto mt-0.5" />
                                        </div>
                                    </div>

                                    {/* Enhanced user details */}
                                    <div className="flex flex-col space-y-1">
                                        <CardTitle className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                                            {user.displayName || "User"}
                                        </CardTitle>
                                        <div className="flex items-center text-sm text-muted-foreground/80">
                                            <Mail className="h-3.5 w-3.5 mr-2 text-primary/60" />
                                            <span className="font-medium">{user.email}</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-muted-foreground/60">
                                            <TrendingUp className="h-3 w-3" />
                                            <span>{user.scans.length} total scans</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Enhanced scan count and expand indicator */}
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center gap-2">
                                        <Badge
                                            variant="secondary"
                                            className="bg-gradient-to-r from-primary/10 to-accent/10 text-primary border-primary/20 font-medium px-3 py-1.5"
                                        >
                                            {user.scans.length} {user.scans.length === 1 ? 'scan' : 'scans'}
                                        </Badge>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-10 w-10 rounded-xl bg-muted/50 hover:bg-muted/80 border border-border/30 hover:border-border/60 transition-all duration-200"
                                    >
                                        {isUserExpanded(user.userId) ? (
                                            <ChevronUp className="h-4 w-4 text-muted-foreground" />
                                        ) : (
                                            <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                        )}
                                    </Button>
                                </div>
                            </div>

                            {/* Enhanced status badges */}
                            {(() => {
                                const counts = getStatusCounts(user.scans);
                                const hasAnyStatus = counts.pending > 0 || counts.inProgress > 0 ||
                                                    counts.reTest > 0 || counts.completed > 0 ||
                                                    counts.critical > 0;

                                if (!hasAnyStatus) return null;

                                return (
                                    <div className="flex items-center flex-wrap gap-2 ml-16 pt-2 border-t border-border/30">
                                        {counts.pending > 0 && (
                                            <Badge className="flex items-center gap-1.5 py-1.5 px-3 bg-gradient-to-r from-yellow-50 to-amber-50 text-yellow-700 hover:from-yellow-100 hover:to-amber-100 border border-yellow-200/50 transition-all duration-200 dark:from-yellow-900/20 dark:to-amber-900/20 dark:text-yellow-400 dark:border-yellow-800/30 shadow-sm">
                                                <Clock className="h-3.5 w-3.5" />
                                                <span className="font-medium">Pending: {counts.pending}</span>
                                            </Badge>
                                        )}
                                        {counts.inProgress > 0 && (
                                            <Badge className="flex items-center gap-1.5 py-1.5 px-3 bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 hover:from-blue-100 hover:to-cyan-100 border border-blue-200/50 transition-all duration-200 dark:from-blue-900/20 dark:to-cyan-900/20 dark:text-blue-400 dark:border-blue-800/30 shadow-sm">
                                                <AlertCircle className="h-3.5 w-3.5" />
                                                <span className="font-medium">In Progress: {counts.inProgress}</span>
                                            </Badge>
                                        )}
                                        {counts.reTest > 0 && (
                                            <Badge className="flex items-center gap-1.5 py-1.5 px-3 bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 hover:from-purple-100 hover:to-violet-100 border border-purple-200/50 transition-all duration-200 dark:from-purple-900/20 dark:to-violet-900/20 dark:text-purple-400 dark:border-purple-800/30 shadow-sm">
                                                <RefreshCw className="h-3.5 w-3.5" />
                                                <span className="font-medium">Re-test: {counts.reTest}</span>
                                            </Badge>
                                        )}
                                        {counts.completed > 0 && (
                                            <Badge className="flex items-center gap-1.5 py-1.5 px-3 bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 hover:from-green-100 hover:to-emerald-100 border border-green-200/50 transition-all duration-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:text-green-400 dark:border-green-800/30 shadow-sm">
                                                <CheckCircle2 className="h-3.5 w-3.5" />
                                                <span className="font-medium">Completed: {counts.completed}</span>
                                            </Badge>
                                        )}
                                        {counts.critical > 0 && (
                                            <Badge className="flex items-center gap-1.5 py-1.5 px-3 bg-gradient-to-r from-red-50 to-rose-50 text-red-700 hover:from-red-100 hover:to-rose-100 border border-red-200/50 transition-all duration-200 dark:from-red-900/20 dark:to-rose-900/20 dark:text-red-400 dark:border-red-800/30 shadow-sm critical-badge">
                                                <ShieldAlert className="h-3.5 w-3.5" />
                                                <span className="font-medium">Critical: {counts.critical}</span>
                                            </Badge>
                                        )}
                                    </div>
                                );
                            })()}
                        </div>
                    </CardHeader>
                    
                    {isUserExpanded(user.userId) && (
                        <CardContent className="pt-6 pb-6 px-6 bg-gradient-to-br from-muted/20 via-muted/10 to-transparent border-t border-border/40 relative z-10">
                            <div className="space-y-4">
                                {user.scans.length > 0 ? (
                                    <div className="grid grid-cols-1 gap-4">
                                        {user.scans.map((scan, scanIndex) => (
                                            <div
                                                key={scan.id}
                                                className="animate-slide-up"
                                                style={{ animationDelay: `${scanIndex * 50}ms` }}
                                            >
                                                <ScanCardModal
                                                    scan={scan}
                                                    onStatusChange={onStatusChange}
                                                />
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-12 text-muted-foreground">
                                        <div className="flex flex-col items-center space-y-3">
                                            <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                                                <Activity className="h-6 w-6 text-muted-foreground/50" />
                                            </div>
                                            <p className="font-medium">No scans available for this user</p>
                                            <p className="text-sm text-muted-foreground/60">Scans will appear here once they are created</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    )}
                </Card>
            ))}
        </div>
    );
});

GroupedScansList.displayName = 'GroupedScansList';
