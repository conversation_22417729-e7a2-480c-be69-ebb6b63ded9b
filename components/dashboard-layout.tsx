"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { MainNav } from "@/components/main-nav"
import { UserNav } from "@/components/user-nav"
import { ModeToggle } from "@/components/mode-toggle"
import { NotificationButton } from "@/components/notification-button"
import {
  BarChart3,
  MessageSquare,
  Shield,
  ShieldAlert,
  Users,
  HelpCircle,
  Menu,
  X,
  PanelLeftOpen,
  PanelLeftClose,
  MessagesSquare,
  UserCog,
  Code
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useScanCount } from "@/context/ScanCountContext" // Import the hook
import { useTeamMessages } from "@/context/TeamMessagesContext" // Import team messages hook
import { useAuth } from "@/hooks/useAuth" // Import auth hook
import { useNotifications } from "@/context/NotificationsContext" // Import notifications hook

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false) // State for desktop sidebar collapse
  const [isHovering, setIsHovering] = useState(false); // New state for hover
  const [mounted, setMounted] = useState(false)
  const { totalScans } = useScanCount();
  const { unreadCount: messageUnreadCount } = useTeamMessages(); // Get unread team messages count
  const { unreadCount: notificationUnreadCount } = useNotifications(); // Get unread notifications count
  const { role, loading: authLoading } = useAuth(); // Get user role and loading state

  // Determine the effective collapsed state for desktop sidebar
  const effectiveIsCollapsed = isCollapsed && !isHovering;

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Set initial collapsed state based on screen size or preference
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) { // md breakpoint is 768px
        setMobileSidebarOpen(false); // Ensure mobile sidebar is closed on resize to desktop
        setIsCollapsed(false); // Always expanded on small screens, or if it's a mobile view
      } else {
        // On desktop, maintain the collapsed state if it was set by the user, otherwise default to expanded
        setIsCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Set initial state
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Don't render anything until we're mounted and auth is loaded
  if (!mounted || authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="rounded-lg bg-primary/10 p-3 mb-4 inline-block">
            <img
              src="/growthguard-logo.svg"
              alt="GrowthGuard Logo"
              width="32"
              height="32"
              className="h-8 w-8"
              fetchPriority="high"
            />
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col growthguard-gradient-bg dark:bg-[#111827]">
      <header className="sticky top-0 z-50 border-b bg-white/90 dark:bg-[#111827] backdrop-blur-md">
        <div className="flex h-16 items-center justify-between px-4 lg:px-6">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="mr-2 md:hidden dark:hover:bg-[#111827]" onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}>
              {mobileSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:block dark:hover:bg-[#111827]"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? <PanelLeftOpen className="h-5 w-5" /> : <PanelLeftClose className="h-5 w-5" />}
            </Button>
            <MainNav />
          </div>

          <div className="hidden md:flex md:flex-1 md:items-center md:justify-center">
            {/* Search bar removed */}
          </div>

          <div className="flex items-center gap-4">
            <NotificationButton />
            <ModeToggle />
            <UserNav />
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Mobile Sidebar */}
        <aside
          className={cn(
            "fixed inset-y-0 left-0 z-40 transform border-r bg-white/90 dark:bg-[#111827] backdrop-blur-md transition-all duration-300 ease-in-out",
            "md:hidden top-16 h-[calc(100vh-4rem)]", // Only visible on mobile, positioned below header
            mobileSidebarOpen ? "translate-x-0 w-64" : "-translate-x-full w-64",
          )}
        >
          <div className="flex h-full flex-col overflow-y-auto dark:bg-[#111827]">
            <nav className="flex-1 space-y-4 px-3 py-4 mt-4">
              <div className="space-y-2">

                {/* NavItems for mobile - always expanded */}
                {role !== 'admin' && (
                  <NavItem
                    href={role === 'manager' ? "/request-pentest" : "/"}
                    icon={<MessageSquare className="h-4 w-4" />}
                    label="Request Pentest"
                    isActive={pathname === "/" || pathname === "/request-pentest"}
                    isCollapsed={false} // Always expanded on mobile
                  />
                )}
                <NavItem
                  href="/dashboard"
                  icon={<BarChart3 className="h-4 w-4" />}
                  label="Dashboard"
                  isActive={pathname === "/dashboard"}
                  isCollapsed={false}
                />
                <NavItem
                  href="/scans"
                  icon={<Shield className="h-4 w-4" />}
                  label={role === "manager" ? "Team Pentests" : "Pentests"}
                  isActive={pathname === "/scans"}
                  badge={totalScans}
                  isCollapsed={false}
                />
                <NavItem
                  href="/vulnerabilities"
                  icon={<ShieldAlert className="h-4 w-4" />}
                  label="Findings"
                  isActive={pathname === "/vulnerabilities"}
                  isCollapsed={false}
                />
                <NavItem
                  href="/learn"
                  icon={<Code className="h-4 w-4" />}
                  label="Developer Studio"
                  isActive={pathname === "/learn"}
                  isCollapsed={false}
                />
              </div>

              <div className="pt-2 space-y-2">
                <p className="px-3 text-xs font-medium text-muted-foreground uppercase tracking-wider">Management</p>
                {role === 'admin' ? (
                  <>
                    <NavItem
                      href="/admin/conversations"
                      icon={<MessageSquare className="h-4 w-4" />}
                      label="Conversations"
                      isActive={pathname === "/admin/conversations"}
                      isCollapsed={false}
                    />
                    <NavItem
                      href="/messages"
                      icon={<MessagesSquare className="h-4 w-4" />}
                      label="Messages"
                      isActive={pathname === "/messages"}
                      badge={messageUnreadCount}
                      isCollapsed={false}
                    />
                    <NavItem
                      href="/admin/users"
                      icon={<UserCog className="h-4 w-4" />}
                      label="User Management"
                      isActive={pathname === "/admin/users"}
                      isCollapsed={false}
                    />
                  </>
                ) : (
                  <NavItem
                    href="/my-messages"
                    icon={<MessagesSquare className="h-4 w-4" />}
                    label="Support"
                    isActive={pathname === "/my-messages"}
                    isCollapsed={false}
                  />
                )}
                {role === 'manager' && (
                  <NavItem
                    href="/team-dashboard"
                    icon={<Users className="h-4 w-4" />}
                    label="Team Dashboard"
                    isActive={pathname === "/team-dashboard"}
                    isCollapsed={false}
                  />
                )}
              </div>
            </nav>
          </div>
        </aside>

        {/* Desktop Sidebar */}
        <aside
          className={cn(
            "hidden md:block sticky top-16 z-40 h-[calc(100vh-4rem)] transform border-r bg-white/90 dark:bg-[#111827] backdrop-blur-md transition-all duration-300 ease-in-out",
            isCollapsed ? "w-[72px]" : "w-56",
            isHovering && isCollapsed ? "w-56" : "" // Expand on hover if collapsed
           )}
          onMouseEnter={() => {
            if (isCollapsed) {
              setIsHovering(true);
            }
          }}
          onMouseLeave={() => {
            if (isCollapsed) {
              setIsHovering(false);
            }
          }}
        >
          <div className="flex h-full flex-col overflow-y-auto dark:bg-[#111827]">
            <nav className="flex-1 space-y-6 px-3 pt-6">
              <div className="space-y-2">

                {/* NavItems for desktop - pass effectiveIsCollapsed */}
                {role !== 'admin' && (
                  <NavItem
                    href={role === 'manager' ? "/request-pentest" : "/"}
                    icon={<MessageSquare className="h-4 w-4" />}
                    label="Request Pentest"
                    isActive={pathname === "/" || pathname === "/request-pentest"}
                    isCollapsed={effectiveIsCollapsed}
                  />
                )}
                <NavItem
                  href="/dashboard"
                  icon={<BarChart3 className="h-4 w-4" />}
                  label="Dashboard"
                  isActive={pathname === "/dashboard"}
                  isCollapsed={effectiveIsCollapsed}
                />
                <NavItem
                  href="/scans"
                  icon={<Shield className="h-4 w-4" />}
                  label={role === "manager" ? "Team Pentests" : "Pentests"}
                  isActive={pathname === "/scans"}
                  badge={totalScans}
                  isCollapsed={effectiveIsCollapsed}
                />
                <NavItem
                  href="/findings"
                  icon={<ShieldAlert className="h-4 w-4" />}
                  label="Findings"
                  isActive={pathname === "/findings"}
                  isCollapsed={effectiveIsCollapsed}
                />
                <NavItem
                  href="/learn"
                  icon={<Code className="h-4 w-4" />}
                  label="Developer Studio"
                  isActive={pathname === "/learn"}
                  isCollapsed={effectiveIsCollapsed}
                />
              </div>

              <div className="pt-2 space-y-2">
                <p className={cn("px-3 text-xs font-medium text-muted-foreground uppercase tracking-wider", effectiveIsCollapsed && "hidden")}>Management</p>
                {role === 'admin' ? (
                  <>
                    <NavItem
                      href="/admin/conversations"
                      icon={<MessageSquare className="h-4 w-4" />}
                      label="Conversations"
                      isActive={pathname === "/admin/conversations"}
                      isCollapsed={effectiveIsCollapsed}
                    />
                    <NavItem
                      href="/messages"
                      icon={<MessagesSquare className="h-4 w-4" />}
                      label="Messages"
                      isActive={pathname === "/messages"}
                      badge={messageUnreadCount}
                      isCollapsed={effectiveIsCollapsed}
                    />
                    <NavItem
                      href="/admin/users"
                      icon={<UserCog className="h-4 w-4" />}
                      label="User Management"
                      isActive={pathname === "/admin/users"}
                      isCollapsed={effectiveIsCollapsed}
                    />
                  </>
                ) : (
                  <NavItem
                    href="/my-messages"
                    icon={<MessagesSquare className="h-4 w-4" />}
                    label="Support"
                    isActive={pathname === "/my-messages"}
                    isCollapsed={effectiveIsCollapsed}
                  />
                )}
                {role === 'manager' && (
                  <NavItem
                    href="/team-dashboard"
                    icon={<Users className="h-4 w-4" />}
                    label="Team Dashboard"
                    isActive={pathname === "/team-dashboard"}
                    isCollapsed={effectiveIsCollapsed}
                  />
                )}
              </div>
            </nav>
          </div>
        </aside>

        {/* Main content */}
        <main
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out dark:bg-[#111827]",
            effectiveIsCollapsed ? "md:ml-[72px]" : "",
          )}
        >
          {children}
        </main>
      </div>
    </div>
  )
}

interface NavItemProps {
  href: string
  icon: React.ReactNode
  label: string
  isActive?: boolean
  badge?: number
  isCollapsed?: boolean
}

function NavItem({ href, icon, label, isActive, badge, isCollapsed }: NavItemProps) {
  return (
    <Link
      href={href}
      className={cn(
        "group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors cursor-pointer",
        isActive ? "bg-primary/10 text-primary dark:text-white" : "text-muted-foreground hover:bg-muted/50 dark:hover:bg-[#111827] hover:text-foreground dark:hover:text-white",
        isCollapsed && "justify-center"
      )}
    >
      <div
        className={cn(
          "flex h-6 w-6 items-center justify-center rounded-md",
          isActive ? "text-primary dark:text-white" : "text-muted-foreground group-hover:text-foreground dark:group-hover:text-white",
          !isCollapsed && "mr-3"
        )}
      >
        {icon}
      </div>
      {!isCollapsed && <span className="flex-1">{label}</span>}
      {badge !== undefined && !isCollapsed && (
        <Badge className="ml-auto bg-primary/20 text-primary hover:bg-primary/30 dark:bg-[#843DF5]/30 dark:text-white">
          {badge}
        </Badge>
      )}
    </Link>
  )
}
