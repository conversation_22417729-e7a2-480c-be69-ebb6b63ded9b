"use client";

import { useEffect, useState } from "react";

/**
 * PerformanceOptimizer component that helps optimize the Largest Contentful Paint (LCP)
 * by prioritizing critical content and deferring non-critical operations.
 */
export function PerformanceOptimizer() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Mark the page as loaded after the main content is rendered
    if (document.readyState === "complete") {
      setIsLoaded(true);
      document.documentElement.classList.add("content-loaded");
    } else {
      // Use the load event to detect when the page is fully loaded
      const handleLoad = () => {
        setIsLoaded(true);
        document.documentElement.classList.add("content-loaded");
      };

      window.addEventListener("load", handleLoad);
      return () => window.removeEventListener("load", handleLoad);
    }
  }, []);

  // Add preload hints for critical resources
  useEffect(() => {
    // Function to add preload link
    const addPreloadLink = (href: string, as: string, type?: string) => {
      const link = document.createElement("link");
      link.rel = "preload";
      link.href = href;
      link.as = as;
      if (type) link.type = type;
      document.head.appendChild(link);
    };

    // Preload critical assets
    addPreloadLink("/growthguard-logo.svg", "image", "image/svg+xml");

    // Preload critical fonts
    const fontUrls = Array.from(document.querySelectorAll("link[rel='stylesheet']"))
      .map((link) => (link as HTMLLinkElement).href)
      .filter((href) => href.includes("fonts.googleapis.com"));

    fontUrls.forEach((url) => {
      addPreloadLink(url, "style");
    });

  }, []);

  // Optimize LCP by prioritizing content rendering
  useEffect(() => {
    // Add content-visibility: auto to non-critical elements
    const nonCriticalElements = document.querySelectorAll(
      ".animate-fade-in, .animate-pulse, .logo-glow"
    );

    // Apply optimizations to non-critical elements only
    if (nonCriticalElements.length > 0) {
      nonCriticalElements.forEach((el) => {
        // Defer animations until after LCP
        if (!isLoaded) {
          el.classList.add("defer-animation");
        }
      });
    }

    // Note: We've removed the code that adds priority-content class to avoid hydration mismatches
  }, [isLoaded]);

  // This component doesn't render anything visible
  return null;
}
