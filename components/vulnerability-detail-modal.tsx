"use client";

import { useState, useEffect, useCallback, useMemo, memo, ReactNode } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  ArrowUpRight,
  Calendar,
  CheckCircle2,
  Copy,
  Download,
  ExternalLink,
  FileText,
  Loader2,
  Shield,
  ShieldAlert,
  Target,
  Wrench,
} from "lucide-react";
import { Vulnerability, VulnerabilitySeverity, VulnerabilityNote } from "@/types/vulnerability-types";
import { ScanType } from "@/types/scan-types";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";
import { useConversations } from "@/context/ConversationsContext";

// --- Helper Functions & Constants ---

// Centralized configuration for severity styles
const SEVERITY_CONFIG: Record<VulnerabilitySeverity, { color: string; badgeClass: string; iconColor: string }> = {
  critical: { color: "bg-red-500", badgeClass: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300", iconColor: "text-red-600 dark:text-red-400" },
  high: { color: "bg-orange-500", badgeClass: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300", iconColor: "text-orange-600 dark:text-orange-400" },
  medium: { color: "bg-yellow-500", badgeClass: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300", iconColor: "text-yellow-600 dark:text-yellow-400" },
  low: { color: "bg-blue-500", badgeClass: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300", iconColor: "text-blue-600 dark:text-blue-400" },

};

// Date formatting utility
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// --- Reusable UI Sub-Components (Memoized) ---

const GeneratingDetailsLoader = memo(({ title, description }: { title: string; description: string }) => (
  <div className="flex flex-col items-center justify-center p-8 bg-gradient-to-br from-violet/5 to-violet/10 rounded-xl border border-violet/20 backdrop-blur-sm">
    <div className="relative">
      <Loader2 className="h-12 w-12 animate-spin text-violet mb-4" />
      <div className="absolute inset-0 h-12 w-12 rounded-full bg-violet/20 animate-ping"></div>
    </div>
    <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
    <p className="text-muted-foreground text-center max-w-md">{description}</p>
  </div>
));
GeneratingDetailsLoader.displayName = "GeneratingDetailsLoader";

const ContentSection = memo(({ title, icon, iconBg, children }: { title: string; icon: ReactNode; iconBg: string; children: ReactNode }) => (
  <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
    <h3 className="text-xl font-semibold mb-4 flex items-center gap-3 text-foreground">
      <div className={cn("p-2 rounded-lg border", iconBg)}>{icon}</div>
      {title}
    </h3>
    <div className="prose prose-sm max-w-none text-muted-foreground leading-relaxed">
      {children}
    </div>
  </div>
));
ContentSection.displayName = "ContentSection";

const InfoItem = memo(({ label, icon, value }: { label: string; icon: ReactNode; value: ReactNode }) => (
  <div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300">
    <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide">{label}</h3>
    <div className="flex items-center gap-3">{icon}{value}</div>
  </div>
));
InfoItem.displayName = "InfoItem";

// --- Tab Content Components (Memoized) ---

const VulnerabilityHeader = memo(({ vulnerability, parentScan, loadingScan, onCopy }: { vulnerability: Vulnerability; parentScan: ScanType | null; loadingScan: boolean; onCopy: () => void; }) => {
  const severityConfig = SEVERITY_CONFIG[vulnerability.severity];
  const actionButtonClasses = "flex items-center gap-2 hover:bg-violet/10 hover:border-violet/30 hover:text-violet transition-all duration-300";

  return (
    <div className="flex items-start justify-between gap-4 mt-4">
      <div className="flex items-center gap-3 flex-wrap">
        <Badge className={cn("capitalize font-semibold px-4 py-2 text-sm shadow-md", severityConfig.badgeClass)}>
          <ShieldAlert className="h-4 w-4 mr-2" />
          {vulnerability.severity}
        </Badge>
        {vulnerability.cvss && (
          <Badge variant="outline" className="font-mono text-sm px-3 py-2 bg-background/50">
            CVSS: {vulnerability.cvss}
          </Badge>
        )}
      </div>
      <div className="flex items-center gap-2 flex-shrink-0">
        <Button variant="outline" size="sm" onClick={onCopy} className={actionButtonClasses}>
          <Copy className="h-4 w-4" /> Copy
        </Button>
        {parentScan?.resultFileUrl && (
          <Button variant="outline" size="sm" asChild className={actionButtonClasses}>
            <a href={parentScan.resultFileUrl} target="_blank" rel="noopener noreferrer">
              <Download className="h-3.5 w-3.5" /> Report
            </a>
          </Button>
        )}
        {loadingScan && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
      </div>
    </div>
  );
});
VulnerabilityHeader.displayName = "VulnerabilityHeader";

const InfoTab = memo(({ vulnerability, scanName }: { vulnerability: Vulnerability; scanName: string }) => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div className="space-y-4">
      <InfoItem
        label="Affected Component"
        icon={<div className="p-2 rounded-lg bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800"><Target className="h-4 w-4 text-blue-600 dark:text-blue-400" /></div>}
        value={<span className="font-medium text-foreground">{vulnerability.affectedComponent}</span>}
      />
      <InfoItem
        label="Pentest Name"
        icon={<div className="p-2 rounded-lg bg-violet/10 border border-violet/20"><Shield className="h-4 w-4 text-violet" /></div>}
        value={<span className="font-medium text-foreground">{scanName}</span>}
      />
      {vulnerability.target && (
        <InfoItem
          label="Target"
          icon={<div className="p-2 rounded-lg bg-orange-50 dark:bg-orange-950/30 border border-orange-200 dark:border-orange-800"><ArrowUpRight className="h-4 w-4 text-orange-600 dark:text-orange-400" /></div>}
          value={<span className="font-medium text-foreground">{vulnerability.target}</span>}
        />
      )}
    </div>
    <div className="space-y-4">
      <InfoItem
        label="Discovery Date"
        icon={<div className="p-2 rounded-lg bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800"><Calendar className="h-4 w-4 text-green-600 dark:text-green-400" /></div>}
        value={<span className="font-medium text-foreground">{formatDate(vulnerability.createdAt)}</span>}
      />
      <InfoItem
        label="Current Status"
        icon={<div className={cn("p-2 rounded-lg border", vulnerability.status === "Closed" ? "bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800" : "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800")}><CheckCircle2 className={cn("h-4 w-4", vulnerability.status === "Closed" ? "text-green-600 dark:text-green-400" : "text-blue-600 dark:text-blue-400")} /></div>}
        value={<span className="font-medium text-foreground">{vulnerability.status || "Open"}</span>}
      />
      {vulnerability.cveId && (
        <InfoItem
          label="CVE Reference"
          icon={<div className="p-2 rounded-lg bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 group-hover:bg-violet/10 group-hover:border-violet/20 transition-all duration-200"><ExternalLink className="h-4 w-4 text-red-600 dark:text-red-400 group-hover:text-violet transition-colors duration-200" /></div>}
          value={<a href={`https://nvd.nist.gov/vuln/detail/${vulnerability.cveId}`} target="_blank" rel="noopener noreferrer" className="flex items-center gap-3 hover:text-violet transition-colors duration-200 group"><span className="font-medium font-mono">{vulnerability.cveId}</span></a>}
        />
      )}
    </div>
  </div>
));
InfoTab.displayName = "InfoTab";

const DetailsTab = memo(({ vulnerability, isGenerating }: { vulnerability: Vulnerability; isGenerating: boolean }) => {
  if (isGenerating) {
    return <GeneratingDetailsLoader title="Generating Details" description="Our AI is analyzing the vulnerability to provide comprehensive details..." />;
  }
  return (
    <div className="space-y-6">
      <ContentSection icon={<ShieldAlert className="h-5 w-5 text-violet" />} iconBg="bg-violet/10 border-violet/20" title="Detailed Explanation">
        <p className="whitespace-pre-line">{vulnerability.detailedExplanation || "No detailed explanation available."}</p>
      </ContentSection>
      <ContentSection icon={<AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />} iconBg="bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800" title="Impact Assessment">
        <p>{vulnerability.impact || "No impact assessment available."}</p>
      </ContentSection>
    </div>
  );
});
DetailsTab.displayName = "DetailsTab";

const RemediationTab = memo(({ vulnerability, isGenerating, onGenerate }: { vulnerability: Vulnerability; isGenerating: boolean; onGenerate: () => void; }) => {
  if (isGenerating) {
    return <GeneratingDetailsLoader title="Generating Remediation Steps" description="Creating comprehensive remediation guidance tailored to this vulnerability..." />;
  }
  return (
    <ContentSection icon={<Wrench className="h-5 w-5 text-green-600 dark:text-green-400" />} iconBg="bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800" title="Remediation Steps">
      {vulnerability.remediationSteps?.length ? (
        <div className="space-y-4">
          {vulnerability.remediationSteps.map((step, index) => (
            <div key={index} className="flex gap-4 p-4 bg-background/50 rounded-lg border border-border/30 hover:border-green-300 dark:hover:border-green-700 transition-all duration-300 group">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-950/50 text-green-700 dark:text-green-300 rounded-full flex items-center justify-center font-semibold text-sm group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors duration-200">
                {index + 1}
              </div>
              <p className="text-muted-foreground leading-relaxed flex-1 group-hover:text-foreground transition-colors duration-200">{step}</p>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Wrench className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No remediation steps available yet.</p>
          <Button variant="outline" onClick={onGenerate} className="mt-4 hover:bg-violet/10 hover:border-violet/30 hover:text-violet transition-all duration-300">
            Generate Remediation Steps
          </Button>
        </div>
      )}
    </ContentSection>
  );
});
RemediationTab.displayName = "RemediationTab";

const NotesTab = memo(({ notes }: { notes: VulnerabilityNote[] | undefined }) => (
  <div className="space-y-4">
    {notes?.length ? (
      notes.map((note, index) => (
        <div key={note.id} className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300" style={{ animationDelay: `${index * 100}ms` }}>
          <div className="flex items-center gap-4 mb-4">
            <div className="w-10 h-10 rounded-full bg-violet/10 border border-violet/20 flex items-center justify-center">
              <span className="text-violet font-semibold text-sm">{note.author.charAt(0).toUpperCase()}</span>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 text-sm">
                <span className="font-semibold text-foreground">{note.author}</span>
                <div className="flex items-center gap-1 text-muted-foreground"><Calendar className="h-3 w-3" /><span>{formatDate(note.createdAt)}</span></div>
              </div>
            </div>
          </div>
          <p className="text-foreground leading-relaxed pl-14">{note.text}</p>
        </div>
      ))
    ) : (
      <div className="text-center py-12 bg-card/30 backdrop-blur-sm border border-border/30 rounded-xl">
        <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mx-auto mb-4"><FileText className="h-8 w-8 text-muted-foreground" /></div>
        <h3 className="text-lg font-semibold mb-2 text-foreground">No Notes Available</h3>
        <p className="text-muted-foreground max-w-md mx-auto">No notes have been added to this vulnerability yet.</p>
      </div>
    )}
  </div>
));
NotesTab.displayName = "NotesTab";


// --- Main Component ---

interface VulnerabilityDetailModalProps {
  vulnerability: Vulnerability;
  isOpen: boolean;
  onClose: () => void;
}

export function VulnerabilityDetailModal({ vulnerability, isOpen, onClose }: VulnerabilityDetailModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { conversations } = useConversations();

  const [activeTab, setActiveTab] = useState("details");
  const [isGeneratingDetails, setIsGeneratingDetails] = useState(false);
  const [loadingScan, setLoadingScan] = useState(false);
  const [localVulnerability, setLocalVulnerability] = useState<Vulnerability>(vulnerability);
  const [parentScan, setParentScan] = useState<ScanType | null>(null);

  // Effect to sync local state when the initial vulnerability prop changes
  useEffect(() => {
    setLocalVulnerability(vulnerability);
  }, [vulnerability]);

  const fetchParentScan = useCallback(async (scanId: string) => {
    if (!user) return;
    setLoadingScan(true);
    try {
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/scans/${scanId}`, {
        headers: { Authorization: `Bearer ${idToken}` },
      });
      if (response.ok) {
        setParentScan(await response.json());
      } else {
        console.warn("Could not fetch parent scan information");
      }
    } catch (err) {
      console.error("Error fetching parent scan:", err);
    } finally {
      setLoadingScan(false);
    }
  }, [user]);

  const generateDetailedExplanation = useCallback(async () => {
    if (!user) return;
    setIsGeneratingDetails(true);
    try {
      const idToken = await user.getIdToken();
      const response = await fetch(`/api/vulnerabilities/generate-details`, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` },
        body: JSON.stringify({
          vulnerabilityId: vulnerability.id,
          name: vulnerability.name,
          severity: vulnerability.severity,
          description: vulnerability.description,
          affectedComponent: vulnerability.affectedComponent,
          target: vulnerability.target,
        }),
      });

      if (!response.ok) throw new Error("Failed to generate vulnerability details");

      const data = await response.json();
      setLocalVulnerability(prev => ({
        ...prev,
        detailedExplanation: data.detailedExplanation,
        remediationSteps: data.remediationSteps,
        impact: data.impact,
        cveId: data.cveId,
      }));

      toast({ title: "Details Generated", description: "Vulnerability details have been generated successfully." });
    } catch (err) {
      console.error("Error generating vulnerability details:", err);
      toast({ title: "Error", description: "Failed to generate vulnerability details. Please try again.", variant: "destructive" });
    } finally {
      setIsGeneratingDetails(false);
    }
  }, [user, vulnerability, toast]);

  // Optimized effect to run actions only when the modal opens
  useEffect(() => {
    if (isOpen) {
      if (localVulnerability.scanId && !parentScan) {
        fetchParentScan(localVulnerability.scanId);
      }
      if (!localVulnerability.detailedExplanation && !isGeneratingDetails) {
        generateDetailedExplanation();
      }
    }
  }, [isOpen, localVulnerability, parentScan, fetchParentScan, generateDetailedExplanation, isGeneratingDetails]);

  const copyToClipboard = useCallback(() => {
    const details = `
Vulnerability: ${localVulnerability.name}
Severity: ${localVulnerability.severity}
Affected Component: ${localVulnerability.affectedComponent}
Description: ${localVulnerability.description}
${localVulnerability.detailedExplanation ? `\nDetailed Explanation:\n${localVulnerability.detailedExplanation}` : ''}
${localVulnerability.remediationSteps?.length ? `\nRemediation Steps:\n${localVulnerability.remediationSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}` : ''}
${localVulnerability.impact ? `\nImpact:\n${localVulnerability.impact}` : ''}
${localVulnerability.cveId ? `\nCVE ID: ${localVulnerability.cveId}` : ''}
    `.trim();

    navigator.clipboard.writeText(details);
    toast({ title: "Copied to Clipboard", description: "Vulnerability details have been copied." });
  }, [localVulnerability, toast]);

  const scanName = useMemo(() => 
    localVulnerability.scanName || conversations.find(conv => conv.id === localVulnerability.scanId)?.title || "Unknown Scan",
    [localVulnerability.scanName, localVulnerability.scanId, conversations]
  );

  const tabTriggerClasses = "flex items-center gap-2 rounded-lg transition-all duration-300 data-[state=active]:bg-violet data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-muted/50";

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[95vh] flex flex-col bg-card/95 backdrop-blur-md border border-border/50 shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[2%] data-[state=open]:slide-in-from-top-[2%]">
        <DialogHeader className="relative pb-6 pr-0">
          <div className={cn("absolute top-0 left-0 right-0 h-1 rounded-t-lg", SEVERITY_CONFIG[localVulnerability.severity]?.color)} />
          <VulnerabilityHeader
            vulnerability={localVulnerability}
            parentScan={parentScan}
            loadingScan={loadingScan}
            onCopy={copyToClipboard}
          />
          <DialogTitle className="text-2xl font-bold mt-4 leading-tight text-foreground">
            {localVulnerability.name}
          </DialogTitle>
          <DialogDescription className="text-base leading-relaxed text-muted-foreground mt-2">
            {localVulnerability.description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-grow overflow-y-auto -mr-6 pr-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-muted/30 backdrop-blur-sm border border-border/30 rounded-xl p-1 shadow-sm">
              <TabsTrigger value="info" className={tabTriggerClasses}><FileText className="h-4 w-4" /> <span className="hidden sm:inline">Info</span></TabsTrigger>
              <TabsTrigger value="details" className={tabTriggerClasses}><ShieldAlert className="h-4 w-4" /> <span className="hidden sm:inline">Details</span></TabsTrigger>
              <TabsTrigger value="remediation" className={tabTriggerClasses}><Wrench className="h-4 w-4" /> <span className="hidden sm:inline">Remediation</span></TabsTrigger>
              <TabsTrigger value="notes" className={tabTriggerClasses}><FileText className="h-4 w-4" /> <span className="hidden sm:inline">Notes</span></TabsTrigger>
            </TabsList>

            <TabsContent value="info" className="mt-6"><InfoTab vulnerability={localVulnerability} scanName={scanName} /></TabsContent>
            <TabsContent value="details" className="mt-6"><DetailsTab vulnerability={localVulnerability} isGenerating={isGeneratingDetails} /></TabsContent>
            <TabsContent value="remediation" className="mt-6"><RemediationTab vulnerability={localVulnerability} isGenerating={isGeneratingDetails} onGenerate={generateDetailedExplanation} /></TabsContent>
            <TabsContent value="notes" className="mt-6"><NotesTab notes={localVulnerability.notes as VulnerabilityNote[] | undefined} /></TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}