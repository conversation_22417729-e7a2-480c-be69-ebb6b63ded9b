"use client";

import React, { useState } from "react";
import { VulnerabilitySeverity } from "@/types/vulnerability-types";
import { ModernCheckbox } from "./ui/modern-checkbox";
import { Label } from "./ui/label";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./ui/popover";
import {
  Filter,
  ChevronDown,
  X,
  Shield,
  Activity,
  RotateCcw,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Zap,
  Search,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface VulnerabilityFilterToolbarProps {
  onSeverityChange: (severity: VulnerabilitySeverity, isChecked: boolean) => void;
  selectedSeverities: VulnerabilitySeverity[];
  onStatusChange: (status: "Open" | "Closed" | "Pending Retest", isChecked: boolean) => void;
  selectedStatuses: ("Open" | "Closed" | "Pending Retest")[];
  onScanChange: (scanName: string | null) => void;
  selectedScan: string | null;
  scanNames: string[];
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const severityLevels: VulnerabilitySeverity[] = ["critical", "high", "medium", "low"];
const statusOptions: ("Open" | "Closed" | "Pending Retest")[] = ["Open", "Closed", "Pending Retest"];

// Severity configuration with colors and icons
const severityConfig = {
  critical: {
    color: "text-red-600 dark:text-red-400",
    bgColor: "bg-red-50 dark:bg-red-950/30",
    borderColor: "border-red-200 dark:border-red-800",
    icon: AlertTriangle,
    label: "Critical",
  },
  high: {
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-50 dark:bg-orange-950/30",
    borderColor: "border-orange-200 dark:border-orange-800",
    icon: Zap,
    label: "High",
  },
  medium: {
    color: "text-yellow-600 dark:text-yellow-400",
    bgColor: "bg-yellow-50 dark:bg-yellow-950/30",
    borderColor: "border-yellow-200 dark:border-yellow-800",
    icon: AlertTriangle,
    label: "Medium",
  },
  low: {
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-50 dark:bg-blue-950/30",
    borderColor: "border-blue-200 dark:border-blue-800",
    icon: Shield,
    label: "Low",
  },
};

// Status configuration with colors and icons
const statusConfig = {
  "Open": {
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-50 dark:bg-blue-950/30",
    borderColor: "border-blue-200 dark:border-blue-800",
    icon: AlertTriangle,
    label: "Open",
  },
  "Closed": {
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-50 dark:bg-green-950/30",
    borderColor: "border-green-200 dark:border-green-800",
    icon: CheckCircle2,
    label: "Closed",
  },
  "Pending Retest": {
    color: "text-violet dark:text-violet-light",
    bgColor: "bg-violet-lightest dark:bg-violet/10",
    borderColor: "border-violet/20 dark:border-violet/30",
    icon: Clock,
    label: "Pending Retest",
  },
};

export const VulnerabilityFilterToolbar: React.FC<VulnerabilityFilterToolbarProps> = ({
  onSeverityChange,
  selectedSeverities,
  onStatusChange,
  selectedStatuses,
  onScanChange,
  selectedScan,
  scanNames,
  searchQuery,
  onSearchChange,
}) => {
  const [isSeverityOpen, setIsSeverityOpen] = useState(false);
  const [isStatusOpen, setIsStatusOpen] = useState(false);

  const totalActiveFilters = selectedSeverities.length + selectedStatuses.length + (selectedScan ? 1 : 0) + (searchQuery ? 1 : 0);

  const clearAllFilters = () => {
    selectedSeverities.forEach(severity => onSeverityChange(severity, false));
    selectedStatuses.forEach(status => onStatusChange(status, false));
    onScanChange(null);
    onSearchChange("");
  };

  const FilterPopover = ({ 
    title, 
    icon: Icon, 
    isOpen, 
    onOpenChange, 
    children, 
    activeCount = 0,
    triggerClassName = ""
  }: {
    title: string;
    icon: React.ElementType;
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    children: React.ReactNode;
    activeCount?: number;
    triggerClassName?: string;
  }) => (
    <Popover open={isOpen} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "h-10 px-4 gap-2 border-border/50 hover:border-violet/30 hover:bg-violet/5 transition-all duration-200",
            activeCount > 0 && "border-violet/50 bg-violet/10 text-violet",
            triggerClassName
          )}
        >
          <Icon className="h-4 w-4" />
          <span className="font-medium">{title}</span>
          {activeCount > 0 && (
            <Badge variant="secondary" className="h-5 px-2 text-xs bg-violet/20 text-violet border-violet/30">
              {activeCount}
            </Badge>
          )}
          <ChevronDown className="h-4 w-4 ml-1" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-4" align="start">
        <div className="space-y-3">
          <div className="flex items-center gap-2 pb-2 border-b border-border/50">
            <Icon className="h-4 w-4 text-violet" />
            <span className="font-medium text-sm">{title}</span>
          </div>
          {children}
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <div className="w-full 2xl:max-w-7xl mx-auto transition-all duration-300 mb-6">
    {/* Main Controls Row */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Search Bar */}
        <div className="relative flex-1 min-w-[50px] rounded-xl">
          <Search className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground transition-colors duration-300" />
          <Input
            placeholder="Search findings..."
            className="px-10 h-10 bg-background/50 text-sm placeholder:text-muted-foreground/50 transition-all duration-300"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 h-6 w-6 -translate-y-1/2 hover:bg-destructive/10 hover:text-destructive transition-all duration-200 p-0"
              onClick={() => onSearchChange("")}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter Separator */}
        <div className="h-6 w-px bg-border/50" />
        {/* Severity Filter */}
        <FilterPopover
          title="Severity"
          icon={Shield}
          isOpen={isSeverityOpen}
          onOpenChange={setIsSeverityOpen}
          activeCount={selectedSeverities.length}
        >
          <div className="space-y-2">
            {severityLevels.map((severity) => {
              const config = severityConfig[severity];
              const Icon = config.icon;
              const isSelected = selectedSeverities.includes(severity);
              
              return (
                <div
                  key={severity}
                  className={cn(
                    "flex items-center gap-3 p-2 rounded-lg transition-all duration-200 cursor-pointer hover:bg-muted/30",
                    isSelected && config.bgColor,
                    isSelected && config.borderColor,
                    isSelected && "border shadow-sm"
                  )}
                  onClick={() => onSeverityChange(severity, !isSelected)}
                >
                  <ModernCheckbox
                    id={`severity-${severity}`}
                    checked={isSelected}
                    onCheckedChange={(checked) =>
                      onSeverityChange(severity, checked as boolean)
                    }
                    variant="primary"
                    size="sm"
                  />
                  <Icon className={cn("h-4 w-4", config.color)} />
                  <Label
                    htmlFor={`severity-${severity}`}
                    className={cn(
                      "text-sm font-medium cursor-pointer flex-1",
                      config.color
                    )}
                  >
                    {config.label}
                  </Label>
                </div>
              );
            })}
          </div>
        </FilterPopover>

        {/* Status Filter */}
        <FilterPopover
          title="Status"
          icon={Activity}
          isOpen={isStatusOpen}
          onOpenChange={setIsStatusOpen}
          activeCount={selectedStatuses.length}
        >
          <div className="space-y-2">
            {statusOptions.map((status) => {
              const config = statusConfig[status];
              const Icon = config.icon;
              const isSelected = selectedStatuses.includes(status);
              
              return (
                <div
                  key={status}
                  className={cn(
                    "flex items-center gap-3 p-2 rounded-lg transition-all duration-200 cursor-pointer hover:bg-muted/30",
                    isSelected && config.bgColor,
                    isSelected && config.borderColor,
                    isSelected && "border shadow-sm"
                  )}
                  onClick={() => onStatusChange(status, !isSelected)}
                >
                  <ModernCheckbox
                    id={`status-${status}`}
                    checked={isSelected}
                    onCheckedChange={(checked) =>
                      onStatusChange(status, checked as boolean)
                    }
                    variant="primary"
                    size="sm"
                  />
                  <Icon className={cn("h-4 w-4", config.color)} />
                  <Label
                    htmlFor={`status-${status}`}
                    className={cn(
                      "text-sm font-medium cursor-pointer flex-1",
                      config.color
                    )}
                  >
                    {config.label}
                  </Label>
                </div>
              );
            })}
          </div>
        </FilterPopover>

        {/* Scans Filter */}
        <div className="flex items-center gap-2">
          <Select value={selectedScan || "all"} onValueChange={(value) => onScanChange(value === "all" ? null : value)}>
            <SelectTrigger className={cn(
              "w-48 h-10 bg-background/50 border-border/50 hover:border-violet/30 focus:border-violet/50 transition-colors",
              selectedScan && "border-violet/50 bg-violet/10"
            )}>
              <SelectValue placeholder="Select pentest..." />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              <SelectItem value="all">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-muted-foreground/30" />
                  <span className="text-muted-foreground">All pentests</span>
                </div>
              </SelectItem>
              {scanNames.map((scanName) => (
                <SelectItem key={scanName} value={scanName}>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-violet" />
                    <span className="truncate max-w-[200px]" title={scanName}>
                      {scanName}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Selected Scan Display */}
        {selectedScan && (
          <div className="flex items-center gap-2 px-3 py-2 bg-violet/5 border border-violet/20 rounded-lg">
            <span className="text-xs font-medium text-violet truncate max-w-[120px]" title={selectedScan}>
              {selectedScan}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onScanChange(null)}
              className="h-5 w-5 p-0 hover:bg-violet/10 text-violet hover:text-violet/80 ml-1"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* Clear All Button */}
        <div
          className={cn(
            "ml-auto transition-all duration-300 ease-in-out overflow-hidden flex-shrink-0",
            totalActiveFilters > 0 ? "w-[120px] opacity-100" : "w-0 opacity-0"
          )}
        >
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFilters}
            className="h-10 px-4 gap-2 text-xs font-medium border-border/50 hover:border-destructive/30 hover:text-destructive hover:bg-destructive/5 transition-all duration-200 whitespace-nowrap"
          >
            <RotateCcw className="h-3 w-3" />
            Clear All
          </Button>
        </div>
      </div>
    </div>
  );
};