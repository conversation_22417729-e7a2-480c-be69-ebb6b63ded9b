"use client"

import React, { useState, useEffect, useRef, useMemo } from "react"
import { format } from "date-fns"
import {
  Search,
  RefreshCw,
  Loader2,
  MessageSquare,
  X,
  Send,
  MessagesSquare
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useAuth } from "@/hooks/useAuth"
import { useTeamMessages } from "@/context/TeamMessagesContext"
import { usePusher } from "@/hooks/usePusher"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

// Basic XSS protection - escape HTML entities
const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

// Sanitize message content to prevent XSS
const sanitizeMessage = (message: string): string => {
  // Remove script tags and other dangerous elements
  const dangerous = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
  const cleaned = message.replace(dangerous, '');
  
  // Escape remaining HTML
  return escapeHtml(cleaned);
};

// Safe message display component
const SafeMessageContent = ({ content }: { content: string }) => {
  const sanitizedContent = useMemo(() => {
    // For now, just escape all HTML to prevent XSS
    // In production, consider using a proper sanitization library
    return escapeHtml(content);
  }, [content]);
  
  return (
    <div className="whitespace-pre-wrap">
      {sanitizedContent}
    </div>
  );
};

// Helper to get initials for AvatarFallback
const getInitials = (email: string = "") => {
  const parts = email.split("@")[0].split(/[._-]/)
  if (parts.length > 1) {
    return (parts[0][0] + parts[1][0]).toUpperCase()
  }
  return email.substring(0, 2).toUpperCase()
}

export const ClientMessages = () => {
  const { user, loading: authLoading } = useAuth()
  const {
    clientMessages,
    clientMessagesLoading,
    fetchClientMessages,
    lastUpdated
  } = useTeamMessages()
  const { isConnected: pusherConnected } = usePusher()

  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)
  const [isMounted, setIsMounted] = useState(false)
  const [showNewMessageForm, setShowNewMessageForm] = useState(false)
  const [newMessage, setNewMessage] = useState("")
  const [replyMessage, setReplyMessage] = useState("")
  const [sendingMessage, setSendingMessage] = useState(false)
  const [sendingReply, setSendingReply] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null) // For scrolling in chat view

  // Set mounted state to avoid hydration mismatch
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Scroll to bottom when messages change or when a message is selected
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [clientMessages, selectedMessageId]);

  // Get the selected message using useMemo to prevent recalculating on every render
  const selectedMessage = useMemo(() =>
    selectedMessageId ? clientMessages.find(msg => msg.id === selectedMessageId) : null,
    [clientMessages, selectedMessageId]
  );

  // Get related messages (replies to the selected message)
  const relatedMessages = useMemo(() => {
    if (!selectedMessage) return [];

    // Find all messages that are replies to the selected message
    return clientMessages
      .filter(msg => msg.isReplyTo === selectedMessage.id)
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }, [clientMessages, selectedMessage]);

  // Function to send a new message to the team
  const sendNewMessage = async () => {
    if (!newMessage.trim() || sendingMessage || !user) return;

    setSendingMessage(true);
    try {
      // Get the authentication token
      const idToken = await user.getIdToken();

      const response = await fetch('/api/contact-team', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({ message: newMessage }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Clear the form and hide it
      setNewMessage('');
      setShowNewMessageForm(false);

      // Refresh messages
      fetchClientMessages();

      toast.success('Message sent to team successfully');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setSendingMessage(false);
    }
  };

  // Function to send a reply to an admin response
  const sendReply = async (originalMessageId: string) => {
    if (!replyMessage.trim() || sendingReply || !user || !originalMessageId) return;

    setSendingReply(true);
    try {
      // Get the authentication token
      const idToken = await user.getIdToken();

      const response = await fetch('/api/client-reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          message: replyMessage,
          originalMessageId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send reply');
      }

      // Clear the reply form
      setReplyMessage('');

      // Refresh messages
      fetchClientMessages();

      toast.success('Reply sent successfully');
    } catch (error) {
      console.error('Error sending reply:', error);
      toast.error('Failed to send reply. Please try again.');
    } finally {
      setSendingReply(false);
    }
  };


  // Filter messages based on search query
  // Use useMemo to prevent recalculating on every render
  const filteredMessages = useMemo(() => {
    return clientMessages.filter(message => {
      const matchesSearch = searchQuery
        ? message.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (message.response && message.response.toLowerCase().includes(searchQuery.toLowerCase()))
        : true
      return matchesSearch
    })
  }, [clientMessages, searchQuery])

  // Conditional returns
  if (!isMounted) return null
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading authentication...</p>
        </div>
      </div>
    )
  }
  if (!user) return null

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)] bg-background dark:bg-[#111827]">
      {/* Top Header */}
      <div className="flex items-center justify-between border-b px-6 py-3 dark:border-slate-700">
        <div className="flex flex-col">
          <div className="flex items-center space-x-3">
            <MessagesSquare className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Support</h2>
          </div>
          {lastUpdated && (
            <p className="text-xs text-muted-foreground mt-1">
              Last updated: {format(lastUpdated, "h:mm:ss a")}
              {clientMessagesLoading && " • Updating..."}
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (!clientMessagesLoading) {
                fetchClientMessages();
                toast.info("Refreshing messages...");
              }
            }}
            disabled={clientMessagesLoading}
            className="dark:hover:bg-slate-700/50 dark:border-slate-600"
          >
            {clientMessagesLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => setShowNewMessageForm(true)}
            disabled={showNewMessageForm}
            className="dark:bg-primary dark:hover:bg-primary/90"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            New Message
          </Button>
        </div>
      </div>

      {/* New Message Form */}
      {showNewMessageForm && (
        <div className="border-b p-4 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/30">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium">New Conversation with Team</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowNewMessageForm(false);
                setNewMessage("");
              }}
              className="dark:hover:bg-slate-700/50"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <div className="space-y-3">
            <Textarea
              placeholder="Type your message to the team here..."
              className="w-full min-h-[120px] dark:bg-slate-700 dark:border-slate-600 dark:text-slate-100"
              value={newMessage}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNewMessage(e.target.value)}
              disabled={sendingMessage}
            />
            <div className="flex justify-end">
              <Button
                onClick={sendNewMessage}
                disabled={!newMessage.trim() || sendingMessage}
                className="dark:bg-primary dark:hover:bg-primary/90"
              >
                {sendingMessage ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Start Conversation
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar: Messages List */}
        <div className="w-[360px] border-r flex flex-col bg-slate-50 dark:bg-slate-800/30 dark:border-slate-700">
          <div className="p-4 border-b dark:border-slate-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search messages..."
                className="pl-9 pr-4 h-10 dark:bg-slate-700 dark:border-slate-600"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-2 space-y-1.5">
            {clientMessagesLoading && clientMessages.length === 0 ? (
              <div className="flex justify-center items-center py-10">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : filteredMessages.length === 0 ? (
              <div className="text-center py-10 px-4 text-sm text-muted-foreground">
                {clientMessages.length === 0 ? (
                  <div className="flex flex-col items-center">
                    <p className="mb-4">You haven't sent any messages to the team yet.</p>
                  </div>
                ) : (
                  "No messages match your search."
                )}
              </div>
            ) : (
              filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-3 rounded-md cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors border border-transparent",
                    selectedMessageId === message.id ? "bg-slate-200 dark:bg-slate-700" : "",
                  )}
                  onClick={() => setSelectedMessageId(message.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 overflow-hidden">
                      <Avatar className="h-8 w-8 text-xs">
                        <AvatarFallback className="bg-primary/20 text-primary dark:bg-primary/30">
                          {user?.email ? getInitials(user.email) : "U"}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate text-sm">Message to Team</span>
                    </div>
                    {message.response ? (
                      <Badge variant="outline" className="text-xs dark:border-slate-600">Replied</Badge>
                    ) : (
                      <Badge variant="secondary" className="text-xs dark:bg-slate-700">Pending</Badge>
                    )}
                  </div>
                  <p className="mt-1.5 text-xs text-muted-foreground line-clamp-2 dark:text-slate-400">
                    {/* Show the latest message - either a related message response, the main response, or the original message */}
                    {(() => {
                      // Find related messages for this message
                      const relatedMsgs = clientMessages.filter(msg => msg.isReplyTo === message.id);
                      // Sort by timestamp, newest first
                      const sortedRelated = relatedMsgs.sort((a, b) =>
                        new Date(b.responseTimestamp || b.timestamp).getTime() -
                        new Date(a.responseTimestamp || a.timestamp).getTime()
                      );

                      // If there's a related message with a response, show that
                      if (sortedRelated.length > 0 && sortedRelated[0].response) {
                        return sortedRelated[0].response;
                      }
                      // Otherwise, if there's a related message, show that
                      else if (sortedRelated.length > 0) {
                        return sortedRelated[0].message;
                      }
                      // Otherwise, show the response to the main message if it exists
                      else if (message.response) {
                        return message.response;
                      }
                      // Finally, fall back to the original message
                      else {
                        return message.message;
                      }
                    })()}
                  </p>
                  <div className="mt-1.5 flex items-center justify-between">
                    <p className="text-xs text-muted-foreground dark:text-slate-500">
                      {/* Show the timestamp of the most recent interaction */}
                      {(() => {
                        // Find related messages for this message
                        const relatedMsgs = clientMessages.filter(msg => msg.isReplyTo === message.id);
                        // Sort by timestamp, newest first
                        const sortedRelated = relatedMsgs.sort((a, b) =>
                          new Date(b.responseTimestamp || b.timestamp).getTime() -
                          new Date(a.responseTimestamp || a.timestamp).getTime()
                        );

                        // If there's a related message with a response, use its timestamp
                        if (sortedRelated.length > 0 && sortedRelated[0].responseTimestamp) {
                          return format(new Date(sortedRelated[0].responseTimestamp), "MMM d, p");
                        }
                        // Otherwise, if there's a related message, use its timestamp
                        else if (sortedRelated.length > 0) {
                          return format(new Date(sortedRelated[0].timestamp), "MMM d, p");
                        }
                        // Otherwise, use the response timestamp if it exists
                        else if (message.responseTimestamp) {
                          return format(new Date(message.responseTimestamp), "MMM d, p");
                        }
                        // Finally, fall back to the original message timestamp
                        else {
                          return format(new Date(message.timestamp), "MMM d, p");
                        }
                      })()}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Main Content: Message Detail & Reply */}
        <div className="flex-1 flex flex-col bg-white dark:bg-[#111827]">
          {selectedMessage ? (
            <>
              {/* Header for selected message */}
              <div className="p-4 border-b flex items-center justify-between dark:border-slate-700">
                <div>
                  <h3 className="font-semibold text-sm">Conversation with Team</h3>
                  <p className="text-xs text-muted-foreground">
                    Started: {format(new Date(selectedMessage.timestamp), "MMM d, yyyy 'at' p")}
                  </p>
                </div>
                {selectedMessage.response ? (
                  <Badge variant="outline" className="capitalize dark:border-slate-600">
                    Replied
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="capitalize dark:bg-slate-700 dark:text-slate-300">
                    Pending
                  </Badge>
                )}
              </div>

              {/* Chat area */}
              <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* User's Message */}
                <div className="flex items-start gap-3 justify-end">
                  <div className="max-w-[75%] text-right">
                    <div className="bg-primary text-primary-foreground p-3 rounded-lg rounded-tr-none text-left">
                      <div className="text-sm">
                        <SafeMessageContent content={selectedMessage.message} />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {format(new Date(selectedMessage.timestamp), "MMM d, p")}
                    </p>
                  </div>
                  <Avatar className="h-9 w-9">
                    <AvatarFallback className="bg-foreground text-background dark:bg-slate-300 dark:text-slate-800">
                      {user?.email ? getInitials(user.email) : "U"}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* Team's Response */}
                {selectedMessage.response && (
                  <div className="flex items-start gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarFallback className="bg-muted text-muted-foreground dark:bg-slate-700 dark:text-slate-300">
                        TD
                      </AvatarFallback>
                    </Avatar>
                    <div className="max-w-[75%]">
                      <div className="bg-muted p-3 rounded-lg rounded-tl-none dark:bg-slate-700">
                        <div className="text-sm">
                          <SafeMessageContent content={selectedMessage.response} />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {selectedMessage.responseTimestamp
                          ? format(new Date(selectedMessage.responseTimestamp), "MMM d, p")
                          : "No timestamp available"}
                      </p>
                    </div>
                  </div>
                )}

                {/* Related messages (follow-up replies) */}
                {relatedMessages.length > 0 && relatedMessages.map(reply => (
                  <React.Fragment key={reply.id}>
                    {/* User's Reply */}
                    <div className="flex items-start gap-3 justify-end">
                      <div className="max-w-[75%] text-right">
                        <div className="bg-primary text-primary-foreground p-3 rounded-lg rounded-tr-none text-left">
                          <div className="text-sm">
                            <SafeMessageContent content={reply.message} />
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {format(new Date(reply.timestamp), "MMM d, p")}
                        </p>
                      </div>
                      <Avatar className="h-9 w-9">
                        <AvatarFallback className="bg-foreground text-background dark:bg-slate-300 dark:text-slate-800">
                          {user?.email ? getInitials(user.email) : "U"}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    {/* Team's Response to Reply */}
                    {reply.response && (
                      <div className="flex items-start gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarFallback className="bg-muted text-muted-foreground dark:bg-slate-700 dark:text-slate-300">
                            TD
                          </AvatarFallback>
                        </Avatar>
                        <div className="max-w-[75%]">
                          <div className="bg-muted p-3 rounded-lg rounded-tl-none dark:bg-slate-700">
                            <div className="text-sm">
                              <SafeMessageContent content={reply.response} />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {reply.responseTimestamp
                              ? format(new Date(reply.responseTimestamp), "MMM d, p")
                              : "No timestamp available"}
                          </p>
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                ))}

                <div ref={messagesEndRef} /> {/* For scrolling to bottom of input after send */}
              </div>

              {/* Reply input area fixed at bottom */}
              {selectedMessage.response && (
                <div className="border-t p-4 bg-slate-50 dark:bg-slate-800/50 dark:border-slate-700">
                  <div className="relative">
                    <Textarea
                      placeholder="Type your reply here..."
                      className="w-full min-h-[80px] pr-28 resize-none dark:bg-slate-700 dark:border-slate-600 dark:text-slate-100"
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (replyMessage.trim()) {
                            sendReply(selectedMessage.id);
                          }
                        }
                      }}
                      disabled={sendingReply}
                    />
                    <Button
                      onClick={() => sendReply(selectedMessage.id)}
                      disabled={!replyMessage.trim() || sendingReply}
                      className="absolute right-3 bottom-3 px-3 py-1.5 text-sm dark:bg-primary dark:hover:bg-primary/90"
                      size="sm"
                    >
                      {sendingReply ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                      <span className="ml-2 hidden sm:inline">Send Reply</span>
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            // Placeholder when no message is selected
            <div className="flex flex-col items-center justify-center h-full p-6 text-center">
              <div className="bg-muted/30 p-10 rounded-lg border border-dashed dark:border-slate-700/50 flex flex-col items-center max-w-md">
                <MessagesSquare className="h-16 w-16 text-muted-foreground/50 mb-6" />
                <h3 className="text-xl font-medium">Select a message</h3>
                <p className="text-muted-foreground mt-2">
                  {clientMessagesLoading && clientMessages.length === 0
                    ? "Loading messages..."
                    : clientMessages.length === 0
                      ? "No messages found. Start a conversation with the team."
                      : filteredMessages.length === 0
                        ? "No messages match your current search. Try adjusting your search terms."
                        : `Select a message from the list on the left to view details and respond.`
                  }
                </p>

              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
