"use client";

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { FixedSizeList as List } from 'react-window';
import { MessageType, FileInfoType } from "@/lib/models/conversation";

interface OptimizedMessageListProps {
  messages: MessageType[];
  findFileInfoByName: (fileName: string) => FileInfoType | undefined;
  renderMessage: (message: MessageType, findFileInfoByName: (fileName: string) => FileInfoType | undefined) => React.ReactNode;
}

const MESSAGES_PER_PAGE = 50;
const ITEM_HEIGHT = 120;
const CONTAINER_HEIGHT = 600;

export const OptimizedMessageList = React.memo(({
  messages,
  findFileInfoByName,
  renderMessage
}: OptimizedMessageListProps) => {
  const [visibleCount, setVisibleCount] = useState(MESSAGES_PER_PAGE);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const topRef = useRef<HTMLDivElement>(null);
  const prevMessagesLength = useRef(0);

  // Intersection observer for auto-loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && visibleCount < messages.length && !isLoadingMore) {
          setIsLoadingMore(true);
          setTimeout(() => {
            setVisibleCount(prev => Math.min(prev + MESSAGES_PER_PAGE, messages.length));
            setIsLoadingMore(false);
          }, 100);
        }
      },
      { threshold: 0.1 }
    );

    if (topRef.current) observer.observe(topRef.current);
    return () => observer.disconnect();
  }, [visibleCount, messages.length, isLoadingMore]);

  // Reset visible count when messages change significantly
  useEffect(() => {
    if (messages.length <= MESSAGES_PER_PAGE) {
      setVisibleCount(messages.length);
    }
  }, [messages.length]);

  // Auto-scroll to bottom for new messages
  useEffect(() => {
    // Only scroll smoothly if new messages are added, or if it's the initial load below threshold
    const isNewMessageAdded = messages.length > prevMessagesLength.current;
    const isInitialLoadNearBottom = messages.length <= MESSAGES_PER_PAGE && visibleCount >= messages.length;

    if (bottomRef.current && (isNewMessageAdded || isInitialLoadNearBottom)) {
        bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    prevMessagesLength.current = messages.length; // Update for next render
  }, [messages.length, visibleCount]);

  // Load more messages when scrolling up
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop } = e.currentTarget;
    
    if (scrollTop < 100 && visibleCount < messages.length && !isLoadingMore) {
      setIsLoadingMore(true);
      
      // Simulate loading delay for better UX
      setTimeout(() => {
        setVisibleCount(prev => Math.min(prev + MESSAGES_PER_PAGE, messages.length));
        setIsLoadingMore(false);
      }, 200);
    }
  }, [visibleCount, messages.length, isLoadingMore]);

  // Get visible messages (show latest messages first, then load older ones)
  const visibleMessages = useMemo(() => {
    const startIndex = Math.max(0, messages.length - visibleCount);
    return messages.slice(startIndex);
  }, [messages, visibleCount]);

  const MessageItem = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const message = visibleMessages[index];
    if (!message) return null;
    
    return (
      <div style={style}>
        <div className="px-4 py-2">
          {renderMessage(message, findFileInfoByName)}
        </div>
      </div>
    );
  }, [visibleMessages, renderMessage, findFileInfoByName]);

  const hasMoreMessages = visibleCount < messages.length;

  if (messages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-muted-foreground animate-fade-in">Requesting a pentest is easy. Just a few questions for us to understand your pentest requirements.</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto chat-scrollbar"
      onScroll={handleScroll}
    >
      <div className="space-y-4 max-w-5xl mx-auto p-4">
        {/* Load more indicator */}
        {hasMoreMessages && (
          <div ref={topRef} className="text-center py-4">
            {isLoadingMore ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-muted-foreground">Loading older messages...</span>
              </div>
            ) : (
              <button
                onClick={() => {
                  setIsLoadingMore(true);
                  setTimeout(() => {
                    setVisibleCount(prev => Math.min(prev + MESSAGES_PER_PAGE, messages.length));
                    setIsLoadingMore(false);
                  }, 200);
                }}
                className="text-sm text-primary hover:underline"
              >
                Load {Math.min(MESSAGES_PER_PAGE, messages.length - visibleCount)} older messages
              </button>
            )}
          </div>
        )}

        {/* Virtualized message list */}
        {visibleMessages.length > 20 ? (
          <List
            height={CONTAINER_HEIGHT}
            width="100%"
            itemCount={visibleMessages.length}
            itemSize={ITEM_HEIGHT}
            itemData={visibleMessages}
          >
            {MessageItem}
          </List>
        ) : (
          visibleMessages.map((message, index) => {
            const isNewMessage = messages.length > prevMessagesLength.current && index >= visibleMessages.length - (messages.length - prevMessagesLength.current);
            return (
              <div
                key={message.id}
                className={isNewMessage ? "animate-fade-in animate-slide-up" : ""}
                style={{
                  animationDelay: isNewMessage ? `${Math.min(index * 50, 500)}ms` : '0ms',
                  animationFillMode: isNewMessage ? 'both' : 'none'
                }}
              >
                {renderMessage(message, findFileInfoByName)}
              </div>
            );
          })
        )}
        
        <div ref={bottomRef} className="h-px" />
      </div>
    </div>
  );
});

OptimizedMessageList.displayName = 'OptimizedMessageList';