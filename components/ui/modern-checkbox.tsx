"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check, Minus } from "lucide-react"
import { cn } from "@/lib/utils"

export interface ModernCheckboxProps
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  indeterminate?: boolean
  size?: "sm" | "md" | "lg"
  variant?: "default" | "primary" | "success"
}

const ModernCheckbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  ModernCheckboxProps
>(({ className, indeterminate, size = "md", variant = "default", ...props }, ref) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", // Larger than default for better touch targets
    lg: "h-8 w-8"
  }

  const variantClasses = {
    default: "border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground",
    primary: "border-violet data-[state=checked]:bg-violet data-[state=checked]:text-white data-[state=indeterminate]:bg-violet data-[state=indeterminate]:text-white",
    success: "border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:text-white data-[state=indeterminate]:bg-green-500 data-[state=indeterminate]:text-white"
  }

  const iconSize = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        "peer shrink-0 rounded-md border-2 shadow-sm ring-offset-background transition-all duration-200 ease-in-out",
        "modern-focus touch-target",
        "disabled:cursor-not-allowed disabled:opacity-50",
        "hover:border-primary/60 hover:shadow-md hover:shadow-violet/20",
        "data-[state=checked]:border-transparent data-[state=indeterminate]:border-transparent",
        "transform hover:scale-105 active:scale-95",
        "data-[state=checked]:checkbox-bounce-animation",
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      checked={indeterminate ? true : props.checked}
      aria-describedby={props['aria-describedby']}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        forceMount
        className={cn(
          "flex items-center justify-center text-current transition-all duration-200",
          "data-[state=checked]:checkbox-check-animation",
          "data-[state=unchecked]:hidden"
        )}
      >
        {indeterminate ? (
          <Minus className={cn(iconSize[size], "stroke-[3]")} />
        ) : (
          <Check className={cn(iconSize[size], "stroke-[3]")} />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
})

ModernCheckbox.displayName = "ModernCheckbox"

export { ModernCheckbox }