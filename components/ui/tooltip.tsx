"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipProvider = ({ children, ...props }: TooltipPrimitive.TooltipProviderProps) => (
  <TooltipPrimitive.Provider
    delayDuration={300}
    skipDelayDuration={0}
    disableHoverableContent
    {...props}
  >
    {children}
  </TooltipPrimitive.Provider>
)

// Standard uncontrolled tooltip
const Tooltip = TooltipPrimitive.Root

// Controlled tooltip that explicitly manages open state
const ControlledTooltip: React.FC<
  TooltipPrimitive.TooltipProps & { children: React.ReactNode }
> = ({ children, ...props }) => {
  const [open, setOpen] = React.useState(false);

  // Close tooltip when mouse leaves trigger or on click
  const handleMouseLeave = () => {
    setOpen(false);
  };

  const handleClick = () => {
    // Close tooltip after a short delay to ensure the click event completes
    setTimeout(() => {
      setOpen(false);
    }, 100);
  };

  return (
    <TooltipPrimitive.Root
      open={open}
      onOpenChange={setOpen}
      {...props}
    >
      <div
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        className="inline-block"
      >
        {children}
      </div>
    </TooltipPrimitive.Root>
  );
};

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    )}
    {...props}
  />
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, ControlledTooltip, TooltipTrigger, TooltipContent, TooltipProvider }
