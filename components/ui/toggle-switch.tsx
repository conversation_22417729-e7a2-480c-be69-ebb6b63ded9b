"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface ToggleSwitchProps {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  leftLabel: string
  rightLabel: string
  disabled?: boolean
  className?: string
}

export function ToggleSwitch({
  checked,
  onChecked<PERSON>hange,
  leftLabel,
  rightLabel,
  disabled = false,
  className
}: ToggleSwitchProps) {
  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <span className={cn(
        "text-sm font-medium transition-colors",
        !checked ? "text-foreground" : "text-muted-foreground"
      )}>
        {leftLabel}
      </span>
      
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => onCheckedChange(!checked)}
        className={cn(
          "relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",
          checked
            ? "border-primary bg-primary text-background"
            : "border-input bg-input hover:bg-input/80"
        )}
      >
        <span
          className={cn(
            "pointer-events-none inline-block h-5 w-5 transform rounded-full ring-0 transition duration-200 ease-in-out shadow-sm",
            checked ? "translate-x-5 bg-background" : "translate-x-0 bg-foreground"
          )}
        />
      </button>
      
      <span className={cn(
        "text-sm font-medium transition-colors",
        checked ? "text-foreground" : "text-muted-foreground"
      )}>
        {rightLabel}
      </span>
    </div>
  )
}