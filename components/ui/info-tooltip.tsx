"use client";

import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { cn } from "@/lib/utils";

interface InfoTooltipProps {
  children: React.ReactNode;
  info: string;
  triggerClassName?: string;
  contentClassName?: string;
}

export function InfoTooltip({
  children,
  info,
  triggerClassName,
  contentClassName,
}: InfoTooltipProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={cn("flex items-center gap-1", triggerClassName)}>
          {children}
          <Info className="h-3 w-3 text-muted-foreground" />
        </div>
      </TooltipTrigger>
      <TooltipContent className={cn("max-w-xs bg-background border-border shadow-lg", contentClassName)}>
        <p>{info}</p>
      </TooltipContent>
    </Tooltip>
  );
}