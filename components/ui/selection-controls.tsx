"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { ModernCheckbox } from "@/components/ui/modern-checkbox"
import { cn } from "@/lib/utils"
import { CheckSquare, Square, Minus } from "lucide-react"

export interface SelectionControlsProps {
  totalCount: number
  selectedCount: number
  onSelectAll: () => void
  onSelectNone: () => void
  className?: string
  showCounter?: boolean
  disabled?: boolean
}

export function SelectionControls({
  totalCount,
  selectedCount,
  onSelectAll,
  onSelectNone,
  className,
  showCounter = true,
  disabled = false
}: SelectionControlsProps) {
  const isAllSelected = selectedCount === totalCount && totalCount > 0
  const isPartiallySelected = selectedCount > 0 && selectedCount < totalCount
  const isNoneSelected = selectedCount === 0

  const handleToggleAll = () => {
    if (isAllSelected || isPartiallySelected) {
      onSelectNone()
    } else {
      onSelectAll()
    }
  }

  return (
    <div className={cn(
      "flex items-center justify-between gap-4 p-4 bg-muted/30 rounded-lg border border-border/50",
      "transition-all duration-200 ease-in-out selection-controls-enter",
      selectedCount > 0 && "bulk-actions-panel",
      className
    )}>
      <div className="flex items-center gap-3">
        <ModernCheckbox
          checked={isAllSelected}
          indeterminate={isPartiallySelected}
          onCheckedChange={handleToggleAll}
          disabled={disabled || totalCount === 0}
          variant="primary"
          size="md"
          aria-label={
            isAllSelected 
              ? "Deselect all items" 
              : isPartiallySelected 
                ? "Select all items" 
                : "Select all items"
          }
        />
        
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-foreground">
              {isAllSelected 
                ? "All items selected" 
                : isPartiallySelected 
                  ? "Some items selected" 
                  : "Select items"
              }
            </span>
            {showCounter && (
              <span className={cn(
                "text-xs px-2 py-1 rounded-full font-medium transition-all duration-200",
                "selection-counter-pulse",
                selectedCount > 0
                  ? "bg-violet/10 text-violet border border-violet/20 shadow-sm"
                  : "bg-muted text-muted-foreground"
              )}>
                {selectedCount} of {totalCount}
              </span>
            )}
          </div>
          {totalCount > 0 && (
            <span className="text-xs text-muted-foreground">
              {isAllSelected 
                ? "Click to deselect all" 
                : isPartiallySelected 
                  ? "Click to select all remaining" 
                  : "Click to select all items"
              }
            </span>
          )}
        </div>
      </div>

      {selectedCount > 0 && (
        <div className="flex items-center gap-2 animate-fade-in">
          <Button
            variant="outline"
            size="sm"
            onClick={onSelectAll}
            disabled={disabled || isAllSelected}
            className="h-8 px-3 text-xs hover:bg-violet/10 hover:border-violet/30 transition-all duration-200 modern-focus"
          >
            <CheckSquare className="h-3 w-3 mr-1" />
            All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onSelectNone}
            disabled={disabled || isNoneSelected}
            className="h-8 px-3 text-xs hover:bg-muted transition-all duration-200 modern-focus"
          >
            <Square className="h-3 w-3 mr-1" />
            None
          </Button>
        </div>
      )}
    </div>
  )
}

// Keyboard shortcuts hook for selection controls
export function useSelectionKeyboard(
  onSelectAll: () => void,
  onSelectNone: () => void,
  disabled: boolean = false
) {
  React.useEffect(() => {
    if (disabled) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + A to select all
      if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
        event.preventDefault()
        onSelectAll()
      }
      
      // Escape to deselect all
      if (event.key === 'Escape') {
        event.preventDefault()
        onSelectNone()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onSelectAll, onSelectNone, disabled])
}