"use client";

import { useEffect, useRef } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useScans } from "@/context/ScanContext";

/**
 * This component preloads team dashboard data in the background
 * It doesn't render anything visible, just loads data when a manager logs in
 */
export function TeamDataPreloader() {
  const { user, role, loading: authLoading } = useAuth();
  const { fetchData: fetchOrganizationScans } = useScans();

  // Use a ref to track if we've already loaded the data
  const hasLoadedRef = useRef(false);

  // Use a ref to track loading attempts
  const loadingAttemptsRef = useRef(0);

  // Use a ref to track if we're currently loading
  const isLoadingRef = useRef(false);

  // Use a ref to track the last time we loaded data
  const lastLoadTimeRef = useRef(0);

  // Store the user ID to detect changes
  const userIdRef = useRef<string | null>(null);

  // Global variable to track if preload has been attempted
  // This ensures we only try to preload once per session, even across component remounts
  const hasAttemptedPreloadRef = useRef(false);

  // Preload team data when a manager logs in, but only once per session
  useEffect(() => {
    // Skip if no user or not a manager
    if (!user || role !== "manager" || authLoading) {
      return;
    }

    // Skip if we've already attempted to preload data in this session
    if (hasAttemptedPreloadRef.current) {
      console.log("Already attempted to preload data in this session, skipping");
      return;
    }

    // Mark that we've attempted to preload data
    hasAttemptedPreloadRef.current = true;

    // Skip if this is the same user we've already processed
    if (userIdRef.current === user.uid && hasLoadedRef.current) {
      console.log("Data already loaded for this user, skipping preload");
      return;
    }

    // Update the user ID ref
    userIdRef.current = user.uid;

    // Create a function to load data that we can call only once
    const preloadData = async () => {
      // Double-check that we're not already loading
      if (isLoadingRef.current) {
        console.log("Already preloading team data, skipping duplicate call");
        return;
      }

      // Check if we've recently loaded data (within the last 30 minutes)
      const now = Date.now();
      const timeSinceLastLoad = now - lastLoadTimeRef.current;

      if (timeSinceLastLoad < 1800000 && hasLoadedRef.current) { // 30 minutes
        console.log(`Skipping preload - last load was ${Math.round(timeSinceLastLoad / 1000)} seconds ago`);
        return;
      }

      // Set loading flag to prevent multiple simultaneous loads
      isLoadingRef.current = true;

      try {
        console.log("Preloading team dashboard data in the background for user:", user.uid);
        console.log("Loading attempt:", loadingAttemptsRef.current + 1);

        // Increment the loading attempts counter
        loadingAttemptsRef.current += 1;

        // Update the last load time
        lastLoadTimeRef.current = now;

        // Load team scans in the background
        await fetchOrganizationScans('organization');

        console.log("Team data preloaded successfully");

        // Mark that we've loaded data successfully
        hasLoadedRef.current = true;
      } catch (err) {
        console.error("Error preloading team dashboard data:", err);
        // Don't reset hasLoadedRef here to prevent repeated attempts on failure
      } finally {
        // Reset loading flag
        isLoadingRef.current = false;
      }
    };

    // Use a longer timeout to ensure the main component has a chance to load first
    const timeoutId = setTimeout(() => {
      preloadData();
    }, 2000); // 2 seconds delay to ensure main component loads first

    // Clean up the timeout if the component unmounts
    return () => clearTimeout(timeoutId);
  }, [user?.uid]); // Only depend on user ID to ensure this effect runs exactly once per user

  // This component doesn't render anything visible
  return null;
}
