import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronDown, ChevronUp, Building, Mail, Clock, AlertCircle, RefreshCw, CheckCircle2, ShieldAlert, User, Users, Activity, TrendingUp, Briefcase } from "lucide-react";
import { ScanCardModal } from "@/components/scan-card-modal";
import { UserInfo, ScanType } from "@/types/scan-types";
import { cn } from "@/lib/utils";
import "@/styles/grouped-scans.css";

interface OrganizationGroupedScansListProps {
    organizations: {
        name: string;
        users: UserInfo[];
    }[];
    emptyMessage: string;
    onStatusChange: () => void;
    activeTab: string;
}

// Interface for status counts
interface StatusCounts {
    pending: number;
    inProgress: number;
    reTest: number;
    completed: number;
    critical: number;
}

export function OrganizationGroupedScansList({ organizations, emptyMessage, onStatusChange, activeTab }: OrganizationGroupedScansListProps) {
    const [expandedOrgs, setExpandedOrgs] = useState<Record<string, boolean>>({});
    const [expandedUsers, setExpandedUsers] = useState<Record<string, boolean>>({});

    // Toggle expansion state for an organization
    const toggleOrgExpansion = (orgName: string) => {
        setExpandedOrgs(prev => ({
            ...prev,
            [orgName]: !prev[orgName]
        }));
    };

    // Toggle expansion state for a user
    const toggleUserExpansion = (userId: string) => {
        setExpandedUsers(prev => ({
            ...prev,
            [userId]: !prev[userId]
        }));
    };

    // Check if an organization is expanded
    const isOrgExpanded = (orgName: string) => {
        return !!expandedOrgs[orgName];
    };

    // Check if a user is expanded
    const isUserExpanded = (userId: string) => {
        return !!expandedUsers[userId];
    };

    // Use the utility function for status counts with memoization
    const getStatusCountsMemo = useMemo(() => {
        const cache = new Map<string, StatusCounts>();

        return (scans: ScanType[]): StatusCounts => {
            // Create a cache key based on scan IDs and statuses
            const cacheKey = scans.map(s => `${s.id}:${s.status}:${s.criticalVulnerabilities}`).join('|');

            if (cache.has(cacheKey)) {
                return cache.get(cacheKey)!;
            }

            // Calculate counts manually to avoid dependency issues
            const counts = scans.reduce((acc, scan) => {
                if (scan.status === 'pending') acc.pending++;
                else if (scan.status === 'in-progress') acc.inProgress++;
                else if (scan.status === 're-test') acc.reTest++;
                else if (scan.status === 'completed') acc.completed++;

                // Count critical vulnerabilities
                if (scan.criticalVulnerabilities > 0) {
                    acc.critical++;
                }

                return acc;
            }, {
                pending: 0,
                inProgress: 0,
                reTest: 0,
                completed: 0,
                critical: 0
            } as StatusCounts);

            cache.set(cacheKey, counts);
            return counts;
        };
    }, []);

    // Calculate total scans for an organization with memoization
    const getOrgScanCounts = useMemo(() => {
        const cache = new Map<string, StatusCounts>();

        return (users: UserInfo[]): StatusCounts => {
            // Create a cache key based on user IDs
            const cacheKey = users.map(u => u.userId).join('|');

            if (cache.has(cacheKey)) {
                return cache.get(cacheKey)!;
            }

            const allScans = users.flatMap(user => user.scans);

            // Calculate counts manually to avoid dependency issues
            const counts = allScans.reduce((acc, scan) => {
                if (scan.status === 'pending') acc.pending++;
                else if (scan.status === 'in-progress') acc.inProgress++;
                else if (scan.status === 're-test') acc.reTest++;
                else if (scan.status === 'completed') acc.completed++;

                // Count critical vulnerabilities
                if (scan.criticalVulnerabilities > 0) {
                    acc.critical++;
                }

                return acc;
            }, {
                pending: 0,
                inProgress: 0,
                reTest: 0,
                completed: 0,
                critical: 0
            } as StatusCounts);

            cache.set(cacheKey, counts);
            return counts;
        };
    }, []);

    // Calculate total number of scans for an organization
    const getOrgScanCount = (users: UserInfo[]): number => {
        return users.reduce((total, user) => total + user.scans.length, 0);
    };

    if (organizations.length === 0) {
        return (
            <Card className="glass-effect enhanced-shadow">
                <CardContent className="flex flex-col items-center justify-center py-16">
                    <div className="flex flex-col items-center space-y-4">
                        <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center border border-border/50">
                            <Building className="h-8 w-8 text-muted-foreground/50" />
                        </div>
                        <div className="text-center space-y-2">
                            <p className="text-lg font-medium text-muted-foreground">{emptyMessage}</p>
                            <p className="text-sm text-muted-foreground/60">Organization scans will appear here when available</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {organizations.map((org, orgIndex) => {
                const filteredUsers = org.users.map(user => {
                    const filteredScans = user.scans.filter(scan => {
                        if (activeTab === 'all') return true;
                        return scan.status === activeTab;
                    });
                    return filteredScans.length > 0 ? { ...user, scans: filteredScans } : null;
                }).filter(Boolean) as UserInfo[]; // Cast to UserInfo[] after filtering nulls

                if (filteredUsers.length === 0) {
                    return null;
                }

                return (
                    <Card
                        key={org.name}
                        className={cn(
                            "w-full transition-all duration-300 glass-effect animate-fade-in shadow-sm hover:shadow-md mb-6",
                            "group relative overflow-hidden"
                        )}
                        style={{ animationDelay: `${orgIndex * 150}ms` }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <CardHeader
                            className="pb-4 pt-5 px-6 cursor-pointer hover:bg-muted/30 transition-all duration-200"
                            onClick={() => toggleOrgExpansion(org.name)}
                        >
                            <div className="flex items-start justify-between">
                                <div className="space-y-3">
                                    <div className="flex flex-wrap items-center gap-3">
                                        <Badge
                                            variant="outline"
                                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                                        >
                                            <Building className="h-3.5 w-3.5" />
                                            Organization
                                        </Badge>
                                        {(() => {
                                            const counts = getOrgScanCounts(filteredUsers);
                                            const totalScans = getOrgScanCount(filteredUsers);
                                            if (totalScans > 0) {
                                                return (
                                                    <Badge
                                                        variant="outline"
                                                        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-700 dark:text-slate-300"
                                                    >
                                                        <Activity className="h-3.5 w-3.5" />
                                                        {totalScans} {totalScans === 1 ? 'Scan' : 'Scans'}
                                                    </Badge>
                                                );
                                            }
                                            return null;
                                        })()}
                                        {(() => {
                                            const counts = getOrgScanCounts(filteredUsers);
                                            return (
                                                <>
                                                    {counts.pending > 0 && (
                                                        <Badge
                                                            variant="outline"
                                                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-yellow-50 dark:bg-yellow-950/30 border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-300"
                                                        >
                                                            <Clock className="h-3.5 w-3.5" />
                                                            {counts.pending} Pending
                                                        </Badge>
                                                    )}
                                                    {counts.inProgress > 0 && (
                                                        <Badge
                                                            variant="outline"
                                                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                                                        >
                                                            <AlertCircle className="h-3.5 w-3.5" />
                                                            {counts.inProgress} In Progress
                                                        </Badge>
                                                    )}
                                                    {counts.reTest > 0 && (
                                                        <Badge
                                                            variant="outline"
                                                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-purple-50 dark:bg-purple-950/30 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300"
                                                        >
                                                            <RefreshCw className="h-3.5 w-3.5" />
                                                            {counts.reTest} Re-test
                                                        </Badge>
                                                    )}
                                                    {counts.completed > 0 && (
                                                        <Badge
                                                            variant="outline"
                                                            className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300"
                                                        >
                                                            <CheckCircle2 className="h-3.5 w-3.5" />
                                                            {counts.completed} Completed
                                                        </Badge>
                                                    )}
                                                </>
                                            );
                                        })()}
                                    </div>
                                    <CardTitle className="text-lg font-semibold leading-tight tracking-tight uppercase">
                                        {org.name}
                                    </CardTitle>
                                    <div className="flex flex-wrap items-center gap-x-6 gap-y-2.5 text-xs">
                                        <div className="flex items-center gap-2 text-muted-foreground/90">
                                            <Users className="h-3.5 w-3.5 text-muted-foreground" />
                                            <span>{filteredUsers.length} {filteredUsers.length === 1 ? 'user' : 'users'}</span>
                                        </div>
                                    </div>
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="gap-2 flex-1 max-w-[140px] h-9 bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-all duration-200 rounded-md shadow-sm hover:scale-105"
                                    onClick={(e) => { e.stopPropagation(); toggleOrgExpansion(org.name); }}
                                >
                                    <ChevronDown className={cn(
                                        "h-3.5 w-3.5 transition-transform duration-300 ease-in-out",
                                        isOrgExpanded(org.name) && "rotate-180"
                                    )} />
                                    {isOrgExpanded(org.name) ? "Hide Users" : "View Users"}
                                </Button>
                            </div>
                        </CardHeader>

                        <CardContent className={cn(
                            "transition-all duration-500 ease-in-out overflow-hidden border-t border-border/40",
                            isOrgExpanded(org.name) 
                                ? "max-h-[2000px] opacity-100 py-4 px-6" 
                                : "max-h-0 opacity-0 py-0 px-6 border-t-0"
                        )}>
                            {isOrgExpanded(org.name) && (
                                <div className="space-y-5 animate-fade-in pb-2" style={{ animationDelay: '100ms' }}>
                                    {filteredUsers.map((user, userIndex) => user && (
                                        <div
                                            key={user.userId}
                                            className="animate-slide-up"
                                            style={{ 
                                                animationDelay: `${Math.min(userIndex * 100 + 200, 800)}ms`,
                                                animationFillMode: 'both'
                                            }}
                                        >
                                            <Card
                                                className={cn(
                                                    "w-full transition-all duration-300 glass-effect shadow-sm hover:shadow-md ring-1 ring-purple-500/30 cursor-pointer"
                                                )}
                                                onClick={() => toggleUserExpansion(user.userId)}
                                            >
                                            <CardHeader className="pb-4 pt-5 px-6">
                                                <div className="flex items-start justify-between">
                                                    <div className="space-y-3">
                                                        <div className="flex flex-wrap items-center gap-3">
                                                            <Badge
                                                                variant="outline"
                                                                className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300"
                                                            >
                                                                <User className="h-3.5 w-3.5" />
                                                                User
                                                            </Badge>
                                                            {user.scans.length > 0 && (
                                                                <Badge
                                                                    variant="outline"
                                                                    className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-700 dark:text-slate-300"
                                                                >
                                                                    <Activity className="h-3.5 w-3.5" />
                                                                    {user.scans.length} {user.scans.length === 1 ? 'Scan' : 'Scans'}
                                                                </Badge>
                                                            )}
                                                            {(() => {
                                                                const userCounts = getStatusCountsMemo(user.scans);
                                                                return (
                                                                    <>
                                                                        {userCounts.pending > 0 && (
                                                                            <Badge
                                                                                variant="outline"
                                                                                className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-yellow-50 dark:bg-yellow-950/30 border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-300"
                                                                            >
                                                                                <Clock className="h-3.5 w-3.5" />
                                                                                {userCounts.pending} Pending
                                                                            </Badge>
                                                                        )}
                                                                        {userCounts.inProgress > 0 && (
                                                                            <Badge
                                                                                variant="outline"
                                                                                className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                                                                            >
                                                                                <AlertCircle className="h-3.5 w-3.5" />
                                                                                {userCounts.inProgress} In Progress
                                                                            </Badge>
                                                                        )}
                                                                        {userCounts.reTest > 0 && (
                                                                            <Badge
                                                                                variant="outline"
                                                                                className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-purple-50 dark:bg-purple-950/30 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300"
                                                                            >
                                                                                <RefreshCw className="h-3.5 w-3.5" />
                                                                                {userCounts.reTest} Re-test
                                                                            </Badge>
                                                                        )}
                                                                        {userCounts.completed > 0 && (
                                                                            <Badge
                                                                                variant="outline"
                                                                                className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-green-50 dark:bg-green-950/30 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300"
                                                                            >
                                                                                <CheckCircle2 className="h-3.5 w-3.5" />
                                                                                {userCounts.completed} Completed
                                                                            </Badge>
                                                                        )}
                                                                    </>
                                                                );
                                                            })()}
                                                        </div>
                                                        <CardTitle className="text-sm font-semibold leading-tight tracking-tight">
                                                            {user.displayName || user.email}
                                                        </CardTitle>
                                                        <div className="flex flex-wrap items-center gap-x-6 gap-y-2.5 text-xs">
                                                            <div className="flex items-center gap-2 text-muted-foreground/90">
                                                                <Mail className="h-3.5 w-3.5 text-muted-foreground" />
                                                                <span>{user.email}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="gap-2 flex-1 max-w-[140px] h-9 bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-all duration-200 rounded-md shadow-sm hover:scale-105"
                                                        onClick={(e) => { e.stopPropagation(); toggleUserExpansion(user.userId); }}
                                                    >
                                                        <ChevronDown className={cn(
                                                            "h-3.5 w-3.5 transition-transform duration-300 ease-in-out",
                                                            isUserExpanded(user.userId) && "rotate-180"
                                                        )} />
                                                        {isUserExpanded(user.userId) ? "Hide Scans" : "View Scans"}
                                                    </Button>
                                                </div>
                                            </CardHeader>

                                                <CardContent
                                                    className={cn(
                                                        "transition-all duration-500 ease-in-out overflow-hidden border-t border-border/40",
                                                        isUserExpanded(user.userId) 
                                                            ? "max-h-[2000px] opacity-100 py-4 px-6" 
                                                            : "max-h-0 opacity-0 py-0 px-6 border-t-0"
                                                    )}
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    {isUserExpanded(user.userId) && (
                                                        user.scans.length > 0 ? (
                                                            <div className="space-y-5 animate-fade-in pb-2">
                                                                {user.scans.map((scan, scanIndex) => (
                                                                    <div
                                                                        key={scan.id}
                                                                        className="animate-slide-up"
                                                                        style={{ 
                                                                            animationDelay: `${Math.min(scanIndex * 50 + 100, 600)}ms`, 
                                                                            animationFillMode: 'both' 
                                                                        }}
                                                                    >
                                                                        <ScanCardModal
                                                                            scan={scan}
                                                                            onStatusChange={onStatusChange}
                                                                        />
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            <div className="text-center py-10 text-muted-foreground animate-fade-in">
                                                                <div className="flex flex-col items-center space-y-3">
                                                                    <div className="h-10 w-10 rounded-full bg-muted/50 flex items-center justify-center">
                                                                        <Activity className="h-5 w-5 text-muted-foreground/50" />
                                                                    </div>
                                                                    <p className="font-medium">No scans available for this user</p>
                                                                    <p className="text-sm text-muted-foreground/60">Scans will appear here once they are created</p>
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </CardContent>
                                            </Card>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                );
            })}
        </div>
    );
}
