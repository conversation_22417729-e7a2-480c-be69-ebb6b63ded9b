"use client";

import React, { useState, useRef, useEffect, useCallback, useMemo, ChangeEvent, KeyboardEvent, useTransition, useDeferredValue, startTransition } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, ShieldAlert, Bot, User as UserIcon, Plus, Clock, Loader2, Paperclip, FileText, Download, CheckCircle2, FileDown} from "lucide-react";
import { cn } from "@/lib/utils";
import { MAX_FILE_SIZE, validateFileExtension, formatFileSize } from "@/lib/file-utils";
import { format } from "date-fns";
import { useAuth, User } from "@/hooks/useAuth";
import { useScans } from "@/context/ScanContext";
import { useConversations } from "@/context/ConversationsContext";
import { useMessageQueue } from "@/context/MessageQueueContext";
import { useIsMobile } from "@/hooks/use-mobile";
import { usePreloadRoutes } from "@/hooks/use-preload";
import { ChatScanCard } from "@/components/chat-scan-card";
import { toast } from "sonner";
import { OptimizedMessageList } from "./chat-message-virtualized";
import { OptimizedConversationHistory } from "./optimized-conversation-history";
import {
    ConversationType,
    MessageType,
    FileInfoType,
    MessageContent,
    ScanCardContent,
    DownloadCardContent,
    AiResponseContent,
    FileUploadContent,
    SystemNotificationContent
} from "@/lib/models/conversation";

// --- Constants ---
const API_EXTRACT_SCAN_FIELDS = "/api/extract-scan-fields";
const API_SCANS = "/api/scans";
const API_CONVERSATION_ASSOCIATE_SCAN = (conversationId: string) => `/api/conversations/${conversationId}/associate-scan`;

const TITLE_PENTEST_SCAN_REQUEST = "Pentest Request";

const MessageRole = { USER: 'user', ASSISTANT: 'assistant', SYSTEM: 'system' } as const;
const FileUploadStatus = { UPLOADING: 'uploading', UPLOADED: 'uploaded', FAILED: 'failed' } as const;
const SystemNotificationSeverity = { ERROR: 'error', INFO: 'info' } as const;

const APP_TYPE_KEYWORDS = [
    'web application', 'mobile app', 'api', 'network', 'cloud infrastructure',
    'web app', 'website', 'mobile application', 'ios app', 'android app',
    'web', 'mobile', 'desktop', 'cloud', 'server', 'application'
];

// --- Internal Custom Hooks ---
interface DetectedScanInfo {
    appType: string | null;
    appName: string | null;
    appUrl: string | null;
}

function useScanInfoDetector(messages: MessageType[]) {
    const [scanInfoProvided, setScanInfoProvided] = useState<DetectedScanInfo>({ appType: null, appName: null, appUrl: null });

    useEffect(() => {
        const hasScanCard = messages.some(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard);
        if (hasScanCard) { setScanInfoProvided({ appType: "Confirmed by Scan Card", appName: "Confirmed by Scan Card", appUrl: "Confirmed by Scan Card" }); return; }

        let detectedAppType: string | null = null;
        let detectedAppName: string | null = null;
        let detectedAppUrl: string | null = null;

        const assistantMessages = messages.filter(m => m.role === MessageRole.ASSISTANT);
        const latestAssistantMessage = assistantMessages.length > 0 ? assistantMessages[assistantMessages.length - 1] : null;

        if (latestAssistantMessage && typeof latestAssistantMessage.content === 'object' && (latestAssistantMessage.content as AiResponseContent).scanInfoFlags) {
            const flags = (latestAssistantMessage.content as AiResponseContent).scanInfoFlags!;
            const aiText = (latestAssistantMessage.content as AiResponseContent).text;
            if (flags.appType) { const match = aiText.match(/(?:application|asset)\s+type\s*:\s*([^\n<]+)/i); if (match && match[1]?.trim()) detectedAppType = match[1].trim(); }
            if (flags.appName) { const match = aiText.match(/(?:application|asset)\s+name\s*:\s*([^\n<]+)/i); if (match && match[1]?.trim()) detectedAppName = match[1].trim(); }
            if (flags.appUrl) { const match = aiText.match(/(https?:\/\/[^\s]+)|([a-z0-9][a-z0-9-]{1,61}[a-z0-9]\.[a-z]{2,})\b/i); if (match && match[0]?.trim()) detectedAppUrl = match[0].trim(); }
            if (detectedAppType || detectedAppName || detectedAppUrl) { setScanInfoProvided({ appType: detectedAppType, appName: detectedAppName, appUrl: detectedAppUrl }); return; }
        }

        const userMessages = messages.filter(m => m.role === MessageRole.USER && typeof m.content === 'string');
        const hasSubmitKeyword = userMessages.some(m => typeof m.content === 'string' && /\bsubmit\b/i.test(m.content));

        if (!detectedAppType) {
            for (const m of userMessages) {
                const cl = (m.content as string).toLowerCase();
                const match = APP_TYPE_KEYWORDS.find(kw => cl.includes(kw));
                if (match) { detectedAppType = match; break; }
                const typeMatch = (m.content as string).match(/\b(asset|application)?\s*type\s*:\s*([^\n<]+)/i);
                if (typeMatch && typeMatch[2]?.trim()) { detectedAppType = typeMatch[2].trim(); break; }
            }
        }
        if (!detectedAppName) {
            for (const m of userMessages) {
                const nameMatch = (m.content as string).match(/\b(?:app(lication)?|asset)?\s*name\s*:\s*([^\n<]+)/i);
                if (nameMatch && nameMatch[2]?.trim()) { detectedAppName = nameMatch[2].trim(); break; }
                const calledNamedMatch = (m.content as string).match(/\b(called|named)\s+([^\n<]+)/i);
                if (calledNamedMatch && calledNamedMatch[2]?.trim()) { detectedAppName = calledNamedMatch[2].trim(); break; }
            }
            if (!detectedAppName) {
                for (const m of assistantMessages) {
                    const mc = m.content;
                    let text = typeof mc === 'string' ? mc : (mc && typeof mc === 'object' && 'text' in mc && typeof (mc as any).text === 'string') ? (mc as AiResponseContent).text : '';
                    if (text) {
                        const nameMatch = text.match(/\b(?:application|asset)\s+name\s*:\s*([^\n<]+)/i);
                        if (nameMatch && nameMatch[1]?.trim() && nameMatch[1].trim().toLowerCase() !== "unknown") { detectedAppName = nameMatch[1].trim(); break; }
                    }
                }
            }
        }
        if (!detectedAppUrl) {
            for (const m of userMessages) {
                const urlMatch = (m.content as string).match(/(https?:\/\/[^\s]+)|(\b[a-z0-9][a-z0-9-]{1,61}[a-z0-9]\.[a-z]{2,}(\/[^\s]*)?)\b/i);
                if (urlMatch && urlMatch[0]?.trim()) { detectedAppUrl = urlMatch[0].trim(); break; }
            }
        }

        if (hasSubmitKeyword) {
            setScanInfoProvided({ appType: detectedAppType || "Provided (via submit)", appName: detectedAppName || "Provided (via submit)", appUrl: detectedAppUrl || "Provided (via submit)" });
        } else {
            setScanInfoProvided({ appType: detectedAppType, appName: detectedAppName, appUrl: detectedAppUrl });
        }
    }, [messages]);

    const allScanInfoProvided = useMemo(() => scanInfoProvided.appType !== null && scanInfoProvided.appUrl !== null, [scanInfoProvided]);
    return { scanInfoProvided, allScanInfoProvided };
}

interface UseScanSubmissionProps {
    user: User | null;
    activeConversationId: string | null;
    activeConversation: ConversationType | null;
    messages: MessageType[];
    allScanInfoProvided: boolean;
    scanInfoProvided: DetectedScanInfo;
    addMessageToConversation: (message: Omit<MessageType, "id" | "timestamp">) => Promise<string>;
    fetchScans: () => void;
    updateConversationTitle: (id: string, title: string) => Promise<boolean>;
    isChatDisabled: boolean;
}

function useScanSubmission({
    user, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided,
    addMessageToConversation, fetchScans, updateConversationTitle, isChatDisabled
}: UseScanSubmissionProps) {
    const [isSubmittingScan, setIsSubmittingScan] = useState(false);

    const _extractFields = useCallback(async (idToken: string) => {
        const convText = messages.map(m => `${m.role}: ${prepareContentForAI(m.content)}`).join("\n\n");
        const res = await fetch(API_EXTRACT_SCAN_FIELDS, { method: "POST", headers: { "Content-Type": "application/json", 'Authorization': `Bearer ${idToken}` }, body: JSON.stringify({ conversation: convText }) });
        if (!res.ok) { const d = await res.json().catch(() => ({})); throw new Error(d.error || `Extract failed (${res.status})`); }
        const { fields } = await res.json();
        if (!fields || typeof fields !== 'object') throw new Error("Extraction failed: Invalid data.");
        return fields;
    }, [messages]);

    const _validatePayload = useCallback((fields: any, currentMessages: MessageType[], currentActiveConv: ConversationType | null) => {
        let assetType = fields.asset_type || fields['Scope definition'];
        if (!assetType) throw new Error("Could not determine asset type.");
        if (!fields.targetDetails || String(fields.targetDetails).trim() === '') {
            const targetMsg = currentMessages.find(m => m.role === MessageRole.USER && typeof m.content === 'string' && (m.content.includes("target") || m.content.includes("url") || m.content.includes("http")));
            fields.targetDetails = targetMsg && typeof targetMsg.content === 'string' ? (targetMsg.content.match(/(https?:\/\/[^\s]+)|([a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,})/)?.[0] || `${assetType.replace(/_/g, ' ')} target`) : `${assetType.replace(/_/g, ' ')} target`;
        }
        const reqFields = ['targetDetails', 'function', 'contact_name', 'contact_email'];
        if (!reqFields.some(f => fields[f]?.trim())) throw new Error("Missing required scan details.");
        const files = currentActiveConv?.fileInfo?.map(f => ({ ...f, uploadedAt: typeof f.uploadedAt === 'string' ? f.uploadedAt : new Date(f.uploadedAt).toISOString(), contentType: f.contentType || 'application/octet-stream' })) || [];
        return { ...fields, asset_type: assetType, files, conversationId: currentActiveConv?.id };
    }, []);

    const _submitToServer = useCallback(async (scanData: any, idToken: string) => {
        const res = await fetch(API_SCANS, { method: "POST", headers: { "Content-Type": "application/json", 'Authorization': `Bearer ${idToken}` }, body: JSON.stringify(scanData) });
        const d = await res.json().catch(() => ({}));
        if (!res.ok) throw new Error(d.error || `Scan creation failed (${res.status})`);
        return d.scan?.scan_id || d.scanId;
    }, []);

    const _associateScan = useCallback(async (scanId: string, convId: string, idToken: string) => {
        try { await fetch(API_CONVERSATION_ASSOCIATE_SCAN(convId), { method: "POST", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` }, body: JSON.stringify({ scanId }), cache: 'no-store' }); }
        catch (e) { console.warn('[Scan Submission] Error associating scan:', e); }
    }, []);

    const _handleError = useCallback(async (error: any) => {
        console.error("[Scan Submission] Error:", error);
        let userMsg = "Issue submitting scan.";
        let detailMsg = error.message || "Unknown error.";
        if (detailMsg.includes("rate_limit") || detailMsg.includes("OpenAI")) userMsg = "AI service busy. Try again.";
        else if (detailMsg.includes("Missing required")) userMsg = "Provide more scan details.";
        else if (detailMsg.includes("asset type")) userMsg = "Specify asset type (Web App, etc.).";
        else if (detailMsg.includes("targetDetails")) userMsg = "Specify target (URL, domain).";
        else if (detailMsg.includes("500")) userMsg = "Service technical difficulties.";

        try { await addMessageToConversation({ role: MessageRole.SYSTEM, content: { isSystemNotification: true, text: `${userMsg} ${detailMsg}`, severity: 'error' } }); }
        catch (e) { console.error("Failed to add error message:", e); }
        toast.error(userMsg);
    }, [addMessageToConversation]);

    const handleSubmitScanRequest = useCallback(async () => {
        if (isSubmittingScan || !activeConversationId || !user || isChatDisabled) return;
        setIsSubmittingScan(true);
        let analysisToastId: string | number | undefined;

        try {
            if (!allScanInfoProvided) {
                const missing = [];
                if (scanInfoProvided.appType === null) missing.push("application type");
                if (scanInfoProvided.appUrl === null) missing.push("target URL/location");
                if (missing.length > 0) {
                    toast.error(`Please provide: ${missing.join(", ")}.`);
                    setIsSubmittingScan(false);
                    return;
                }
            }

            analysisToastId = toast.loading("Analyzing your request details with AI...", { description: "This may take a moment." });
            const idToken = await user.getIdToken(true);
            const fields = await _extractFields(idToken);
            if (analysisToastId) toast.success("Scan details analyzed successfully.", { id: analysisToastId });

            const payload = _validatePayload(fields, messages, activeConversation);
            const scanId = await _submitToServer(payload, idToken);
            if (!scanId) throw new Error("Failed to get scan ID.");

            const tempScanCardTitle = fields.conversationTitle || `${payload.asset_type || 'Untitled'} Scan for ${payload.targetDetails || 'N/A'}`;
            await addMessageToConversation({ role: MessageRole.ASSISTANT, content: { isScanCard: true, scanId, assetType: payload.asset_type, target: payload.targetDetails || 'N/A', status: "pending", timestamp: new Date().toISOString(), name: tempScanCardTitle } });
            if (activeConversationId) await _associateScan(scanId, activeConversationId, idToken);
            toast.success("Scan request submitted!");
            fetchScans();
            if (updateConversationTitle && activeConversationId && activeConversation && fields.conversationTitle) {
                const newTitle = fields.conversationTitle;
                await updateConversationTitle(activeConversationId, newTitle);
                try { await fetch(`/api/scans/${scanId}/update`, { method: "PUT", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` }, body: JSON.stringify({ conversationTitle: newTitle }), cache: 'no-store' }); }
                catch (e) { console.warn("Failed to update scan title directly:", e); }
            }
        } catch (error: any) {
            if (analysisToastId) toast.error("Failed to analyze scan details.", { id: analysisToastId, description: error.message || "Unknown error." });
            await _handleError(error);
        } finally {
            setIsSubmittingScan(false);
            if (analysisToastId) toast.dismiss(analysisToastId);
        }
    }, [
        user, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided, isChatDisabled, isSubmittingScan,
        _extractFields, _validatePayload, _submitToServer, _associateScan, _handleError,
        addMessageToConversation, fetchScans, updateConversationTitle
    ]);
    return { handleSubmitScanRequest, isSubmittingScan };
}

// --- Helper: Prepare content for AI ---
const prepareContentForAI = (content: MessageContent): string => {
    if (typeof content === 'string') return content;
    if (content && typeof content === 'object') {
        if ('text' in content && typeof (content as AiResponseContent).text === 'string') return (content as AiResponseContent).text;
        if ('isScanCard' in content) { const sc = content as ScanCardContent; return `[User initiated a scan for ${sc.assetType}: ${sc.target}]`; }
        if ('isDownloadCard' in content) return `[System provided a download link for a report]`;
        if ('isSystemFileUpload' in content) { const fuc = content as FileUploadContent; return `[User uploaded file: ${fuc.fileName}]`; }
        if ('isSystemNotification' in content) return `[System notification: ${(content as SystemNotificationContent).text}]`;
        return "[Structured message content]";
    }
    return "[Unknown message content]";
};

// --- Optimized Message Rendering Components ---
const RenderTextContent = React.memo(({ text }: { text: string }) => <div className="text-sm leading-loose whitespace-pre-line message-content">{text}</div>);
const RenderAiResponse = React.memo(({ content }: { content: AiResponseContent }) => <div className="text-sm leading-loose whitespace-pre-line message-content">{content.text}</div>);
const RenderScanCardMessage = React.memo(({ content }: { content: ScanCardContent }) => (
    <div className="w-full flex justify-left py-2"><div className="w-full max-w-[400px]"><ChatScanCard {...content} /></div></div>
));
const RenderDownloadCardMessage = React.memo(({ content }: { content: DownloadCardContent }) => (
    <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800/30 text-green-600 dark:text-green-400"><FileDown className="h-6 w-6" /></div>
        <div className="flex-1"><h4 className="text-sm font-medium text-green-800 dark:text-green-300">{content.assetType || 'Scan'} Report Available</h4><p className="text-xs text-green-700 dark:text-green-400 mt-1">Your pentest is complete. Download the report.</p></div>
        <Button variant="outline" size="sm" className="bg-white dark:bg-green-800/30 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-800/50" onClick={() => window.open(content.reportUrl, '_blank')}><Download className="h-4 w-4 mr-2" /> Download</Button>
    </div>
));
const RenderFileUploadMessage = React.memo(({ content, findFileInfoByName }: { content: FileUploadContent; findFileInfoByName: (fileName: string) => FileInfoType | undefined; }) => {
    const fileInfo = findFileInfoByName(content.fileName);
    const downloadUrl = content.fileUrl || fileInfo?.url;
    return (
        <div className="flex items-center gap-3 p-3 bg-primary/5 dark:bg-primary/10 rounded-lg border border-primary/20 dark:border-violet-400">
            <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-md bg-primary/10 dark:bg-violet-500">
                {content.status === FileUploadStatus.UPLOADING ? <Loader2 className="h-5 w-5 text-primary dark:text-primary animate-spin" /> : content.status === FileUploadStatus.UPLOADED ? <CheckCircle2 className="h-5 w-5 text-primary dark:text-primary-foreground" /> : <FileText className="h-5 w-5 text-primary dark:text-primary" />}
            </div>
            <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-primary dark:text-primary-foreground truncate">{content.fileName}</p>
                <p className="text-xs text-muted-foreground dark:text-muted-foreground">{content.fileSize} • {content.status === FileUploadStatus.UPLOADING ? 'Uploading...' : content.status === FileUploadStatus.UPLOADED ? 'Upload complete' : content.status === FileUploadStatus.FAILED ? 'Upload failed' : 'Processing...'}</p>
            </div>
            {content.status === FileUploadStatus.UPLOADED && downloadUrl && (<Button variant="outline" size="sm" className="bg-white dark:bg-violet-700 border-primary/20 dark:border-violet-800 text-primary dark:text-gray-100 hover:bg-primary/5 dark:hover:bg-primary/30" onClick={() => window.open(downloadUrl, '_blank')}><Download className="h-4 w-4 mr-1" /> View</Button>)}
        </div>
    );
});
const RenderSystemNotificationMessage = React.memo(({ content }: { content: SystemNotificationContent }) => (
    <div className={cn("text-sm italic p-2 bg-muted/30 rounded-md", content.severity === SystemNotificationSeverity.ERROR ? "text-red-600 dark:text-red-400 border border-red-200 dark:border-red-700" : "text-muted-foreground")}>{content.text}</div>
));

const OptimizedChatMessageItem = React.memo(({ message, findFileInfoByName }: { message: MessageType; findFileInfoByName: (fileName: string) => FileInfoType | undefined; }) => {
    const { content, role } = message;
    const renderedContent = useMemo(() => {
        let parsedContent = content;
        if (typeof content === 'string') { try { const potentialObject = JSON.parse(content); if (typeof potentialObject === 'object' && potentialObject !== null) { parsedContent = potentialObject; } } catch (e) { return <RenderTextContent text={content} />; } }
        if (typeof parsedContent === 'string') { return <RenderTextContent text={parsedContent} />; }
        if (parsedContent && typeof parsedContent === 'object') {
            if ((parsedContent as FileUploadContent).isSystemFileUpload) return <RenderFileUploadMessage content={parsedContent as FileUploadContent} findFileInfoByName={findFileInfoByName} />;
            if ((parsedContent as ScanCardContent).isScanCard) return <RenderScanCardMessage content={parsedContent as ScanCardContent} />;
            if ((parsedContent as DownloadCardContent).isDownloadCard) return <RenderDownloadCardMessage content={parsedContent as DownloadCardContent} />;
            if ((parsedContent as SystemNotificationContent).isSystemNotification) return <RenderSystemNotificationMessage content={parsedContent as SystemNotificationContent} />;
            if ((parsedContent as AiResponseContent).isAiResponse || (parsedContent as AiResponseContent).text) return <RenderAiResponse content={parsedContent as AiResponseContent} />;
        }
        return <div className="text-sm leading-relaxed whitespace-pre-line message-content">[Unsupported content]</div>;
    }, [content, findFileInfoByName]);

    const formattedTime = useMemo(() => format(new Date(message.timestamp), "h:mm a"), [message.timestamp]);

    if (role === MessageRole.SYSTEM) {
        return (<div className="flex flex-col"><div data-message-id={message.id} className="mx-auto w-full max-w-3xl p-2">{renderedContent}<p className="text-xs text-muted-foreground mt-2 text-center">{formattedTime}</p></div></div>);
    }
    if (typeof content === 'object' && (content as ScanCardContent).isScanCard) {
        return (<div className="flex flex-col"><div data-message-id={message.id} className="mx-auto w-full max-w-4xl p-2">{renderedContent}<p className="text-xs text-muted-foreground mt-2 text-left">{formattedTime}</p></div></div>);
    }
    return (
        <div className="flex flex-col">
            <div data-message-id={message.id} className={cn("flex items-start gap-3 p-4", role === MessageRole.USER ? "chat-message-user ml-auto max-w-[80%]" : "chat-message-assistant mr-auto max-w-[80%]")}>
                {role === MessageRole.ASSISTANT && (<div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 dark:bg-[#843DF5]/30 text-primary dark:text-white"><Bot className="h-4 w-4" /></div>)}
                <div className="flex-1 space-y-2">{renderedContent}<p className="text-xs text-muted-foreground mt-2">{formattedTime}</p></div>
                {role === MessageRole.USER && (<div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-secondary dark:bg-[#111827]"><UserIcon className="h-4 w-4 dark:text-white" /></div>)}
            </div>
        </div>
    );
});
OptimizedChatMessageItem.displayName = 'OptimizedChatMessageItem';

// --- REFACTORED: Child Components ---

/**
 * Renders the header of the chat interface, including history, title, and new request button.
 */
const ChatHeader = React.memo(({ title, onToggleHistory, onNewConversation, isCreatingNewConversation }: {
    title: string;
    onToggleHistory: () => void;
    onNewConversation: () => void;
    isCreatingNewConversation: boolean;
}) => {
    const isMobile = useIsMobile();

    return (
        <div className="py-2 flex items-center justify-between backdrop-blur-md sticky top-0 z-10">
            <div className="flex items-center space-x-2">
                <Button variant="ghost" size="icon" onClick={onToggleHistory} className="m-2 dark:hover:bg-[#111827] text-primary bg-primary/10 p-4 dark:bg-[#111827]/90 rounded-lg shadow-md shadow-primary/20">
                    <Clock className="h-6 w-6 sm:h-10 sm:w-10" />
                </Button>
                <div className="relative overflow-hidden w-full max-w-[calc(100vw-200px)] sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl">
                    {isMobile ? (
                        <p className="text-xl font-semibold overflow-hidden truncate">{title}</p>
                    ) : (
                        <h2 className="text-lg font-semibold overflow-hidden truncate">{title}</h2>
                    )}
                </div>
            </div>
            <Button
                onClick={onNewConversation}
                size={isMobile ? "icon" : "lg"}
                className="h-12 px-6 rounded-full bg-gradient-to-r from-[#843DF5] to-[#9333EA] text-white hover:from-[#7C3AED] hover:to-[#8B5CF6] shadow-lg hover:shadow-xl shadow-violet-300/50 transition-all duration-300 font-semibold"
                disabled={isCreatingNewConversation}
            >
                {isCreatingNewConversation ? (
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                ) : (
                    <Plus className="h-5 w-5 mr-2" />
                )}
                {!isMobile && "New Request"}
            </Button>
        </div>
    );
});
ChatHeader.displayName = 'ChatHeader';

/**
 * Renders the chat input area, managing its own state for the text input
 * and handling user actions like sending messages, attaching files, and submitting scans.
 */
const ChatInputBar = React.memo(({ onSendMessage, onFileUpload, onSubmitScan, isSubmitting, isChatDisabled, showSubmitScanButton, activeConversation }: {
    onSendMessage: (content: string) => Promise<void>;
    onFileUpload: (file: File) => void;
    onSubmitScan: () => void;
    isSubmitting: boolean;
    isChatDisabled: boolean;
    showSubmitScanButton: boolean;
    activeConversation: ConversationType | null;
}) => {
    const [input, setInput] = useState("");
    const [debouncedInput, setDebouncedInput] = useState("");
    const inputRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isPending, startTransition] = useTransition();

    // Debounce input for performance
    useEffect(() => {
        const timer = setTimeout(() => {
            startTransition(() => setDebouncedInput(input));
        }, 150);
        return () => clearTimeout(timer);
    }, [input]);

    const handleSendMessageClick = async () => {
        if (!input.trim()) return;
        await onSendMessage(input.trim());
        setInput("");
        if (inputRef.current) inputRef.current.style.height = 'auto';
    };

    const handleSubmitScan = async () => {
        if (input.trim()) {
            await onSendMessage(input.trim());
            setInput("");
            if (inputRef.current) {
                inputRef.current.style.height = 'auto';
            }
        }
        onSubmitScan();
    };

    const handleFileSelect = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const target = event.target;
        if (!file) { if (target) target.value = ''; return; }
        if (file.size > MAX_FILE_SIZE) { toast.error(`File too large (max ${formatFileSize(MAX_FILE_SIZE)}).`); if (target) target.value = ''; return; }
        if (!validateFileExtension(file.name)) { toast.error("Invalid file type."); if (target) target.value = ''; return; }
        onFileUpload(file);
        if (target) target.value = '';
    };

    const placeholder = activeConversation && activeConversation.messages.length === 0 && activeConversation.title === TITLE_PENTEST_SCAN_REQUEST
        ? "Enter application type and target URL/location..."
        : "Type your message...";

    return (
        <div className="w-full mx-auto flex flex-row flex-wrap items-end gap-2 max-w-4xl">
            <div className="relative flex-auto">
                <Textarea
                    ref={inputRef}
                    placeholder={placeholder}
                    value={input}
                    onChange={(e) => {
                        const value = e.target.value;
                        setInput(value);
                        requestAnimationFrame(() => {
                            e.target.style.height = 'auto';
                            e.target.style.height = `${Math.min(e.target.scrollHeight, 120)}px`;
                        });
                    }}
                    onKeyDown={(e: KeyboardEvent<HTMLTextAreaElement>) => { if (e.key === "Enter" && !e.shiftKey) { e.preventDefault(); handleSendMessageClick(); } }}
                    className="w-full shadow-md focus:shadow-purple-200 dark:focus:shadow-primary/50 rounded-full pr-16 pl-14 min-h-[50px] max-h-[120px] py-3 overflow-hidden resize-none ring-2 ring-primary/30 hover:ring-primary/50 hover border-0"
                    rows={1}
                    disabled={isSubmitting || isChatDisabled}
                    autoFocus
                />
                <Button variant="ghost" size="icon" className="absolute left-2 top-1/2 h-9 w-9 rounded-full cursor-pointer -translate-y-1/2" onClick={() => fileInputRef.current?.click()} disabled={isSubmitting || isChatDisabled}>
                    <Paperclip className="h-5 w-5" />
                </Button>
                <input type="file" className="hidden" ref={fileInputRef} onChange={handleFileSelect} accept=".pdf,.doc,.docx,.txt,.json,.xml,.csv" disabled={isSubmitting || isChatDisabled} />
                <Button type="submit" size="icon" className="absolute right-2 top-1/2 h-9 w-9 rounded-full -translate-y-1/2" onClick={handleSendMessageClick} disabled={!input.trim() || isSubmitting || isChatDisabled}>
                    {isSubmitting ? <Loader2 className="h-5 w-5 animate-spin" /> : <Send className="h-5 w-5" />}
                </Button>
            </div>
            <div className={cn("transition-all duration-500 ease-in-out", showSubmitScanButton ? "w-full sm:w-auto opacity-100" : "w-0 max-w-0 opacity-0 pointer-events-none")}>
                <Button variant="default" size="lg" className="rounded-full bg-[#843DF5] hover:bg-[#843DF5]/90 text-white shadow-md shadow-primary/50 flex items-center justify-center space-x-1 h-[50px] px-4 py-2 w-full" disabled={isSubmitting || !showSubmitScanButton} onClick={handleSubmitScan}>
                    {isSubmitting ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : <ShieldAlert className="h-4 w-4 mr-1" />}
                    Submit Pentest Request
                </Button>
            </div>
        </div>
    );
});
ChatInputBar.displayName = 'ChatInputBar';


// --- Main Component ---
export function OptimizedChatInterface() {
    const router = useRouter();
    const { user, role, loading: authLoading } = useAuth();
    const { scans, fetchData } = useScans();
    const [isMounted, setIsMounted] = useState(false);
    const [isChatDisabled, setIsChatDisabled] = useState(false);
    const { conversations, activeConversationId, activeConversation, setActiveConversationId, createNewConversation, deleteConversation, addMessageToConversation, loading: conversationsLoading, updateConversationTitle, historyLoaded, loadConversationHistory } = useConversations();
    const { sendMessage, uploadFile } = useMessageQueue();

    const [deleteConfirmationId, setDeleteConfirmationId] = useState<string | null>(null);
    const [deletingConversations, setDeletingConversations] = useState<string[]>([]);
    const [isCreatingNewConversation, setIsCreatingNewConversation] = useState(false);
    const [historySidebarOpen, setHistorySidebarOpen] = useState(false);
    const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);

    const messages: MessageType[] = useMemo(() => (activeConversation?.messages as MessageType[] || []), [activeConversation]);
    const deferredMessages = useDeferredValue(messages);

    const { scanInfoProvided, allScanInfoProvided } = useScanInfoDetector(deferredMessages);
    const refreshScansForChat = useCallback(() => {
        if (fetchData) {
            const view = role === 'client' ? 'organization' : 'personal';
            fetchData(view, true);
        }
    }, [fetchData, role]);

    const { handleSubmitScanRequest, isSubmittingScan } = useScanSubmission({ user, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided, addMessageToConversation, fetchScans: refreshScansForChat, updateConversationTitle, isChatDisabled });

    const isSubmitButtonVisible = useMemo(() => (allScanInfoProvided || !!scanInfoProvided.appUrl) && !messages.some(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard), [allScanInfoProvided, scanInfoProvided.appUrl, messages]);

    useEffect(() => { setIsMounted(true); }, []);
    usePreloadRoutes();
    useEffect(() => { if (isMounted && !authLoading && !user) router.replace("/login"); }, [isMounted, user, authLoading, router]);



    useEffect(() => {
        const checkChatStatus = () => {
            if (!activeConversation || !scans || scans.length === 0) { setIsChatDisabled(false); return; }
            const scanCardMsg = messages.find(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard);
            if (scanCardMsg) {
                const scanId = (scanCardMsg.content as ScanCardContent).scanId;
                const scan = scans.find(s => s.id === scanId);
                setIsChatDisabled(scan?.status === "completed");
            } else { setIsChatDisabled(false); }
        };
        
        if (window.requestIdleCallback) {
            window.requestIdleCallback(checkChatStatus, { timeout: 500 });
        } else {
            checkChatStatus();
        }
    }, [activeConversation, messages, scans]);

    const findFileInfoByName = useCallback((fileName: string): FileInfoType | undefined => {
        return activeConversation?.fileInfo?.find(f => f.originalName === fileName || f.originalName === fileName.split('/').pop() || fileName === f.originalName.split('/').pop());
    }, [activeConversation]);

    const renderMessage = useCallback((message: MessageType, findFileInfoByName: (fileName: string) => FileInfoType | undefined) => (
        <OptimizedChatMessageItem message={message} findFileInfoByName={findFileInfoByName} />
    ), []);

    const handleSendMessage = useCallback(async (messageContent: string) => {
        if (!messageContent || isSubmittingMessage || isSubmittingScan || isChatDisabled) return;
        setIsSubmittingMessage(true);
        try { await sendMessage(messageContent); }
        catch (error) { console.error("Error sending message:", error); toast.error("Failed to send message."); }
        finally { setIsSubmittingMessage(false); }
    }, [isSubmittingMessage, isSubmittingScan, isChatDisabled, sendMessage]);

    const handleNewConversation = useCallback(async () => {
        if (isCreatingNewConversation) return;
        setIsCreatingNewConversation(true);
        setHistorySidebarOpen(false);
        try { await createNewConversation(); }
        catch (error) { console.error("Error creating conversation:", error); toast.error("Failed to create new conversation"); }
        finally { setIsCreatingNewConversation(false); }
    }, [isCreatingNewConversation, createNewConversation]);

    const handleFileUpload = useCallback(async (file: File) => {
        if (isSubmittingMessage || isSubmittingScan || isChatDisabled || !user || !activeConversationId) {
            if (!user) toast.error("Authentication error.");
            if (!activeConversationId) toast.error("Select or start a conversation first.");
            return;
        }
        setIsSubmittingMessage(true);
        try { await uploadFile(file); }
        catch (error) { console.error("Error uploading file:", error); }
        finally { setIsSubmittingMessage(false); }
    }, [isSubmittingMessage, isSubmittingScan, isChatDisabled, user, activeConversationId, uploadFile]);

    if (!isMounted) return <div className="flex h-screen items-center justify-center"><Loader2 className="h-10 w-10 animate-spin text-primary" /></div>;
    if (authLoading) return <div className="flex h-[calc(100vh-4rem)] items-center justify-center"><Loader2 className="h-8 w-8 animate-spin text-primary" /><p className="ml-2 text-muted-foreground">Loading authentication...</p></div>;
    if (!user) return null;
    if (role === "admin") { router.replace("/dashboard/admin"); return null; }

    return (
        <div className="flex h-[calc(100vh-4rem)] flex-col w-full relative growthguard-gradient-bg dark:bg-[#111827]">
            <OptimizedConversationHistory
                conversations={conversations}
                activeConversationId={activeConversationId}
                isOpen={historySidebarOpen}
                loading={conversationsLoading}
                historyLoaded={historyLoaded}
                onClose={() => setHistorySidebarOpen(false)}
                onSelectConversation={setActiveConversationId}
                onDeleteConversation={(id) => setDeleteConfirmationId(prev => prev === id ? null : id)}
                onConfirmDelete={async (id) => {
                    if (deletingConversations.includes(id)) return;
                    setDeletingConversations(prev => [...prev, id]);
                    setDeleteConfirmationId(null);
                    try {
                        const success = await deleteConversation(id);
                        if (!success) { toast.error("Failed to delete conversation"); setDeletingConversations(prev => prev.filter(convId => convId !== id)); }
                    } catch (error) {
                        console.error("Error deleting conversation:", error);
                        toast.error("Failed to delete conversation");
                        setDeletingConversations(prev => prev.filter(convId => convId !== id));
                    }
                }}
                onCancelDelete={() => setDeleteConfirmationId(null)}
                deleteConfirmationId={deleteConfirmationId}
                deletingConversations={deletingConversations}
                onLoadHistory={loadConversationHistory}
            />

            <div className="px-6 py-6 2xl:px-0 w-full 2xl:max-w-7xl mx-auto flex flex-col flex-1 min-h-0 dark:bg-[#111827]">
                <ChatHeader
                    title={activeConversation?.title || TITLE_PENTEST_SCAN_REQUEST}
                    onToggleHistory={() => setHistorySidebarOpen(!historySidebarOpen)}
                    onNewConversation={handleNewConversation}
                    isCreatingNewConversation={isCreatingNewConversation}
                />

                <OptimizedMessageList
                    messages={deferredMessages}
                    findFileInfoByName={findFileInfoByName}
                    renderMessage={renderMessage}
                />

                {authLoading ? (
                    <div className="p-4 dark:bg-[#111827]/90 backdrop-blur-md border-border/50 flex items-center justify-center h-[100px]">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <p className="ml-2 text-muted-foreground">Getting Started.</p>
                    </div>
                ) : (
                    <ChatInputBar
                        onSendMessage={handleSendMessage}
                        onFileUpload={handleFileUpload}
                        onSubmitScan={handleSubmitScanRequest}
                        isSubmitting={isSubmittingMessage || isSubmittingScan}
                        isChatDisabled={isChatDisabled}
                        showSubmitScanButton={isSubmitButtonVisible}
                        activeConversation={activeConversation}
                    />
                )}
            </div>

            {historySidebarOpen && (
                <div className="fixed inset-0 bg-white/60 dark:bg-[#111827]/60 backdrop-blur-sm z-20" onClick={() => setHistorySidebarOpen(false)} />
            )}
        </div>
    );
}
