"use client";

import React from "react";
import { Input } from "./ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Search } from "lucide-react";

interface ConversationFilterToolbarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  scanFilter: "all" | "with-scan" | "without-scan";
  onScanFilterChange: (filter: "all" | "with-scan" | "without-scan") => void;
}

export const ConversationFilterToolbar: React.FC<ConversationFilterToolbarProps> = ({
  searchQuery,
  onSearchChange,
  scanFilter,
  onScanFilterChange,
}) => {
  return (
    <div className="max-w-5xl p-4 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 mb-6">
      <div className="flex flex-wrap items-center gap-3">
        <div className="relative flex-1 min-w-[300px]">
          <Search className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground transition-colors duration-300" />
          <Input
            placeholder="Search conversations by title, user, or message..."
            className="pl-11 pr-10 h-10 bg-background/50 border-border/50 rounded-lg text-sm placeholder:text-muted-foreground/70 focus:border-violet/50 focus:ring-violet/20 transition-all duration-300 hover:border-border"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        <div className="h-6 w-px bg-border/50" />
        <div className="flex items-center gap-2">
          <Select value={scanFilter} onValueChange={onScanFilterChange}>
            <SelectTrigger className="w-48 h-10 bg-background/50 border-border/50 hover:border-violet/30 focus:border-violet/50 transition-colors">
              <SelectValue placeholder="Filter by scan..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Conversations</SelectItem>
              <SelectItem value="with-scan">With Scan</SelectItem>
              <SelectItem value="without-scan">Without Scan</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};