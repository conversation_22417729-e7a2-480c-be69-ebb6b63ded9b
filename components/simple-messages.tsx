"use client"

import React, { useState, useEffect, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import {
  MessagesSquare,
  RefreshCw,
  Loader2,
  Send,
  Search,
  Filter as FilterIcon, // Renamed to avoid conflict
  X,
  Calendar as CalendarIcon, // Renamed to avoid conflict
  CheckCircle,
  Circle,
  User,
  MessageCircle,
  CornerUpRight,
  Trash2,
  ChevronsUpDown,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar" // REDESIGN: Added Avatar for a richer look
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useAuth } from "@/hooks/useAuth"
import { useTeamMessages, TeamMessage } from "@/context/TeamMessagesContext"
import { toast } from "sonner"

// Helper to get initials for AvatarFallback
const getInitials = (email: string = "") => {
  const parts = email.split("@")[0].split(/[._-]/)
  if (parts.length > 1) {
    return (parts[0][0] + parts[1][0]).toUpperCase()
  }
  return email.substring(0, 2).toUpperCase()
}

export const SimpleMessages = () => {
  const router = useRouter()
  const { user, role, loading: authLoading } = useAuth()
  const {
    messages,
    unreadCount,
    loading,
    fetchMessages,
    markAsRead,
    respondToMessage,
    deleteMessage,
    lastUpdated,
    isConnected,
  } = useTeamMessages()

  const [isMounted, setIsMounted] = useState(false)
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)
  const [responseText, setResponseText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteConfirmationId, setDeleteConfirmationId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null) // For scrolling in detail view
  const chatContainerRef = useRef<HTMLDivElement>(null); // REDESIGN: For scrolling chat messages

  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "read" | "unread">("all")
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined)
  // REDESIGN: No need for showFilters state, Popover handles its own open/close

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (isMounted && !authLoading && role !== "admin") {
      router.replace("/")
    }
  }, [isMounted, authLoading, role, router])

  // REDESIGN: Scroll to bottom of chat messages when a new message is selected or response is added
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [selectedMessageId, messages]); // Re-scroll if messages array changes (e.g., after a response)


  const selectedMessage = useMemo(
    () => (selectedMessageId ? messages.find((msg) => msg.id === selectedMessageId) : null),
    [messages, selectedMessageId]
  )

  const filteredMessages = useMemo(() => {
    return messages.filter((message) => {
      const matchesSearch = searchQuery
        ? message.userEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
          message.message.toLowerCase().includes(searchQuery.toLowerCase())
        : true
      const matchesStatus = statusFilter === "all" ? true : message.status === statusFilter
      const matchesDate = dateFilter
        ? new Date(message.timestamp).toDateString() === dateFilter.toDateString()
        : true
      return matchesSearch && matchesStatus && matchesDate
    })
  }, [messages, searchQuery, statusFilter, dateFilter])

  const clearFilters = () => {
    setSearchQuery("")
    setStatusFilter("all")
    setDateFilter(undefined)
    // Popover will close on its own if open, or we can manage its open state if needed
  }

  const handleSelectMessage = (message: TeamMessage) => {
    setSelectedMessageId(message.id);
    setResponseText(""); // Clear previous response text
    if (message.status === "unread") {
      markAsRead(message.id);
    }
  }

  const handleSendResponse = async () => {
    if (!responseText.trim() || isSubmitting || !selectedMessageId) return;

    setIsSubmitting(true);
    try {
      await respondToMessage(selectedMessageId, responseText);
      setResponseText("");
      toast.success("Response sent successfully");
      // The useEffect for chatContainerRef.current.scrollTop will handle scrolling
    } catch (error) {
      console.error("Error sending response:", error);
      toast.error("Failed to send response");
    } finally {
      setIsSubmitting(false);
    }
  };


  const handleDeleteMessage = async () => {
    if (!deleteConfirmationId || isDeleting) return;

    setIsDeleting(true);
    try {
      await deleteMessage(deleteConfirmationId);
      toast.success("Message deleted successfully");
      setSelectedMessageId(null);
      setDeleteConfirmationId(null);
    } catch (error) {
      console.error("Error deleting message:", error);
      toast.error("Failed to delete message");
    } finally {
      setIsDeleting(false);
    }
  };


  if (!isMounted) return null
  if (authLoading) return <div className="flex items-center justify-center h-[calc(100vh-4rem)]">Loading Authentication...</div>
  if (!user || role !== "admin") return null

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)] bg-background dark:bg-[#0f172a]"> {/* REDESIGN: Consistent background */}
      {/* Top Header */}
      <div className="flex items-center justify-between border-b px-6 py-3 dark:border-slate-700">
        <div className="flex flex-col">
          <div className="flex items-center space-x-3">
            <MessageCircle className="h-6 w-6 text-primary" />
            <h2 className="text-xl font-semibold">Team Messages</h2> {/* REDESIGN: Slightly smaller title */}
            {unreadCount > 0 && (
              <Badge variant="default" className="text-xs">
                {unreadCount} new
              </Badge>
            )}
          </div>
          {lastUpdated && (
            <p className="text-xs text-muted-foreground mt-1">
              Last updated: {format(lastUpdated, "h:mm:ss a")}
              {loading && !isConnected && " • Connecting..."}
              {loading && isConnected && " • Updating..."}
              {!loading && !isConnected && " • Disconnected"}
            </p>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            fetchMessages()
            toast.info("Refreshing messages...")
          }}
          disabled={loading}
          className="dark:hover:bg-slate-700/50 dark:border-slate-600"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar: Messages List */}
        {/* REDESIGN: Adjusted width, e.g., w-[360px] or w-2/5 */}
        <div className="w-[360px] border-r flex flex-col bg-slate-50 dark:bg-slate-800/30 dark:border-slate-700">
          {/* REDESIGN: Filter controls moved to Popover */}
          <div className="p-4 border-b space-y-3 dark:border-slate-700">
            <div className="flex items-center space-x-2">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by email or message..."
                  className="pl-9 pr-4 h-10 dark:bg-slate-700 dark:border-slate-600"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="icon" className="h-10 w-10 dark:hover:bg-slate-700/50 dark:border-slate-600">
                    <FilterIcon className="h-4 w-4" />
                    <span className="sr-only">Filters</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-72 p-4 space-y-4 dark:bg-slate-800 dark:border-slate-700">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Status</label>
                    <Select
                      value={statusFilter}
                      onValueChange={(value) => setStatusFilter(value as "all" | "read" | "unread")}
                    >
                      <SelectTrigger className="w-full dark:bg-slate-700 dark:border-slate-600">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent className="dark:bg-slate-800 dark:border-slate-700">
                        <SelectItem value="all">All Messages</SelectItem>
                        <SelectItem value="read">Read</SelectItem>
                        <SelectItem value="unread">Unread</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-1 block">Date</label>
                    <Popover> {/* Nested Popover for Calendar */}
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal dark:bg-slate-700 dark:border-slate-600 dark:hover:bg-slate-700/80"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateFilter ? format(dateFilter, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={dateFilter}
                          onSelect={setDateFilter}
                          className="dark:bg-slate-800"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  {(statusFilter !== "all" || dateFilter || searchQuery) && (
                     <Button variant="ghost" size="sm" onClick={clearFilters} className="w-full text-destructive hover:text-destructive dark:hover:bg-destructive/10">
                        <Trash2 className="h-4 w-4 mr-2" /> Clear All Filters
                    </Button>
                  )}
                </PopoverContent>
              </Popover>
            </div>

            {/* REDESIGN: Active filter badges */}
            {(statusFilter !== "all" || dateFilter) && (
              <div className="flex flex-wrap gap-1.5 pt-1">
                {statusFilter !== "all" && (
                  <Badge variant="secondary" className="text-xs dark:bg-slate-700 dark:text-slate-300">
                    Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                    <X className="h-3 w-3 ml-1.5 cursor-pointer" onClick={() => setStatusFilter("all")} />
                  </Badge>
                )}
                {dateFilter && (
                  <Badge variant="secondary" className="text-xs dark:bg-slate-700 dark:text-slate-300">
                    Date: {format(dateFilter, "MMM d")}
                    <X className="h-3 w-3 ml-1.5 cursor-pointer" onClick={() => setDateFilter(undefined)} />
                  </Badge>
                )}
              </div>
            )}
          </div>

          <div className="flex-1 overflow-y-auto p-2 space-y-1.5">
            {loading && messages.length === 0 ? (
              <div className="flex justify-center items-center py-10">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            ) : filteredMessages.length === 0 ? (
              <div className="text-center py-10 px-4 text-sm text-muted-foreground">
                {messages.length === 0 ? "No messages yet. New messages from users will appear here." : "No messages match your filters."}
              </div>
            ) : (
              filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "p-3 rounded-md cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors border border-transparent group relative",
                    selectedMessageId === message.id ? "bg-slate-200 dark:bg-slate-700" : "",
                    message.status === "unread" && selectedMessageId !== message.id ? "font-semibold dark:text-slate-100" : "dark:text-slate-300",
                  )}
                  onClick={() => handleSelectMessage(message)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 overflow-hidden">
                      <Avatar className="h-8 w-8 text-xs">
                        <AvatarFallback className="bg-primary/20 text-primary dark:bg-primary/30">
                          {getInitials(message.userEmail)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="truncate text-sm">{message.userEmail}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {message.status === "unread" && (
                        <span className="h-2.5 w-2.5 rounded-full bg-primary shrink-0 mr-1"></span>
                      )}
                      {deleteConfirmationId === message.id ? (
                        <div className="absolute right-1 top-1 flex space-x-1 bg-background/80 backdrop-blur-sm rounded-full p-0.5 shadow-sm border z-10">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 text-green-500 rounded-full hover:bg-green-500/10 cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteMessage();
                            }}
                            disabled={isDeleting}
                          >
                            {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
                            <span className="sr-only">Confirm delete</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 text-destructive rounded-full hover:bg-destructive/10 cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteConfirmationId(null);
                            }}
                            disabled={isDeleting}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Cancel delete</span>
                          </Button>
                        </div>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute right-1 top-1 h-7 w-7 p-0 opacity-0 group-hover:opacity-100 rounded-full hover:text-destructive transition-opacity hover:bg-destructive/10 cursor-pointer z-10"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteConfirmationId(message.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete message</span>
                        </Button>
                      )}
                    </div>
                  </div>
                  <p className={cn(
                    "mt-1.5 text-xs text-muted-foreground line-clamp-2",
                     message.status === "unread" && selectedMessageId !== message.id ? "dark:text-slate-400" : "dark:text-slate-500"
                    )}>
                    {/* Show the latest message - either a related message, the response, or the original message */}
                    {(() => {
                      // Find related messages for this message (replies)
                      const relatedMsgs = messages.filter(msg => msg.isReplyTo === message.id);
                      // Sort by timestamp, newest first
                      const sortedRelated = relatedMsgs.sort((a, b) =>
                        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
                      );

                      // If there's a related message, show that
                      if (sortedRelated.length > 0) {
                        return sortedRelated[0].message;
                      }
                      // Otherwise, show the response if it exists
                      else if (message.response) {
                        return message.response;
                      }
                      // Finally, fall back to the original message
                      else {
                        return message.message;
                      }
                    })()}
                  </p>
                  <div className="mt-1.5 flex items-center justify-between">
                    <p className="text-xs text-muted-foreground dark:text-slate-500">
                      {/* Show the timestamp of the most recent interaction */}
                      {(() => {
                        // Find related messages for this message
                        const relatedMsgs = messages.filter(msg => msg.isReplyTo === message.id);
                        // Sort by timestamp, newest first
                        const sortedRelated = relatedMsgs.sort((a, b) =>
                          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
                        );

                        // If there's a related message, use its timestamp
                        if (sortedRelated.length > 0) {
                          return format(new Date(sortedRelated[0].timestamp), "MMM d, p");
                        }
                        // Otherwise, use the response timestamp if it exists
                        else if (message.responseTimestamp) {
                          return format(new Date(message.responseTimestamp), "MMM d, p");
                        }
                        // Finally, fall back to the original message timestamp
                        else {
                          return format(new Date(message.timestamp), "MMM d, p");
                        }
                      })()}
                    </p>
                    {message.response && (
                      <Badge variant="outline" className="text-xs px-1.5 py-0.5 dark:border-slate-600 dark:text-slate-400">Replied</Badge>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Main Content: Message Detail & Reply */}
        <div className="flex-1 flex flex-col bg-white dark:bg-[#111827]"> {/* REDESIGN: Main content area */}
          {selectedMessage ? (
            <>
              {/* REDESIGN: Header for selected message */}
              <div className="p-4 border-b flex items-center justify-between dark:border-slate-700">
                <div>
                  <h3 className="font-semibold text-sm">{selectedMessage.userEmail}</h3>
                  <p className="text-xs text-muted-foreground">
                    Conversation started: {format(new Date(selectedMessage.timestamp), "MMM d, yyyy 'at' p")}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  {/* Status badge removed as requested */}
                </div>
              </div>

              {/* REDESIGN: Chat area */}
              <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* User's Message */}
                <div className="flex items-start gap-3">
                  <Avatar className="h-9 w-9">
                    <AvatarFallback className="bg-muted text-muted-foreground dark:bg-slate-700 dark:text-slate-300">
                      {getInitials(selectedMessage.userEmail)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="max-w-[75%]">
                    <div className="bg-muted p-3 rounded-lg rounded-tl-none dark:bg-slate-700">
                      <p className="text-sm whitespace-pre-wrap">{selectedMessage.message}</p>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {format(new Date(selectedMessage.timestamp), "MMM d, p")}
                    </p>
                  </div>
                </div>

                {/* Admin's Response */}
                {selectedMessage.response && (
                  <div className="flex items-start gap-3 justify-end">
                     <div className="max-w-[75%] text-right"> {/* REDESIGN: Ensure text aligns right within this div */}
                        <div className="bg-primary text-primary-foreground p-3 rounded-lg rounded-tr-none text-left"> {/* REDESIGN: Text left inside bubble */}
                          <p className="text-sm whitespace-pre-wrap">{selectedMessage.response}</p>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {selectedMessage.responseTimestamp
                            ? format(new Date(selectedMessage.responseTimestamp), "MMM d, p")
                            : "Just now"}
                        </p>
                      </div>
                    <Avatar className="h-9 w-9">
                      <AvatarFallback className="bg-foreground text-background dark:bg-slate-300 dark:text-slate-800">
                        {user?.email ? getInitials(user.email) : "AD"}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                )}
                <div ref={messagesEndRef} /> {/* For scrolling to bottom of input after send */}
              </div>

              {/* REDESIGN: Reply input area fixed at bottom */}
              <div className="border-t p-4 bg-slate-50 dark:bg-slate-800/50 dark:border-slate-700">
                <div className="relative">
                  <Textarea
                    placeholder="Type your response..."
                    className="w-full min-h-[80px] pr-28 resize-none dark:bg-slate-700 dark:border-slate-600 dark:text-slate-100"
                    value={responseText}
                    onChange={(e) => setResponseText(e.target.value)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendResponse();
                        }
                    }}
                    disabled={isSubmitting}
                  />
                  <Button
                    onClick={handleSendResponse}
                    disabled={!responseText.trim() || isSubmitting}
                    className="absolute right-3 bottom-3 px-3 py-1.5 text-sm dark:bg-primary dark:hover:bg-primary/90"
                    size="sm"
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                    <span className="ml-2 hidden sm:inline">
                        {selectedMessage.response ? "Send Update" : "Send"}
                    </span>
                  </Button>
                </div>
                 <p className="text-xs text-muted-foreground mt-2">
                    {selectedMessage.response ? "" : ""}
                  </p>
              </div>
            </>
          ) : (
            // Placeholder when no message is selected
            <div className="flex flex-col items-center justify-center h-full p-6 text-center">
              <div className="bg-muted/30 p-10 rounded-lg border border-dashed dark:border-slate-700/50 flex flex-col items-center max-w-md">
                <MessageCircle className="h-16 w-16 text-muted-foreground/50 mb-6" />
                <h3 className="text-xl font-medium">Select a message</h3>
                <p className="text-muted-foreground mt-2">
                  {loading && messages.length === 0
                    ? "Loading messages..."
                    : messages.length === 0
                      ? "No messages found. When users send messages, they will appear here."
                      : filteredMessages.length === 0
                        ? "No messages match your current filters. Try adjusting or clearing your filters."
                        : `Select a message from the list on the left to view details and respond.`
                  }
                </p>
                {filteredMessages.length === 0 && messages.length > 0 && (
                  <Button
                    variant="outline"
                    className="mt-6 dark:hover:bg-slate-700/50 dark:border-slate-600"
                    onClick={clearFilters}
                  >
                    <Trash2 className="h-4 w-4 mr-2"/> Clear All Filters
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}