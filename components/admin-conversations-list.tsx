"use client";

import React, { useState, useEffect, use<PERSON>emo, useC<PERSON>back } from "react";
import { useAuth } from "@/hooks/useAuth";
import { ConversationType, MessageType, TextContent, FileUploadContent } from "@/lib/models/conversation";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { format } from "date-fns";
// Added ArrowLeft for the mobile back button
import { Loader2, ChevronDown, ChevronUp, MessageSquare, User, Calendar, Paperclip, Download, CheckCircle2, FileText, Shield, Search, X, ArrowLeft, PanelLeft } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScanCard } from "@/components/scan-card";
import { useScans } from "@/context/ScanContext";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const getInitials = (name: string = "") => {
  const parts = name.split(" ");
  if (parts.length > 1) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

const FileUploadCard: React.FC<{ content: FileUploadContent }> = ({ content }) => {
  return (
    <div className="flex items-center gap-3 p-3 bg-slate-100 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
      <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-md bg-slate-200 dark:bg-slate-700">
        {content.status === 'uploading' ?
          <Loader2 className="h-5 w-5 text-slate-600 dark:text-slate-400 animate-spin" /> :
          content.status === 'uploaded' ?
          <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" /> :
          <FileText className="h-5 w-5 text-slate-600 dark:text-slate-400" />
        }
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-slate-800 dark:text-slate-300 truncate">{content.fileName}</p>
        <p className="text-xs text-slate-600 dark:text-slate-400">
          {content.fileSize} • {content.status === 'uploading' ? 'Uploading...' :
           content.status === 'uploaded' ? 'Upload complete' :
           content.status === 'failed' ? 'Upload failed' : 'Processing...'}
        </p>
      </div>
      {content.status === 'uploaded' && content.fileUrl && (
        <Button
          variant="outline"
          size="sm"
          className="bg-white dark:bg-slate-700 border-slate-200 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-600"
          onClick={() => window.open(content.fileUrl, '_blank')}
        >
          <Download className="h-4 w-4 mr-1" /> View
        </Button>
      )}
    </div>
  )
}

const ConversationMessage: React.FC<{ message: MessageType, user: any }> = ({ message, user }) => {
  const isUser = message.role === 'user';

  const renderContent = () => {
    let content = message.content;

    if (typeof content === 'string') {
      try {
        const parsed = JSON.parse(content);
        if (typeof parsed === 'object' && parsed !== null) {
          content = parsed;
        }
      } catch (e) {
        // Not JSON, treat as plain text
      }
    }

    if (typeof content === 'object' && content !== null) {
      if ('isSystemFileUpload' in content && (content as any).isSystemFileUpload) {
        return <FileUploadCard content={content as FileUploadContent} />;
      }
      if ('text' in content && typeof (content as any).text === 'string') {
        return <p className="text-sm whitespace-pre-wrap">{(content as TextContent).text.replace(/\\n/g, '\n')}</p>;
      }
      if ('isScanCard' in content && (content as any).isScanCard) {
        return null;
      }
    }
    
    if (typeof content === 'string') {
        return <p className="text-sm whitespace-pre-wrap">{content.replace(/\\n/g, '\n')}</p>;
    }

    return <p className="text-sm whitespace-pre-wrap">{JSON.stringify(content)}</p>;
  }

  return (
    <div className={cn("flex items-start gap-3", isUser ? "justify-end" : "")}>
      {!isUser && (
        <Avatar className="h-9 w-9">
          <AvatarFallback className="bg-foreground text-background dark:bg-slate-300 dark:text-slate-800">
            DS
          </AvatarFallback>
        </Avatar>
      )}
      <div className={cn("max-w-[75%]", isUser ? "text-right" : "text-left")}>
        <div className={cn(
          "p-3 rounded-lg text-left",
          isUser ? "bg-primary text-primary-foreground rounded-tr-none dark:bg-[#843DF5] bg-primary" : "bg-muted rounded-tl-none dark:bg-slate-700"
        )}>
          {renderContent()}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {format(new Date(message.timestamp), "MMM d, p")}
        </p>
      </div>
      {isUser && (
        <Avatar className="h-9 w-9">
          <AvatarFallback className="bg-muted text-muted-foreground dark:bg-slate-600 dark:text-slate-300">
            {getInitials(user.displayName || user.email)}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};

const ConversationCard: React.FC<{ conversation: any, scans: any[], isSelected: boolean, onSelect: () => void }> = ({ conversation, scans, isSelected, onSelect }) => {
  return (
    <div 
      className={cn(
        "p-2 rounded-lg cursor-pointer transition-all duration-200 border text-left",
        isSelected ? "bg-primary/10 border-primary/20" : "bg-card hover:bg-muted/50 border-border/50"
      )}
      onClick={onSelect}
    >
      <div className="space-y-1.5">
        <div className="flex items-center gap-1.5">
          <Badge variant="outline" className="text-xs px-1.5 py-0.5">
            <MessageSquare className="h-2.5 w-2.5 mr-1" />
            Chat
          </Badge>
          {conversation.associatedScanIds?.length > 0 && (
            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
              <Shield className="h-2.5 w-2.5 mr-1" />
              {conversation.associatedScanIds.length}
            </Badge>
          )}
        </div>
        <h4 className="text-xs font-medium leading-tight line-clamp-2">
          {conversation.title}
        </h4>
        <div className="space-y-0.5 text-xs text-muted-foreground">
          <div className="flex items-center gap-1 truncate">
            <User className="h-2.5 w-2.5 flex-shrink-0" />
            <span className="truncate">{conversation.user.displayName}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-2.5 w-2.5 flex-shrink-0" />
            <span>{format(new Date(conversation.updatedAt), "MMM d")}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export const AdminConversationsList = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [scanFilter, setScanFilter] = useState<"all" | "with-scan" | "without-scan">("all");
  const { scans, loading: scansLoading } = useScans();
  const [expandedOrganizations, setExpandedOrganizations] = useState<Record<string, boolean>>({});
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    const fetchConversations = async () => {
      if (!user) return;
      setLoading(true);
      try {
        const idToken = await user.getIdToken();
        const response = await fetch("/api/admin/conversations", {
          headers: { Authorization: `Bearer ${idToken}` },
        });
        if (!response.ok) {
          throw new Error("Failed to fetch conversations");
        }
        const data = await response.json();
        setConversations(data.conversations);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, [user]);

  const filteredConversations = useMemo(() => {
    let filtered = conversations;

    if (scanFilter !== "all") {
      filtered = Object.entries(conversations).reduce((acc, [org, convos]) => {
        const filteredConvos = convos.filter(conversation => {
          const hasScan = conversation.associatedScanIds && conversation.associatedScanIds.length > 0;
          if (scanFilter === "with-scan") return hasScan;
          if (scanFilter === "without-scan") return !hasScan;
          return true;
        });
        if (filteredConvos.length > 0) {
          acc[org] = filteredConvos;
        }
        return acc;
      }, {} as Record<string, any[]>);
    }

    if (searchTerm === "") return filtered;

    return Object.entries(filtered).reduce((acc, [org, convos]) => {
      const filteredConvos = convos.filter(conversation =>
        conversation.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conversation.user?.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conversation.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conversation.messages.some((message: MessageType) => {
          if (typeof message.content === 'string') {
            return message.content.toLowerCase().includes(searchTerm.toLowerCase());
          }
          if (typeof message.content === 'object' && message.content && 'text' in message.content) {
              const textContent = (message.content as any).text;
              if (typeof textContent === 'string') {
                  return textContent.toLowerCase().includes(searchTerm.toLowerCase());
              }
          }
          return false;
        })
      );
      if (filteredConvos.length > 0) {
        acc[org] = filteredConvos;
      }
      return acc;
    }, {} as Record<string, any[]>);
  }, [conversations, searchTerm, scanFilter]);

  const { totalConversations, withScanCount, withoutScanCount } = useMemo(() => {
    const allConvos = Object.values(conversations).flat();
    const withScan = allConvos.filter(conversation => 
      conversation.associatedScanIds && conversation.associatedScanIds.length > 0
    ).length;
    const withoutScan = allConvos.filter(conversation => 
      !conversation.associatedScanIds || conversation.associatedScanIds.length === 0
    ).length;
    
    return {
      totalConversations: allConvos.length,
      withScanCount: withScan,
      withoutScanCount: withoutScan
    };
  }, [conversations]);

  const toggleOrganizationExpansion = useCallback((organizationName: string) => {
    setExpandedOrganizations(prev => ({
      ...prev,
      [organizationName]: !prev[organizationName]
    }));
  }, []);

  const isOrganizationExpanded = useCallback((organizationName: string) => {
    return !!expandedOrganizations[organizationName];
  }, [expandedOrganizations]);

  const selectedConversation = useMemo(() => {
    if (!selectedConversationId) return null;
    const allConversations = Object.values(filteredConversations).flat();
    return allConversations.find(conv => conv.id === selectedConversationId) || null;
  }, [selectedConversationId, filteredConversations]);

  return (
    <div className="h-[calc(100vh-4rem)] max-w-full overflow-hidden bg-background dark:bg-[#111827]">
      <div className="flex flex-col h-full">
        {/* Top Header */}
        <div className="flex items-center border-b px-4 py-3 dark:border-slate-700 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="icon" className="hidden md:inline-flex" onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}>
              <PanelLeft className="h-5 w-5" />
            </Button>
            <div className={cn("flex items-center space-x-3", isSidebarCollapsed ? "hidden" : "")}>
              <MessageSquare className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Conversations</h2>
            </div>
          </div>
          <div className="hidden md:block ml-16 mt-1">
            <Tabs defaultValue="all" onValueChange={setScanFilter as any}>
              <div className="flex items-center gap-3">
                <TabsList className="h-9 p-1 bg-muted/50 rounded-lg">
                  <TabsTrigger value="all" className="px-2 py-1 text-xs">
                    All <Badge variant="outline" className="ml-1 text-xs">{totalConversations}</Badge>
                  </TabsTrigger>
                  <TabsTrigger value="with-scan" className="px-2 py-1 text-xs">
                    <Shield className="h-3 w-3 mr-1" />With Scan <Badge variant="outline" className="ml-1 text-xs">{withScanCount}</Badge>
                  </TabsTrigger>
                  <TabsTrigger value="without-scan" className="px-2 py-1 text-xs">
                    <Shield className="h-3 w-3 mr-1" /> No Scan <Badge variant="outline" className="ml-1 text-xs">{withoutScanCount}</Badge>
                  </TabsTrigger>
                </TabsList>
                <div className="relative w-full sm:w-auto sm:flex-1 sm:max-w-xs">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search..." className="h-9 pl-9 pr-9 text-sm" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                  {searchTerm && <Button variant="ghost" size="icon" className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6" onClick={() => setSearchTerm("")}><X className="h-3 w-3" /></Button>}
                </div>
              </div>
            </Tabs>
          </div>
        </div>

        {/* Filters */}
        <div className="border-b px-4 py-3 dark:border-slate-700 flex-shrink-0 md:hidden">
          <Tabs defaultValue="all" onValueChange={setScanFilter as any}>
            {/* RESPONSIVE CHANGE: This flex container now stacks on small screens and becomes a row on larger screens. */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
              <TabsList className="h-9 p-1 bg-muted/50 rounded-lg">
                <TabsTrigger value="all" className="px-2 py-1 text-xs">
                  All <Badge variant="outline" className="ml-1 text-xs">{totalConversations}</Badge>
                </TabsTrigger>
                <TabsTrigger value="with-scan" className="px-2 py-1 text-xs">
                  <Shield className="h-3 w-3 mr-1" /> Scan <Badge variant="outline" className="ml-1 text-xs">{withScanCount}</Badge>
                </TabsTrigger>
                <TabsTrigger value="without-scan" className="px-2 py-1 text-xs">
                  <Shield className="h-3 w-3 mr-1" /> No Scan <Badge variant="outline" className="ml-1 text-xs">{withoutScanCount}</Badge>
                </TabsTrigger>
              </TabsList>
              <div className="relative w-full sm:w-auto sm:flex-1 sm:max-w-xs">
                <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search..." className="h-9 pl-9 pr-9 text-sm" value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                {searchTerm && <Button variant="ghost" size="icon" className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6" onClick={() => setSearchTerm("")}><X className="h-3 w-3" /></Button>}
              </div>
            </div>
          </Tabs>
        </div>

        <div className="flex flex-1 min-h-0">
          {/* Sidebar */}
          {/* RESPONSIVE CHANGE: The sidebar is now conditionally rendered.
              - On small screens, it's hidden if a conversation is selected.
              - On medium screens (md) and up, it's always visible. */}
          <div className={cn(
            "flex-col bg-gray-50 dark:bg-slate-800/30 dark:border-slate-700 transition-all duration-300",
            "md:flex md:flex-shrink-0 md:border-r",
            isSidebarCollapsed ? "md:w-20" : "md:w-80",
            selectedConversationId ? "hidden md:flex" : "w-full flex"
          )}>
            <div className={cn("flex-1 overflow-y-auto p-3", isSidebarCollapsed ? "hidden" : "")}>
              {loading || scansLoading ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : Object.keys(filteredConversations).length === 0 ? (
                <div className="text-center py-10 px-4 text-sm text-muted-foreground">
                  No conversations found.
                </div>
              ) : (
                <div className="space-y-3">
                  {Object.entries(filteredConversations).map(([organization, conversations]) => (
                    <div key={organization}>
                      <div 
                        className="flex items-center justify-between p-2 cursor-pointer hover:bg-muted/50 rounded-lg transition-colors"
                        onClick={() => toggleOrganizationExpansion(organization)}
                      >
                        <div className="flex items-center gap-2 min-w-0">
                          <div className="h-6 w-6 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <User className="h-3 w-3 text-primary/70" />
                          </div>
                          <span className="font-medium text-xs truncate">{organization}</span>
                          <Badge variant="secondary" className="text-xs flex-shrink-0">{conversations.length}</Badge>
                        </div>
                        <ChevronDown className={cn(
                          "h-3 w-3 text-muted-foreground transition-transform duration-200 flex-shrink-0",
                          isOrganizationExpanded(organization) && "rotate-180"
                        )} />
                      </div>
                      {isOrganizationExpanded(organization) && (
                        <div className="ml-2 mt-1 space-y-1">
                          {conversations.map((conversation) => (
                            <ConversationCard 
                              key={conversation.id} 
                              conversation={conversation} 
                              scans={scans}
                              isSelected={selectedConversationId === conversation.id}
                              onSelect={() => setSelectedConversationId(conversation.id)}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          {/* RESPONSIVE CHANGE: The main content area is now conditionally rendered.
              - On small screens, it's hidden by default and only shown when a conversation is selected.
              - On medium screens (md) and up, it's always visible. */}
          <div className={cn(
            "flex-col bg-white dark:bg-[#111827] min-w-0",
            "w-full md:flex-1",
            selectedConversationId ? "flex" : "hidden md:flex"
          )}>
            {selectedConversation ? (
              <>
                {/* Header */}
                <div className="p-4 border-b flex items-center justify-between dark:border-slate-700 flex-shrink-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {/* RESPONSIVE CHANGE: Back button visible only on small screens (md:hidden) */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="md:hidden flex-shrink-0"
                      onClick={() => setSelectedConversationId(null)}
                    >
                      <ArrowLeft className="h-5 w-5" />
                    </Button>
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-sm truncate">{selectedConversation.title}</h3>
                      <p className="text-xs text-muted-foreground truncate">
                        {selectedConversation.user.displayName} • {format(new Date(selectedConversation.updatedAt), "MMM d")}
                      </p>
                    </div>
                  </div>
                  {selectedConversation.associatedScanIds?.length > 0 && (
                    <Badge variant="outline" className="flex-shrink-0 ml-2">
                      <Shield className="h-3 w-3 mr-1" />
                      {selectedConversation.associatedScanIds.length}
                    </Badge>
                  )}
                </div>

                {/* Chat area */}
                <div className="flex-1 overflow-y-auto p-4 space-y-2">
                  {selectedConversation.messages.map((message: MessageType) => (
                    <ConversationMessage key={message.id} message={message} user={selectedConversation.user} />
                  ))}
                  
                  {/* Associated Scans */}
                  {selectedConversation.associatedScans?.length > 0 && (
                    <div className="space-y-3 py-4">
                      <hr className="dark:border-slate-700" />
                      <h4 className="font-medium text-sm text-muted-foreground px-4">Associated Scans</h4>
                      <div className="space-y-3 max-w-full">
                        {selectedConversation.associatedScans.map((scan: any) => (
                          <div key={scan.id} className="max-w-full px-4">
                            <ScanCard scan={scan} />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                <div className="bg-muted/30 p-8 rounded-lg border border-dashed dark:border-slate-700/50 flex flex-col items-center max-w-sm">
                  <MessageSquare className="h-12 w-12 text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium">Select a conversation</h3>
                  <p className="text-muted-foreground mt-2 text-sm">
                    {loading || scansLoading
                      ? "Loading conversations..."
                      : Object.keys(filteredConversations).length === 0
                        ? "No conversations found."
                        : "Select a conversation from the list to view its details."
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};