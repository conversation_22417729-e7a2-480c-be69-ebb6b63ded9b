import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
    Clock, Download, CheckCircle2, AlertCircle,
    Trash2, Loader2,
    ChevronDown, ChevronUp,
    FileText, Paperclip, ExternalLink,
    Edit, Plus, Minus, Eye,
    Brain, Search, AlertTriangle, CheckCircle, FileCode, Copy,
    RefreshCw, X, ShieldAlert // Added ShieldAlert for vulnerability section
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatAssetType } from "@/lib/utils/formatAssetType"
import { formatScanTitle } from "@/lib/utils/formatScanTitle"
import "@/styles/progress-slider.css"
import "@/styles/progress-stepper.css"
import "@/styles/enhanced-scan-design.css"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import React, { useState, useMemo, useCallback } from "react"
import { useAuth } from "@/hooks/useAuth"
import { useScans } from "@/context/ScanContext"
import { toast } from "@/components/ui/use-toast";

// File information type
type FileInfo = {
    url: string;
    originalName: string;
    uploadedAt: string;
    size?: number;
    contentType?: string;
}

// Define the types for AI summary
type Severity = "critical" | "high" | "medium" | "low" | string; // Allow string for flexibility if API returns other values
type Priority = "immediate" | "high" | "medium" | "low" | string; // Allow string for flexibility

type Finding = {
    finding: string;
    severity: Severity;
};

type Recommendation = {
    recommendation: string;
    priority: Priority;
};

type AISummaryData = {
    summary: string;
    keyFindings: Finding[];
    potentialImpacts: string[];
    recommendations: Recommendation[];
    rawAnalysis?: string;
};

type ScanStatus = "pending" | "in-progress" | "completed" | "re-test";

type ScanType = {
    id: string;
    name: string; // Assuming name might be used, though not explicitly in current JSX
    status: ScanStatus;
    target: string;
    requestedAt: Date | string; // Allow string for initial API response
    completedAt?: Date | string; // Allow string for initial API response
    criticalVulnerabilities: number;
    highVulnerabilities: number;
    mediumVulnerabilities: number;
    lowVulnerabilities: number;
    resultFileUrl?: string;
    userId?: string;
    files?: FileInfo[];
    aiSummary?: AISummaryData; // This should ideally be strictly AISummaryData from API

    // Explicitly defined properties based on usage
    progress?: number;
    userEmail?: string;
    asset_type?: string;
    conversationTitle?: string;
    platform?: string;
    environment?: string;

    // If there are truly dynamic/unknown extra fields from an API:
    [key: string]: any; // Kept as per original, but minimize reliance
    retestNotes?: {
        vulnerabilityId: string;
        vulnerabilityTitle: string;
        note: string;
        requestedBy: string;
        requestedAt: string;
    }[];
}

interface ScanCardProps {
    scan: ScanType;
    onStatusChange?: () => void; // Kept for immediate parent reaction if needed
}

// Helper to process stored AI summary and ensure correct types
const processStoredAiSummary = (storedSummary: any): AISummaryData | undefined => {
    if (!storedSummary || typeof storedSummary.summary !== 'string') {
        return undefined;
    }
    return {
        summary: storedSummary.summary,
        keyFindings: Array.isArray(storedSummary.keyFindings)
            ? storedSummary.keyFindings.map((f: any) => ({
                finding: String(f.finding || ''),
                severity: String(f.severity || 'low') as Severity,
            }))
            : [],
        potentialImpacts: Array.isArray(storedSummary.potentialImpacts)
            ? storedSummary.potentialImpacts.map(String)
            : [],
        recommendations: Array.isArray(storedSummary.recommendations)
            ? storedSummary.recommendations.map((r: any) => ({
                recommendation: String(r.recommendation || ''),
                priority: String(r.priority || 'low') as Priority,
            }))
            : [],
        rawAnalysis: typeof storedSummary.rawAnalysis === 'string' ? storedSummary.rawAnalysis : undefined,
    };
};


export function ScanCard({ scan, onStatusChange }: ScanCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadStage, setUploadStage] = useState<string | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isDeletingReport, setIsDeletingReport] = useState(false);
    const [deleteConfirmationId, setDeleteConfirmationId] = useState<string | null>(null);
    const [deleteReportConfirmation, setDeleteReportConfirmation] = useState(false);
    const [animateStatus, setAnimateStatus] = useState<string | null>(null);
    const [isReportModalOpen, setIsReportModalOpen] = useState(false);

    // Function to handle delete confirmation
    const handleDeleteConfirmation = (id: string) => {
        setDeleteConfirmationId(id);
    };

    const [aiSummary, setAiSummary] = useState<AISummaryData | undefined>(() => processStoredAiSummary(scan.aiSummary));
    const [isLoadingAiSummary, setIsLoadingAiSummary] = useState(false);
    const [aiSummaryError, setAiSummaryError] = useState<string | null>(null);
    const [aiSummaryStage, setAiSummaryStage] = useState<string | null>(null);

    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [refreshKey, setRefreshKey] = useState(0);

    const [editedData, setEditedData] = useState({
        status: scan.status,
        criticalVulnerabilities: scan.criticalVulnerabilities,
        highVulnerabilities: scan.highVulnerabilities,
        mediumVulnerabilities: scan.mediumVulnerabilities,
        lowVulnerabilities: scan.lowVulnerabilities,
        progress: scan.progress || 0,
    });

    // Sync editedData with scan prop changes when not editing
    React.useEffect(() => {
        if (!isEditing) {
            setEditedData({
                status: scan.status,
                criticalVulnerabilities: scan.criticalVulnerabilities,
                highVulnerabilities: scan.highVulnerabilities,
                mediumVulnerabilities: scan.mediumVulnerabilities,
                lowVulnerabilities: scan.lowVulnerabilities,
                progress: scan.progress || 0,
            });
        }
    }, [scan.status, scan.criticalVulnerabilities, scan.highVulnerabilities, scan.mediumVulnerabilities, scan.lowVulnerabilities, scan.progress, isEditing]);

    const { user, role } = useAuth();
    const { deleteScan, updateScan, updateScanLocally, fetchData: fetchScans } = useScans();

    // Memoized values for display
    const displayTotalVulnerabilities = useMemo(() =>
        scan.criticalVulnerabilities + scan.highVulnerabilities + scan.mediumVulnerabilities + scan.lowVulnerabilities,
        [scan.criticalVulnerabilities, scan.highVulnerabilities, scan.mediumVulnerabilities, scan.lowVulnerabilities]
    );

    const editedTotalVulnerabilities = useMemo(() =>
        editedData.criticalVulnerabilities + editedData.highVulnerabilities + editedData.mediumVulnerabilities + editedData.lowVulnerabilities,
        [editedData.criticalVulnerabilities, editedData.highVulnerabilities, editedData.mediumVulnerabilities, editedData.lowVulnerabilities]
    );

    const displayFiles = useMemo((): FileInfo[] => {
        const files = scan.files || [];
        if (files.length === 0 && scan.resultFileUrl) {
            return [{
                originalName: scan.target ? `Report for ${scan.target}` : "Report File",
                url: scan.resultFileUrl,
                uploadedAt: (scan.completedAt ? new Date(scan.completedAt) : new Date()).toISOString(),
                contentType: "application/pdf", // Default, actual type might be unknown
                size: undefined, // Size unknown from just URL
            }];
        }
        return files;
    }, [scan.files, scan.resultFileUrl, scan.completedAt, scan.target]);


    const closeReportModal = useCallback(() => {
        setIsReportModalOpen(false);
        // Reset AI summary states if modal is closed, to ensure fresh load next time if needed
        // setAiSummary(processStoredAiSummary(scan.aiSummary)); // Or null if you always want to re-fetch/re-process
        // setAiSummaryError(null);
        // setAiSummaryStage(null);
    }, []);

    const getAiSummary = useCallback(async () => {
        if (!scan.resultFileUrl) {
            setAiSummaryError("No report file available for analysis.");
            setIsLoadingAiSummary(false);
            setAiSummaryStage("error");
            return;
        }

        setIsLoadingAiSummary(true);
        setAiSummaryError(null);
        setAiSummary(undefined); // Clear previous summary

        try {
            // Check for existing summary in the scan prop first
            const existingSummary = processStoredAiSummary(scan.aiSummary);
            if (existingSummary) {
                console.log("Using pre-generated AI summary from scan prop.");
                setAiSummaryStage("loading-existing");
                // Simulate loading for UX consistency
                await new Promise(resolve => setTimeout(resolve, 300));
                setAiSummary(existingSummary);
                setAiSummaryStage("completed");
                setIsLoadingAiSummary(false);
                return;
            }

            console.log("No pre-generated summary found in prop, attempting to generate new summary.");
            setAiSummaryStage("initializing");

            const idToken = await user?.getIdToken();
            if (!idToken) {
                throw new Error("Authentication token not available.");
            }

            setAiSummaryStage("downloading-report");
            const response = await fetch('/api/analyze-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`,
                },
                body: JSON.stringify({
                    reportUrl: scan.resultFileUrl,
                    scanId: scan.id,
                    target: scan.target,
                    vulnerabilities: {
                        critical: scan.criticalVulnerabilities,
                        high: scan.highVulnerabilities,
                        medium: scan.mediumVulnerabilities,
                        low: scan.lowVulnerabilities,
                    }
                }),
            });

            setAiSummaryStage("analyzing-report");

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to analyze report (${response.status})`);
            }

            setAiSummaryStage("processing-results");
            const data = await response.json();

            // Assuming data.summary is the AISummaryData object
            const processedSummary = processStoredAiSummary(data.summary);
            if (!processedSummary) {
                throw new Error("Received invalid summary format from API.");
            }
            setAiSummary(processedSummary);

            // Update the scan in context so it's available for future views without regenerating
            // The API /api/analyze-report is expected to save it to DB.
            // This local update ensures UI consistency until next full fetch.
            updateScanLocally(scan.id, { aiSummary: processedSummary });
            setAiSummaryStage("completed");

        } catch (error: any) {
            console.error("Error getting AI summary:", error);
            setAiSummaryError(error.message || "Failed to generate AI summary.");
            setAiSummaryStage("error");
        } finally {
            setIsLoadingAiSummary(false);
        }
    }, [scan.id, scan.resultFileUrl, scan.target, scan.criticalVulnerabilities, scan.highVulnerabilities, scan.mediumVulnerabilities, scan.lowVulnerabilities, scan.aiSummary, user, updateScanLocally]);


    const currentStatus = useMemo(() => isEditing ? editedData.status : scan.status, [isEditing, editedData.status, scan.status]);
    const currentProgress = useMemo(() => isEditing ? editedData.progress : (scan.progress || 0), [isEditing, editedData.progress, scan.progress]);

    const getStatusIcon = useCallback(() => {
        switch (currentStatus) {
            case "pending": return <Clock className="h-4 w-4 text-yellow-500" />;
            case "in-progress": return <AlertCircle className="h-4 w-4 text-blue-500" />;
            case "re-test": return <RefreshCw className="h-4 w-4 text-purple-500" />;
            case "completed": return <CheckCircle2 className="h-4 w-4 text-green-500" />;
            default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
        }
    }, [currentStatus]);

    const getStatusText = useCallback(() => {
        switch (currentStatus) {
            case "pending": return "Pending";
            case "in-progress": return "In Progress";
            case "re-test": return "Re-test";
            case "completed": return "Completed";
            default: return "Unknown";
        }
    }, [currentStatus]);

    const getStatusColor = useCallback(() => {
        switch (currentStatus) {
            case "pending": return "text-yellow-500 bg-yellow-500/10";
            case "in-progress": return "text-blue-500 bg-blue-500/10";
            case "re-test": return "text-purple-500 bg-purple-500/10";
            case "completed": return "text-green-500 bg-green-500/10";
            default: return "text-gray-500 bg-gray-500/10";
        }
    }, [currentStatus]);

    const getCardClass = useCallback(() => {
        // Base class is applied by cn, this adds status specific one
        switch (currentStatus) {
            case "pending": return "scan-card-pending";
            case "in-progress": return "scan-card-in-progress";
            case "re-test": return "scan-card-re-test";
            case "completed": return "scan-card-completed";
            default: return "";
        }
    }, [currentStatus]);

    const getCardHoverClass = useCallback(() => {
        switch (currentStatus) {
            case "pending": return "card-hover-pending";
            case "in-progress": return "card-hover-in-progress";
            case "re-test": return "card-hover-re-test";
            case "completed": return "card-hover-completed";
            default: return "";
        }
    }, [currentStatus]);


    const handleStatusChange = (newStatus: ScanStatus) => {
        const updates: Partial<ScanType> = { status: newStatus };
        if (newStatus === "completed" && editedData.status !== "completed") {
            updates.completedAt = new Date().toISOString();
        }
        if (newStatus === "re-test" || newStatus === "in-progress") {
            updates.progress = editedData.progress === 100 ? 0 : editedData.progress;
        }

        setEditedData(prev => ({ ...prev, ...updates }));
        setAnimateStatus(newStatus);
        setTimeout(() => setAnimateStatus(null), 1000);
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file || isUploading) return;

        setIsUploading(true);
        setUploadStage("preparing");

        try {
            setUploadStage("authenticating");
            const idToken = await user?.getIdToken();
            if (!idToken) throw new Error("Authentication token not available.");

            setUploadStage("preparing-upload");
            const formData = new FormData();
            formData.append('file', file);
            formData.append('scanId', scan.id);
            formData.append('replaceExisting', 'true');

            setUploadStage("uploading");

            // Set a timeout to handle cases where the upload hangs
            const uploadTimeout = setTimeout(() => {
                console.error("Upload timed out after 3 minutes");
                setUploadStage("error");
                toast({
                    title: "Upload Timeout",
                    description: "The upload is taking longer than expected. Please try again with a smaller file or contact support.",
                    variant: "destructive"
                });
                setIsUploading(false);
            }, 180000); // 3 minutes timeout

            const response = await fetch('/api/upload-scan-result', {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${idToken}` },
                body: formData,
            });

            // Clear the timeout since we got a response
            clearTimeout(uploadTimeout);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to upload file (${response.status})`);
            }

            setUploadStage("processing-response");
            const fileData = await response.json(); // Expects { fileUrl, vulnerabilityCounts?, aiSummary? }

            if (fileData.fileUrl) {
                setUploadStage("updating-scan-data");
                const serverFile: FileInfo = {
                    originalName: file.name,
                    size: file.size,
                    contentType: file.type,
                    uploadedAt: new Date().toISOString(),
                    url: fileData.fileUrl
                };

                const updates: Partial<ScanType> = {
                    files: [serverFile],
                    resultFileUrl: fileData.fileUrl,
                };

                if (fileData.vulnerabilityCounts) {
                    setUploadStage("updating-vulnerability-counts");
                    updates.criticalVulnerabilities = fileData.vulnerabilityCounts.critical;
                    updates.highVulnerabilities = fileData.vulnerabilityCounts.high;
                    updates.mediumVulnerabilities = fileData.vulnerabilityCounts.medium;
                    updates.lowVulnerabilities = fileData.vulnerabilityCounts.low;
                }
                if (fileData.aiSummary) {
                    setUploadStage("updating-ai-summary");
                    updates.aiSummary = processStoredAiSummary(fileData.aiSummary);
                }

                // If counts or AI summary are missing, fetch latest full scan data.
                // Otherwise, just update locally.
                if (!fileData.vulnerabilityCounts || !fileData.aiSummary) {
                    setUploadStage("fetching-latest-data");
                    await fetchScans('organization'); // This will refresh all scans, including this one
                } else {
                    setUploadStage("finalizing");
                    // Create a new object with the correct types for the context
                    const typedUpdates: any = { ...updates };

                    // Convert any string dates to Date objects before updating
                    if (typedUpdates.completedAt && typeof typedUpdates.completedAt === 'string') {
                        typedUpdates.completedAt = new Date(typedUpdates.completedAt);
                    }
                    if (typedUpdates.requestedAt && typeof typedUpdates.requestedAt === 'string') {
                        typedUpdates.requestedAt = new Date(typedUpdates.requestedAt);
                    }

                    updateScanLocally(scan.id, typedUpdates);
                }
            }
            setUploadStage("completed");
            toast({ title: "Success", description: "Result file uploaded successfully." });
        } catch (error: any) {
            console.error("Error uploading file:", error);
            setUploadStage("error");
            toast({ title: "Error", description: error.message || "Could not upload result file.", variant: "destructive" });
            // No need to revert local update, as we only update after success or full fetch
        } finally {
            if (event.target) event.target.value = '';
            setTimeout(() => {
                setIsUploading(false);
                setUploadStage(null);
            }, 1500);
        }
    };

    const handleSaveChanges = async () => {
        if (isSaving) return;
        setIsSaving(true);

        const oldStatus = scan.status;

        try {
            await updateScan(scan.id, editedData);

            toast({ title: "Success", description: "Scan updated successfully." });
            setIsEditing(false);

            // Email notifications can be triggered here or, even better, from the backend after the update
            if (oldStatus !== editedData.status && scan.userEmail) {
                const idToken = await user?.getIdToken();
                console.log(`Status changed from ${oldStatus} to ${editedData.status}, sending email notifications`);
                const recipients = [scan.userEmail];
                if (scan.userEmail !== '<EMAIL>') {
                    recipients.push('<EMAIL>');
                }
                fetch('/api/send-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${idToken}` },
                    body: JSON.stringify({
                        scanId: scan.id,
                        recipients: [...new Set(recipients.filter(Boolean))],
                        oldStatus,
                        newStatus: editedData.status,
                        scanDetails: { id: scan.id, target: scan.target, asset_type: scan.asset_type, resultFileUrl: scan.resultFileUrl }
                    }),
                }).catch(emailError => console.error('Failed to send email notifications:', emailError));
            }

            if (onStatusChange && oldStatus !== editedData.status) {
                onStatusChange();
            }

        } catch (error: any) {
            // The context now handles reverting the state, so we just need to inform the user.
            toast({ title: "Error", description: error.message || "Could not update scan.", variant: "destructive" });
        } finally {
            setIsSaving(false);
        }
    };

    const confirmDeleteScan = async () => {
        if (!scan.id || isDeleting) return;
        setIsDeleting(true);
        setDeleteConfirmationId(null);

        try {
            const response = await deleteScan(scan.id); // deleteScan from context
            let description = `Scan for "${scan.target}" deleted successfully.`;

            // Add information about deleted files if any
            if (response && 'filesDeleted' in response && response.filesDeleted > 0) {
                description += ` ${response.filesDeleted} associated file${response.filesDeleted > 1 ? 's were' : ' was'} also deleted.`;
            }


            // Add information about deleted vulnerabilities if any
            if (response && 'vulnerabilitiesDeleted' in response && response.vulnerabilitiesDeleted !== undefined && response.vulnerabilitiesDeleted > 0) {
                description += ` ${response.vulnerabilitiesDeleted} vulnerability card${response.vulnerabilitiesDeleted > 1 ? 's were' : ' was'} also removed.`;
            }

            toast({ title: "Success", description });
            // onStatusChange might not be needed if deleteScan refreshes context
        } catch (error: any) {
            console.error(`Error deleting scan ${scan.id}:`, error);
            toast({ title: "Error", description: error.message || `Could not delete scan.`, variant: "destructive" });
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteReportConfirm = async () => {
        if (!scan.id || isDeletingReport) return;
        setIsDeletingReport(true);

        try {
            const idToken = await user?.getIdToken();
            if (!idToken) throw new Error("Authentication token not available.");

            const response = await fetch(`/api/delete-scan-report?scanId=${scan.id}`, {
                method: 'DELETE',
                headers: { 'Authorization': `Bearer ${idToken}` },
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to delete report (${response.status})`);
            }

            // Get the response data
            const responseData = await response.json();
            const vulnerabilitiesDeleted = responseData.vulnerabilitiesDeleted || 0;

            // Fetch updated scan data to reflect report deletion and reset counts if backend does it
            await fetchScans('organization');
            // Fallback local update if fetchScans is slow or doesn't immediately reflect
            updateScanLocally(scan.id, {
                resultFileUrl: undefined,
                files: [],
                aiSummary: undefined, // Also clear AI summary
                criticalVulnerabilities: 0, // Assuming deletion resets counts
                highVulnerabilities: 0,
                mediumVulnerabilities: 0,
                lowVulnerabilities: 0,
            });

            // Show success message with vulnerability deletion info
            toast({
                title: "Success",
                description: vulnerabilitiesDeleted > 0
                    ? `Report deleted successfully. ${vulnerabilitiesDeleted} associated vulnerabilities were also removed.`
                    : "Report deleted successfully."
            });
            setDeleteReportConfirmation(false);
        } catch (error: any) {
            console.error("Error deleting report:", error);
            toast({ title: "Error", description: error.message || "Could not delete report.", variant: "destructive" });
        } finally {
            setIsDeletingReport(false);
        }
    };

    const handleRequestRetest = async () => {
        if (isSaving) return;
        setIsSaving(true);
        try {
            const idToken = await user?.getIdToken();
            if (!idToken) throw new Error("Authentication token not available.");

            const newStatusData = { status: "re-test" as ScanStatus, progress: 0 };
            updateScanLocally(scan.id, newStatusData); // Optimistic update

            const response = await fetch(`/api/scans`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${idToken}` },
                body: JSON.stringify({ scanId: scan.id, ...newStatusData }),
            });

            if (!response.ok) {
                updateScanLocally(scan.id, { status: "completed", progress: scan.progress }); // Revert
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to request re-test (${response.status})`);
            }

            // Send email notification for re-test request
            if (scan.userEmail) {
                const recipients = [scan.userEmail];
                if (scan.userEmail !== '<EMAIL>') {
                    recipients.push('<EMAIL>');
                }
                fetch('/api/send-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${idToken}` },
                    body: JSON.stringify({
                        scanId: scan.id, recipients: [...new Set(recipients.filter(Boolean))], oldStatus: "completed", newStatus: "re-test",
                        scanDetails: { id: scan.id, target: scan.target, asset_type: scan.asset_type, resultFileUrl: scan.resultFileUrl }
                    }),
                }).catch(emailError => console.error('Error sending re-test email notifications:', emailError));
            }

            toast({ title: "Success", description: "Re-test requested successfully." });
            if (onStatusChange) onStatusChange();
        } catch (error: any) {
            console.error("Error requesting re-test:", error);
            toast({ title: "Error", description: error.message || "Could not request re-test.", variant: "destructive" });
        } finally {
            setIsSaving(false);
        }
    };

    const getAnimationClass = useCallback(() => {
        if (!animateStatus) return "";
        const baseClass = "status-change-animation";
        switch (animateStatus) {
            case "pending": return `${baseClass} ${baseClass}-pending`;
            case "in-progress": return `${baseClass} ${baseClass}-in-progress`;
            case "re-test": return `${baseClass} ${baseClass}-re-test`;
            case "completed": return `${baseClass} ${baseClass}-completed`;
            default: return "";
        }
    }, [animateStatus]);

    const getProgressBgColor = useCallback(() => {
        switch (currentStatus) {
            case "pending": return "bg-yellow-100 dark:bg-yellow-950/30";
            case "in-progress": return "bg-gray-200 dark:bg-gray-700";
            case "re-test": return "bg-gray-200 dark:bg-gray-700";
            case "completed": return "bg-green-100 dark:bg-green-950/30";
            default: return "bg-gray-100 dark:bg-gray-950/30";
        }
    }, [currentStatus]);

    const getProgressFillColor = useCallback(() => {
        switch (currentStatus) {
            case "pending": return "bg-yellow-500";
            case "in-progress": return "bg-blue-600";
            case "re-test": return "bg-purple-600";
            case "completed": return "bg-green-500";
            default: return "bg-gray-500";
        }
    }, [currentStatus]);

    const cardTitleText = useMemo(() => {
        // Use the formatScanTitle utility function for consistent title formatting
        return formatScanTitle(scan);
    }, [scan]);

    const requestedAtDate = useMemo(() => new Date(scan.requestedAt), [scan.requestedAt]);
    const completedAtDate = useMemo(() => scan.completedAt ? new Date(scan.completedAt) : null, [scan.completedAt]);

    const renderVulnerabilityCountEditor = (
        severityName: "critical" | "high" | "medium" | "low",
        severityKey: "criticalVulnerabilities" | "highVulnerabilities" | "mediumVulnerabilities" | "lowVulnerabilities",
        colorClassPrefix: "red" | "orange" | "yellow" | "blue"
    ) => (
        <div className="flex items-center">
            <Button
                variant="ghost"
                size="sm"
                className={`h-6 w-6 p-0 hover:bg-${colorClassPrefix}-100 dark:hover:bg-${colorClassPrefix}-900/20`}
                onClick={() => setEditedData(prev => ({
                    ...prev,
                    [severityKey]: Math.max(0, prev[severityKey] - 1)
                }))}
                disabled={editedData[severityKey] <= 0}
            >
                <Minus className="h-3 w-3" />
            </Button>
            <span className="text-xs font-medium w-6 text-center">{editedData[severityKey]}</span>
            <Button
                variant="ghost"
                size="sm"
                className={`h-6 w-6 p-0 hover:bg-${colorClassPrefix}-100 dark:hover:bg-${colorClassPrefix}-900/20`}
                onClick={() => setEditedData(prev => ({
                    ...prev,
                    [severityKey]: prev[severityKey] + 1
                }))}
            >
                <Plus className="h-3 w-3" />
            </Button>
            <span className="text-xs font-medium ml-1 capitalize">{severityName}</span>
        </div>
    );

    return (
        <Card
            className={cn(
                "w-full max-w-full transition-all duration-300 animate-fade-in mb-6 scan-card",
                "bg-gradient-to-br from-white via-white to-slate-50/80 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800/80",
                "border border-slate-200/60 hover:border-primary/40 dark:border-gray-700/60 dark:hover:border-primary/40",
                "shadow-lg hover:shadow-xl hover:shadow-primary/10 dark:shadow-gray-900/20",
                "backdrop-blur-sm rounded-2xl overflow-hidden",
                "group relative hover:-translate-y-1",
                getCardClass(),
                getAnimationClass(),
                isDeleting && "opacity-50",
                isExpanded && "mb-8"
            )}
            onClick={(e) => e.stopPropagation()}
        >
            <CardHeader className="pb-5 pt-7 px-7 relative z-10 bg-gradient-to-r from-transparent via-white/50 to-transparent dark:via-gray-900/50" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-start justify-between">
                    <div className="space-y-4 flex-1">
                        <div className="flex flex-wrap items-center gap-3">
                            {isEditing ? (
                                <div className="flex flex-col gap-3">
                                    <div className="flex items-center gap-2">
                                        <div className="text-xs font-medium text-muted-foreground">Status:</div>
                                        <div className="flex gap-1.5 flex-wrap">
                                            {(["pending", "in-progress", "re-test", "completed"] as ScanStatus[]).map(s => (
                                                <Button
                                                    key={s}
                                                    variant={editedData.status === s ? "default" : "outline"}
                                                    size="sm"
                                                    className={cn(
                                                        "h-8 px-4 text-xs font-medium rounded-full transition-all duration-200 shadow-sm",
                                                        editedData.status === s && (
                                                            s === "pending" ? "bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600 text-white border-0" :
                                                                s === "in-progress" ? "bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white border-0" :
                                                                    s === "re-test" ? "bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600 text-white border-0" :
                                                                        "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white border-0"
                                                        )
                                                    )}
                                                    onClick={() => handleStatusChange(s)}
                                                >
                                                    {s === "pending" && <Clock className="mr-1.5 h-3 w-3" />}
                                                    {s === "in-progress" && <AlertCircle className="mr-1.5 h-3 w-3" />}
                                                    {s === "re-test" && <RefreshCw className="mr-1.5 h-3 w-3" />}
                                                    {s === "completed" && <CheckCircle2 className="mr-1.5 h-3 w-3" />}
                                                    {s.charAt(0).toUpperCase() + s.slice(1).replace("-", " ")}
                                                </Button>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <Badge
                                    variant="outline"
                                    className={cn(
                                        "flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 shadow-sm border-2",
                                        currentStatus === "pending" && "bg-gradient-to-r from-yellow-50 to-amber-50 text-yellow-700 border-yellow-200 hover:from-yellow-100 hover:to-amber-100 dark:from-yellow-900/20 dark:to-amber-900/20 dark:text-yellow-400 dark:border-yellow-800/50",
                                        currentStatus === "in-progress" && "bg-gradient-to-r from-blue-50 to-cyan-50 text-blue-700 border-blue-200 hover:from-blue-100 hover:to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20 dark:text-blue-400 dark:border-blue-800/50",
                                        currentStatus === "re-test" && "bg-gradient-to-r from-purple-50 to-violet-50 text-purple-700 border-purple-200 hover:from-purple-100 hover:to-violet-100 dark:from-purple-900/20 dark:to-violet-900/20 dark:text-purple-400 dark:border-purple-800/50",
                                        currentStatus === "completed" && "bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200 hover:from-green-100 hover:to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 dark:text-green-400 dark:border-green-800/50",
                                        animateStatus && "status-badge-animation"
                                    )}
                                >
                                    {getStatusIcon()}
                                    {getStatusText()}
                                </Badge>
                            )}
                            {displayFiles.length > 0 && (
                                <Badge
                                    variant="outline"
                                    className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium rounded-full bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-950/30 dark:to-gray-950/30 border-slate-200 dark:border-slate-800 hover:from-slate-100 hover:to-gray-100 dark:hover:from-slate-900/50 dark:hover:to-gray-900/50 transition-all duration-200 shadow-sm"
                                    asChild
                                >
                                    <a
                                        href={displayFiles[0].url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        title={`View ${displayFiles.length} attached file(s)`}
                                        className="flex items-center gap-2"
                                    >
                                        <Paperclip className="h-3.5 w-3.5" />
                                        <span>{displayFiles.length} File{displayFiles.length !== 1 ? 's' : ''}</span>
                                    </a>
                                </Badge>
                            )}
                        </div>
                        <CardTitle className="text-xl font-bold leading-tight tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 dark:from-white dark:via-gray-100 dark:to-gray-200 bg-clip-text text-transparent">
                            {cardTitleText}
                        </CardTitle>
                    </div>
                    {role === 'admin' && (
                        <div className="flex items-center gap-3 ml-4">
                            {isEditing ? (
                                <>
                                    <Button
                                        variant="outline" size="sm"
                                        className="h-9 px-4 text-xs font-medium rounded-lg bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-all duration-200 shadow-sm border-border/50"
                                        onClick={() => {
                                            setIsEditing(false);
                                            setEditedData({
                                                status: scan.status,
                                                criticalVulnerabilities: scan.criticalVulnerabilities,
                                                highVulnerabilities: scan.highVulnerabilities,
                                                mediumVulnerabilities: scan.mediumVulnerabilities,
                                                lowVulnerabilities: scan.lowVulnerabilities,
                                                progress: scan.progress || 0
                                            });
                                        }}
                                        disabled={isSaving || isDeleting}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        variant="default" size="sm"
                                        className={cn(
                                            "h-9 px-4 text-xs font-medium rounded-lg transition-all duration-200 shadow-sm",
                                            editedData.status === "pending" && "bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600",
                                            editedData.status === "in-progress" && "bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600",
                                            editedData.status === "re-test" && "bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600",
                                            editedData.status === "completed" && "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                                        )}
                                        onClick={handleSaveChanges} disabled={isSaving || isDeleting}
                                    >
                                        {isSaving ? <><Loader2 className="mr-2 h-3.5 w-3.5 animate-spin" />Saving...</> : 'Save'}
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button 
                                        variant="outline" 
                                        size="icon" 
                                        className="h-10 w-10 rounded-xl bg-gradient-to-br from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 hover:bg-primary/10 hover:text-primary transition-all duration-200 shadow-sm border-border/50 hover:border-primary/30" 
                                        onClick={() => setIsEditing(true)} 
                                        disabled={isDeleting}
                                    >
                                        <Edit className="h-4 w-4" />
                                        <span className="sr-only">Edit scan</span>
                                    </Button>
                                    {deleteConfirmationId === scan.id ? (
                                        <div className="flex space-x-1 bg-background/95 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-border/50">
                                            <Button variant="ghost" size="icon" className="h-8 w-8 text-green-500 hover:bg-green-500/10 rounded-lg" onClick={(e) => { e.stopPropagation(); confirmDeleteScan(); }} disabled={isDeleting}>
                                                <CheckCircle className="h-4 w-4" /> <span className="sr-only">Confirm delete</span>
                                            </Button>
                                            <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive hover:bg-destructive/10 rounded-lg" onClick={(e) => { e.stopPropagation(); setDeleteConfirmationId(null); }} disabled={isDeleting}>
                                                <X className="h-4 w-4" /> <span className="sr-only">Cancel delete</span>
                                            </Button>
                                        </div>
                                    ) : (
                                        <Button 
                                            variant="outline" 
                                            size="icon" 
                                            className="h-10 w-10 rounded-xl bg-gradient-to-br from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 hover:text-destructive hover:bg-destructive/10 transition-all duration-200 shadow-sm border-border/50 hover:border-destructive/30" 
                                            onClick={() => handleDeleteConfirmation(scan.id)} 
                                            disabled={isDeleting || isEditing}
                                        >
                                            {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                                            <span className="sr-only">Delete scan</span>
                                        </Button>
                                    )}
                                </>
                            )}
                        </div>
                    )}
                </div>
                <CardDescription className="mt-5">
                    <div className="flex flex-wrap items-center gap-x-8 gap-y-4 text-sm">
                        <div className="flex items-center gap-3 text-slate-600 dark:text-slate-300">
                            <div className="h-2.5 w-2.5 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 shadow-sm" />
                            <span className="font-semibold">Requested {requestedAtDate.toLocaleString(undefined, { year: 'numeric', month: 'numeric', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</span>
                        </div>
                        {completedAtDate && (
                            <div className="flex items-center gap-3 text-slate-600 dark:text-slate-300">
                                <div className="h-2.5 w-2.5 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 shadow-sm" />
                                <span className="font-semibold">Completed {completedAtDate.toLocaleString(undefined, { year: 'numeric', month: 'numeric', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</span>
                            </div>
                        )}
                    </div>
                </CardDescription>
            </CardHeader>
            
            {/* Enhanced gradient overlay for visual depth */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/[0.02] via-transparent to-accent/[0.03] pointer-events-none" />
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent" />

            {/* Status-specific content */}
            {((isEditing ? editedData.status : scan.status) === "in-progress" || (isEditing ? editedData.status : scan.status) === "re-test") && (
                <div className="px-6 pt-2 pb-5 bg-gradient-to-br from-muted/10 via-transparent to-transparent border-t border-border/30">
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="relative h-3 w-3">
                                    <div className={`absolute h-full w-full rounded-full ${(isEditing ? editedData.status : scan.status) === "in-progress" ? "bg-blue-500" : "bg-purple-500"} animate-pulse opacity-75`}></div>
                                    <div className={`relative h-3 w-3 rounded-full ${(isEditing ? editedData.status : scan.status) === "in-progress" ? "bg-blue-500" : "bg-purple-500"}`}></div>
                                </div>
                                <span className="font-semibold text-base">{(isEditing ? editedData.status : scan.status) === "in-progress" ? "Scan in Progress" : "Re-testing in Progress"}</span>
                            </div>
                            <div className={`px-3 py-1.5 rounded-full text-sm font-bold ${currentStatus === "in-progress" ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400" : "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"}`}>
                                {currentProgress}%
                            </div>
                        </div>
                    {isEditing && role === 'admin' ? (
                       <div className="stepper-wrapper">
                          <div className="stepper-container">
                              <div className="stepper-track"></div>
                              <div className={`stepper-progress ${editedData.status === 're-test' ? 're-test' : ''}`} style={{ width: `${editedData.progress}%` }}></div>
                              <div className="stepper-checkpoints">
                                  {[0, 25, 50, 75, 100].map((value) => (
                                      <div
                                          key={value}
                                          className={`stepper-checkpoint ${editedData.progress >= value ? 'active' : ''} ${editedData.status === 're-test' ? 're-test' : ''}`}
                                          onClick={() => setEditedData({ ...editedData, progress: value })}
                                      >
                                      </div>
                                  ))}
                              </div>
                          </div>
                       </div>
                   ) : (
                        <div className="stepper-wrapper">
                           <div className="stepper-container">
                               <div className="stepper-track"></div>
                               <div className={`stepper-progress ${currentStatus === 're-test' ? 're-test' : ''}`} style={{ width: `${currentProgress}%` }}></div>
                               <div className="stepper-checkpoints">
                                   {[0, 25, 50, 75, 100].map((value) => (
                                       <div
                                           key={value}
                                           className={`stepper-checkpoint ${currentProgress >= value ? 'active' : ''} ${currentStatus === 're-test' ? 're-test' : ''}`}
                                       >
                                       </div>
                                   ))}
                               </div>
                           </div>
                        </div>
                   )}
                    </div>
                </div>
            )}
            {(isEditing ? editedData.status : scan.status) === "pending" && (
                <div className="px-6 pt-2 pb-5 bg-gradient-to-br from-muted/10 via-transparent to-transparent border-t border-border/30">
                    <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-950/20 dark:to-amber-950/20 border border-yellow-200 dark:border-yellow-900/30 text-yellow-700 dark:text-yellow-300 shadow-sm">
                        <div className="h-10 w-10 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center flex-shrink-0">
                            <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                        </div>
                        <div>
                            <p className="font-semibold text-sm">Pentest Queued</p>
                            <p className="text-sm opacity-90">This pentest is queued and will be processed shortly.</p>
                        </div>
                    </div>
                </div>
            )}
            {(isEditing ? editedData.status : scan.status) === "completed" && (
                <div className="px-6 pt-2 pb-6 bg-gradient-to-br from-muted/10 via-transparent to-transparent border-t border-border/30">
                    <div className="space-y-5">
                        <div className="flex items-center gap-3">
                            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-primary/10 to-accent/10 flex items-center justify-center">
                                <ShieldAlert className="h-4 w-4 text-primary/70" />
                            </div>
                            <h5 className="text-base font-semibold text-foreground">Vulnerabilities Found</h5>
                        </div>
                        <div className="flex flex-wrap items-center gap-3 max-w-full">
                            <div className="flex items-center gap-3 px-4 py-3 rounded-xl border bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-950/30 dark:to-gray-950/30 border-slate-200 dark:border-slate-800 text-slate-700 dark:text-slate-300 shadow-sm">
                                <div className="h-3 w-3 rounded-full bg-slate-500" />
                                <span className="text-sm font-semibold">{isEditing ? editedTotalVulnerabilities : displayTotalVulnerabilities} Total</span>
                            </div>
                            {[
                                { name: "critical", key: "criticalVulnerabilities", color: "red", dotClass: "status-dot-critical", gradient: "from-red-50 to-rose-50 dark:from-red-950/30 dark:to-rose-950/30", border: "border-red-200 dark:border-red-900/30", text: "text-red-700 dark:text-red-400" },
                                { name: "high", key: "highVulnerabilities", color: "orange", dotClass: "bg-orange-500", gradient: "from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30", border: "border-orange-200 dark:border-orange-900/30", text: "text-orange-700 dark:text-orange-400" },
                                { name: "medium", key: "mediumVulnerabilities", color: "yellow", dotClass: "bg-yellow-500", gradient: "from-yellow-50 to-amber-50 dark:from-yellow-950/30 dark:to-amber-950/30", border: "border-yellow-200 dark:border-yellow-900/30", text: "text-yellow-700 dark:text-yellow-400" },
                                { name: "low", key: "lowVulnerabilities", color: "blue", dotClass: "bg-blue-500", gradient: "from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30", border: "border-blue-200 dark:border-blue-900/30", text: "text-blue-700 dark:text-blue-400" },
                            ].map(vuln => (
                                <div key={vuln.name} className={cn(
                                    "flex items-center gap-3 px-4 py-3 rounded-xl border shadow-sm transition-all duration-200 hover:shadow-md",
                                    (isEditing ? editedData[vuln.key as keyof typeof editedData] : scan[vuln.key as keyof ScanType]) > 0
                                        ? `bg-gradient-to-r ${vuln.gradient} ${vuln.border} ${vuln.text}`
                                        : "bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-950/30 dark:to-gray-950/30 border-slate-200 dark:border-slate-800 text-slate-500 dark:text-slate-400"
                                )}>
                                    <div className={`h-3 w-3 rounded-full ${vuln.dotClass}`} />
                                    {isEditing && role === 'admin' ? (
                                        renderVulnerabilityCountEditor(vuln.name as any, vuln.key as any, vuln.color as any)
                                    ) : (
                                        <span className="text-sm font-semibold">{scan[vuln.key as keyof ScanType]} {vuln.name.charAt(0).toUpperCase() + vuln.name.slice(1)}</span>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            <CardContent
                className={cn(
                    "transition-all duration-500 ease-in-out overflow-hidden",
                    isExpanded ? "max-h-[2000px] opacity-100 py-4 px-6 border-t border-border/40" : "max-h-0 opacity-0 p-0 border-t-0"
                )}
                onClick={(e) => e.stopPropagation()}
            >
                {isExpanded && ( // Content only rendered when expanded for performance
                    <div className="space-y-5 animate-fade-in pb-2">
                        {(isEditing ? editedData.status : scan.status) === "completed" && (
                            <>
                                <div className="space-y-4">
                                    <div className="flex items-center gap-2">
                                        <h4 className="text-sm font-medium">Vulnerability Details</h4>
                                        <span className="font-medium text-xs bg-muted px-2 py-0.5 rounded-md">Total: {displayTotalVulnerabilities}</span>
                                    </div>
                                    {displayTotalVulnerabilities > 0 ? (
                                        <p className="text-sm text-muted-foreground">
                                            This scan detected {displayTotalVulnerabilities} vulnerabilities.
                                            {scan.criticalVulnerabilities > 0 && ` ${scan.criticalVulnerabilities} critical vulnerabilities require immediate attention.`}
                                            {scan.highVulnerabilities > 0 && ` ${scan.highVulnerabilities} high-risk vulnerabilities should be addressed soon.`}
                                        </p>
                                    ) : (
                                        <p className="text-sm text-green-600 dark:text-green-400">No vulnerabilities were detected in this scan.</p>
                                    )}
                                </div>
                                <Separator className="my-4" />
                            </>
                        )}

                        {displayFiles.length > 0 && (
                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <Paperclip className="h-4 w-4 text-muted-foreground" />
                                    <p className="font-medium text-sm">Attached Files:</p>
                                </div>
                                <div className="space-y-2 pl-2">
                                    {displayFiles.map((file, index) => (
                                        <div key={index} className="flex flex-col text-sm border rounded-md p-2 bg-muted/30">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2 overflow-hidden">
                                                    <FileText className="h-4 w-4 flex-shrink-0 text-primary" />
                                                    <span className="truncate font-medium" title={file.originalName}>{file.originalName}</span>
                                                </div>
                                                <Button asChild variant="ghost" size="icon" className="h-8 w-8">
                                                    <a href={file.url} target="_blank" rel="noopener noreferrer" title="Open file">
                                                        <ExternalLink className="h-4 w-4" />
                                                        <span className="sr-only">Open file</span>
                                                    </a>
                                                </Button>
                                            </div>
                                            <div className="flex items-center justify-between text-xs text-muted-foreground mt-1 pl-6">
                                                <div className="flex items-center gap-2">
                                                    {file.size && <span>{(file.size / 1024 / 1024).toFixed(1)} MB</span>}
                                                    {file.uploadedAt && <span>• Uploaded {new Date(file.uploadedAt).toLocaleDateString()}</span>}
                                                </div>
                                                {file.contentType && <span className="text-xs bg-muted rounded px-1.5 py-0.5">{file.contentType.split('/')[1]?.toUpperCase() || file.contentType}</span>}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <Separator className="my-3" />
                            </div>
                        )}

                        <div className="space-y-4 text-sm pb-4">
                            <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-primary" />
                                <h4 className="font-medium">Scan Information</h4>
                            </div>
                            <div className={`rounded-lg border bg-card/50 overflow-hidden shadow-xs card-hover ${getCardHoverClass()}`}>
                                {/* Simplified details display */}
                                {[
                                    { label: "Scan ID", value: scan.id, icon: FileText },
                                    { label: "Asset Type", value: formatAssetType(scan.asset_type), icon: ExternalLink },
                                    { label: "Platform", value: scan.platform || "N/A", icon: Paperclip },
                                    { label: "Environment", value: scan.environment || "N/A", icon: AlertCircle },
                                ].map(item => (
                                    <div key={item.label} className="grid grid-cols-[auto,1fr] items-center gap-2 p-3 border-b border-muted/20 last:border-0 bg-muted/50">
                                        <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                                            <item.icon className="h-3.5 w-3.5" />
                                            <span>{item.label}</span>
                                        </div>
                                        <div className="font-medium truncate text-right" title={String(item.value)}>{String(item.value)}</div>
                                    </div>
                                ))}
                                {/* Display other arbitrary fields */}
                                {Object.entries(scan).map(([key, value]) => {
                                    if (["id", "name", "status", "target", "platform", "environment", "requestedAt", "completedAt", "criticalVulnerabilities", "highVulnerabilities", "mediumVulnerabilities", "lowVulnerabilities", "resultFileUrl", "userId", "files", "aiSummary", "progress", "userEmail", "asset_type", "conversationTitle", "retestNotes"].includes(key) || typeof value === 'object' || value === null || value === undefined || value === '') {
                                        return null;
                                    }
                                    return (
                                        <div key={key} className="grid grid-cols-[auto,1fr] items-center gap-2 p-3 border-b border-muted/20 last:border-0 bg-muted/50">
                                            <div className="text-xs text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</div>
                                            <div className="font-medium truncate text-right" title={String(value)}>{String(value)}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>

            <CardFooter className={cn(
                "flex justify-between items-center pt-5 pb-6 px-6 gap-4 border-t border-border/40 relative z-10 bg-gradient-to-r from-card/50 to-card backdrop-blur-sm",
                isExpanded && "mt-2"
            )}>
                <Button
                    variant="outline" size="sm"
                    className="gap-2 flex-1 max-w-[140px] h-10 bg-gradient-to-r from-white/90 to-white/70 dark:from-black/90 dark:to-black/70 hover:bg-muted/20 transition-all duration-200 rounded-lg shadow-sm border-border/50 hover:border-primary/30"
                    onClick={(e) => { e.stopPropagation(); setIsExpanded(!isExpanded); }}
                >
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <span className="font-medium">{isExpanded ? "Hide Details" : "View Details"}</span>
                </Button>

                {(isEditing ? editedData.status : scan.status) === "re-test" && scan.retestNotes && scan.retestNotes.length > 0 && (
                    <div className="flex-1 text-right text-sm text-muted-foreground truncate">
                        <p className="truncate" title={scan.retestNotes.map(n => n.note).join(', ')}>
                            <span className="font-medium text-purple-600 dark:text-purple-400">Retest Notes:</span> {scan.retestNotes.length} vulnerabilities
                        </p>
                    </div>
                )}

                <div className="flex items-center gap-2.5 flex-1 justify-end flex-wrap">
                    {(isEditing ? editedData.status : scan.status) === "completed" && role === 'admin' && (displayFiles.length === 0 || isUploading || isDeletingReport) && (
                        <>
                            <Button asChild variant="outline" size="sm" disabled={isUploading || isDeletingReport} className="h-10 px-4 gap-2 rounded-xl bg-gradient-to-r from-white to-slate-50 dark:from-gray-800 dark:to-gray-700 hover:from-slate-50 hover:to-slate-100 dark:hover:from-gray-700 dark:hover:to-gray-600 transition-all duration-200 shadow-md border-slate-200 dark:border-gray-600">
                                <label htmlFor={`file-upload-${scan.id}`} className={cn("cursor-pointer flex items-center gap-2", (isUploading || isDeletingReport) && "cursor-not-allowed opacity-50")}>
                                    {isUploading || isDeletingReport ? <Loader2 className="h-4 w-4 animate-spin" /> : <Paperclip className="h-4 w-4" />}
                                    <span className="text-sm font-semibold">
                                        {isUploading ? `${uploadStage ? uploadStage.charAt(0).toUpperCase() + uploadStage.slice(1) + "..." : "Uploading..."}` : isDeletingReport ? "Deleting..." : "Upload Report"}
                                    </span>
                                </label>
                            </Button>
                            <input id={`file-upload-${scan.id}`} type="file" accept=".pdf,.json,.xml,.txt,.csv" className="sr-only" onChange={handleFileUpload} disabled={isUploading || isDeletingReport} />
                        </>
                    )}

                    {(isEditing ? editedData.status : scan.status) === "completed" && (role === 'client' || role === 'manager') && (
                        <Button variant="outline" size="sm" className="h-10 px-4 gap-2 rounded-xl bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/30 dark:to-violet-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700 hover:from-purple-100 hover:to-violet-100 dark:hover:from-purple-800/50 dark:hover:to-violet-800/50 shadow-md transition-all duration-200" onClick={handleRequestRetest} disabled={isSaving}>
                            {isSaving ? <><Loader2 className="h-4 w-4 animate-spin" /> <span className="text-sm font-semibold">Requesting...</span></> : <><RefreshCw className="h-4 w-4" /> <span className="text-sm font-semibold">Request Re-test</span></>}
                        </Button>
                    )}

                    {(isEditing ? editedData.status : scan.status) === "completed" && scan.resultFileUrl && !isUploading && !isDeletingReport && (
                        <div className="flex items-center gap-2.5">
                            {role === 'admin' && (
                                <>
                                    {deleteReportConfirmation ? (
                                        <div className="flex space-x-1 bg-background/90 backdrop-blur-sm rounded-full p-0.5 shadow-sm border">
                                            <Button variant="ghost" size="icon" className="h-7 w-7 text-green-500 hover:bg-green-500/10" onClick={(e) => { e.stopPropagation(); handleDeleteReportConfirm(); }} disabled={isDeletingReport}>
                                                <CheckCircle className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="icon" className="h-7 w-7 text-destructive hover:bg-destructive/10" onClick={(e) => { e.stopPropagation(); setDeleteReportConfirmation(false); }} disabled={isDeletingReport}>
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ) : (
                                        <Button variant="outline" size="icon" className="h-10 w-10 rounded-xl bg-gradient-to-r from-white to-slate-50 dark:from-gray-800 dark:to-gray-700 hover:text-destructive hover:from-red-50 hover:to-rose-50 dark:hover:from-red-900/20 dark:hover:to-rose-900/20 shadow-md border-slate-200 dark:border-gray-600 transition-all duration-200" onClick={(e) => { e.stopPropagation(); setDeleteReportConfirmation(true); }} disabled={isDeletingReport}>
                                            {isDeletingReport ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                                        </Button>
                                    )}
                                </>
                            )}
                            <Button
                                variant="default" size="sm"
                                className="h-10 px-4 gap-2 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-sm transition-all duration-200"
                                disabled={isLoadingAiSummary}
                                onClick={async () => {
                                    setIsReportModalOpen(true);
                                    await getAiSummary();
                                }}
                            >
                                {isLoadingAiSummary ? <Loader2 className="h-4 w-4 animate-spin" /> : <Eye className="h-4 w-4" />}
                                <span className="text-sm font-medium">{isLoadingAiSummary ? "Loading..." : "Review Report"}</span>
                            </Button>
                        </div>
                    )}
                </div>
            </CardFooter>

            {isReportModalOpen && (
                <Dialog open={isReportModalOpen} onOpenChange={(open) => !open && closeReportModal()}>
                    <DialogContent className="sm:max-w-5xl w-[95vw] max-h-[90svh] flex flex-col">
                        <DialogHeader>
                            <DialogTitle>Security Scan Report: {scan.target}</DialogTitle>
                            <DialogDescription>
                                AI-powered summary, findings, impacts, and recommendations.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-6 py-4 overflow-y-auto flex-grow"> {/* Added flex-grow */}
                            <div className="flex items-center justify-between sticky top-0 bg-background py-2 z-10"> {/* Sticky header for buttons */}
                                <h3 className="text-lg font-semibold">AI Generated Summary</h3>
                                <div className="flex items-center gap-2">
                                    {aiSummary && !isLoadingAiSummary && !aiSummaryError && (
                                        <Button variant="outline" size="sm" className="h-8 gap-1.5" onClick={() => {
                                            const textContent = `SECURITY SCAN SUMMARY - ${scan.target}\n...\n${aiSummary.summary}\n...`; // Simplified for brevity
                                            navigator.clipboard.writeText(textContent.trim())
                                                .then(() => toast({ title: "Copied to clipboard" }))
                                                .catch(() => toast({ title: "Copy failed", variant: "destructive" }));
                                        }}>
                                            <Copy className="h-3.5 w-3.5" /> <span className="text-xs">Copy</span>
                                        </Button>
                                    )}
                                    <Button asChild variant="default" size="sm" className="h-8 gap-1.5" disabled={!scan.resultFileUrl}>
                                        <a href={scan.resultFileUrl} target="_blank" rel="noopener noreferrer">
                                            <Download className="h-3.5 w-3.5" /> <span className="text-xs">Report</span>
                                        </a>
                                    </Button>
                                </div>
                            </div>

                            <div className="p-1 md:p-4 bg-muted/30 rounded-md border text-sm">
                                {isLoadingAiSummary && (
                                    <div className="flex flex-col items-center justify-center py-10 space-y-3">
                                        <div className="relative">
                                            <Brain className="absolute inset-0 m-auto h-8 w-8 text-primary/30" />
                                            <Loader2 className="h-16 w-16 animate-spin text-primary/20" />
                                        </div>
                                        <p className="text-sm text-muted-foreground animate-pulse">
                                            {aiSummaryStage ? `${aiSummaryStage.charAt(0).toUpperCase() + aiSummaryStage.slice(1).replace(/-/g, " ")}...` : "Analyzing..."}
                                        </p>
                                        <p className="text-xs text-muted-foreground/70 max-w-md text-center">
                                            Our AI is examining the report. This might take a moment.
                                        </p>
                                    </div>
                                )}
                                {!isLoadingAiSummary && aiSummaryError && (
                                    <div className="p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-900/30 rounded-md text-center">
                                        <p className="text-red-600 dark:text-red-400 font-medium mb-2">Unable to generate AI summary</p>
                                        <p className="text-red-500 dark:text-red-300 text-sm mb-3">Error: {aiSummaryError}</p>
                                        <Button variant="outline" size="sm" onClick={getAiSummary} disabled={isLoadingAiSummary}>
                                            {isLoadingAiSummary ? <Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" /> : <RefreshCw className="h-3.5 w-3.5 mr-2" />}
                                            Retry Analysis
                                        </Button>
                                    </div>
                                )}
                                {!isLoadingAiSummary && !aiSummaryError && aiSummary && (
                                    <div className="space-y-5">
                                        {[
                                            { title: "Overall Summary", data: aiSummary.summary, icon: FileText, color: "blue", type: "paragraph" },
                                            { title: "Key Findings", data: aiSummary.keyFindings, icon: Search, color: "amber", type: "list-finding" },
                                            { title: "Potential Impacts", data: aiSummary.potentialImpacts, icon: AlertTriangle, color: "red", type: "list-impact" },
                                            { title: "Recommendations", data: aiSummary.recommendations, icon: CheckCircle, color: "green", type: "list-recommendation" },
                                            { title: "Raw Analysis", data: aiSummary.rawAnalysis, icon: FileCode, color: "gray", type: "pre" }
                                        ].map(section => {
                                            if (!section.data || (Array.isArray(section.data) && section.data.length === 0)) return null;
                                            return (
                                                <div key={section.title} className={`bg-${section.color}-50 dark:bg-${section.color}-950/30 p-3 md:p-4 rounded-md border border-${section.color}-200 dark:border-${section.color}-900/30`}>
                                                    <h4 className={`font-semibold text-${section.color}-800 dark:text-${section.color}-300 mb-2 flex items-center gap-2`}>
                                                        <section.icon className="h-4 w-4" /> {section.title}
                                                    </h4>
                                                    {section.type === "paragraph" && <p className="text-base whitespace-pre-line">{section.data as string}</p>}
                                                    {section.type === "pre" && <pre className="whitespace-pre-wrap text-xs">{section.data as string}</pre>}
                                                    {section.type === "list-finding" && Array.isArray(section.data) && (
                                                        <ul className="pl-0 space-y-2.5">
                                                            {(section.data as Finding[]).map((item, idx) => (
                                                                <li key={idx} className="flex items-start gap-2.5">
                                                                    <Badge variant="secondary" className={`bg-${item.severity === 'critical' ? 'red' : item.severity === 'high' ? 'orange' : item.severity === 'medium' ? 'amber' : 'blue'}-500 text-white hover:bg-${item.severity === 'critical' ? 'red' : item.severity === 'high' ? 'orange' : item.severity === 'medium' ? 'amber' : 'blue'}-600 text-xs`}>{item.severity.toUpperCase()}</Badge>
                                                                    <div className={`text-${section.color}-700 dark:text-${section.color}-400 text-sm`}>{item.finding}</div>
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    )}
                                                    {section.type === "list-recommendation" && Array.isArray(section.data) && (
                                                        <ul className="pl-0 space-y-2.5">
                                                            {(section.data as Recommendation[]).map((item, idx) => (
                                                                <li key={idx} className="flex items-start gap-2.5">
                                                                    <Badge variant="secondary" className={`bg-${item.priority === 'immediate' ? 'red' : item.priority === 'high' ? 'orange' : item.priority === 'medium' ? 'amber' : 'blue'}-500 text-white hover:bg-${item.priority === 'immediate' ? 'red' : item.priority === 'high' ? 'orange' : item.priority === 'medium' ? 'amber' : 'blue'}-600 text-xs`}>{item.priority.toUpperCase()}</Badge>
                                                                    <div className={`text-${section.color}-700 dark:text-${section.color}-400 text-sm`}>{item.recommendation}</div>
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    )}
                                                    {section.type === "list-impact" && Array.isArray(section.data) && (
                                                        <ul className="list-disc pl-5 space-y-1">
                                                            {(section.data as string[]).map((item, idx) => (
                                                                <li key={idx} className={`text-${section.color}-700 dark:text-${section.color}-400 text-sm`}>{item}</li>
                                                            ))}
                                                        </ul>
                                                    )}
                                                </div>
                                            );
                                        })}
                                        <p className="text-xs text-muted-foreground mt-4 border-t pt-3">AI summary. Download full report for complete details.</p>
                                    </div>
                                )}
                                {/* Fallback if no AI summary and not loading/error */}
                                {!isLoadingAiSummary && !aiSummaryError && !aiSummary && (
                                    <div className="text-center py-10">
                                        <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                                        <p className="text-muted-foreground">No AI summary available for this report yet.</p>
                                        <p className="text-xs text-muted-foreground mt-1">You can try generating one or download the full report.</p>
                                        <Button variant="default" size="sm" onClick={getAiSummary} className="mt-4" disabled={isLoadingAiSummary}>
                                            {isLoadingAiSummary ? <Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" /> : <Brain className="h-3.5 w-3.5 mr-2" />}
                                            Generate AI Summary
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* DialogFooter can be added here if needed, but buttons are at top */}
                    </DialogContent>
                </Dialog>
            )}
        </Card>
    );
}