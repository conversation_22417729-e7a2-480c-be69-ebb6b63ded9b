"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/hooks/useAuth"
import { useTheme } from "next-themes"
import { toast } from "sonner"
import { useSearchParams } from "next/navigation"
import { updateProfile } from "firebase/auth"
import { auth } from "@/lib/firebase"
import {
  User,
  Bell,
  Shield,
  Palette,
  Key,
  Save,
  Loader2,
  Settings
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

export function SettingsComponent() {
  const { user, role } = useAuth()
  const { theme, setTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("profile")

  // Get the tab from URL parameters
  useEffect(() => {
    if (searchParams) {
      const tabParam = searchParams.get("tab")
      if (tabParam && ["profile", "notifications", "appearance", "security"].includes(tabParam)) {
        setActiveTab(tabParam)
      }
    }
  }, [searchParams])

  // Profile settings
  const [displayName, setDisplayName] = useState(user?.displayName || "")
  const [email, setEmail] = useState(user?.email || "")

  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [scanCompletedNotifications, setScanCompletedNotifications] = useState(true)
  const [messageNotifications, setMessageNotifications] = useState(true)
  const [notificationsLoading, setNotificationsLoading] = useState(true)

  // Appearance settings
  const [themePreference, setThemePreference] = useState(theme || "system")

  // Security settings

  const handleSaveProfile = async () => {
    setLoading(true)
    try {
      // Get the current user
      const currentUser = auth.currentUser;

      if (!currentUser) {
        throw new Error("No user is currently signed in");
      }

      // Update the display name
      await updateProfile(currentUser, {
        displayName: displayName
      });

      // Force refresh the user object in the auth context
      // This will update the display name in the UI
      if (typeof window !== 'undefined') {
        // Force a reload of the page to update the user info everywhere
        window.location.reload();
      }

      toast.success("Profile settings saved successfully");
    } catch (error) {
      toast.error("Failed to save profile settings");
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  // Load user notification preferences
  useEffect(() => {
    const loadNotificationPreferences = async () => {
      if (!user) return;

      setNotificationsLoading(true);
      try {
        const idToken = await user.getIdToken();

        const response = await fetch('/api/user-preferences', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${idToken}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          setEmailNotifications(data.emailNotifications);
          setScanCompletedNotifications(data.scanCompletedNotifications);
          setMessageNotifications(data.messageNotifications);
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error);
      } finally {
        setNotificationsLoading(false);
      }
    };

    loadNotificationPreferences();
  }, [user]);

  const handleSaveNotifications = async () => {
    setLoading(true)
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const idToken = await user.getIdToken();

      const response = await fetch('/api/user-preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({
          emailNotifications,
          scanCompletedNotifications,
          messageNotifications
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save notification settings');
      }

      toast.success("Notification settings saved successfully")
    } catch (error: any) {
      toast.error(error.message || "Failed to save notification settings")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveAppearance = async () => {
    setLoading(true)
    try {
      setTheme(themePreference)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success("Appearance settings saved successfully")
    } catch (error) {
      toast.error("Failed to save appearance settings")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSecurity = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success("Security settings saved successfully")
    } catch (error) {
      toast.error("Failed to save security settings")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Settings className="h-6 w-6 text-primary" />
          Settings
        </h2>
        <p className="text-muted-foreground text-md mt-1">
          Manage your account settings and preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-3xl">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="appearance" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span className="hidden sm:inline">Appearance</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
        </TabsList>

        {/* Profile Settings */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile</CardTitle>
              <CardDescription>
                Manage your profile information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Your display name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Account Role</Label>
                <div className="flex items-center gap-2">
                  <div className="rounded-md bg-primary/10 px-2.5 py-1.5 text-sm font-medium text-primary">
                    {role === 'admin' ? 'Administrator' :
                     role === 'manager' ? 'Manager' : 'Client User'}
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Your account role determines your permissions and access level.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveProfile} disabled={loading} className="cursor-pointer">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notifications</CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="emailNotifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  id="emailNotifications"
                  checked={emailNotifications}
                  onCheckedChange={setEmailNotifications}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="scanCompletedNotifications">Scan Completed</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when a scan is completed
                  </p>
                </div>
                <Switch
                  id="scanCompletedNotifications"
                  checked={scanCompletedNotifications}
                  onCheckedChange={setScanCompletedNotifications}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="messageNotifications">New Messages</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when you receive new messages
                  </p>
                </div>
                <Switch
                  id="messageNotifications"
                  checked={messageNotifications}
                  onCheckedChange={setMessageNotifications}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveNotifications} disabled={loading} className="cursor-pointer">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Appearance Settings */}
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>
                Customize the look and feel of the application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="theme">Theme</Label>
                <Select value={themePreference} onValueChange={setThemePreference}>
                  <SelectTrigger id="theme" className="w-full">
                    <SelectValue placeholder="Select a theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose between light, dark, or system theme.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveAppearance} disabled={loading} className="cursor-pointer">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security</CardTitle>
              <CardDescription>
                Manage your account security settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="changePassword">Change Password</Label>
                <div className="flex gap-2">
                  <Button variant="outline" className="cursor-pointer">
                    <Key className="mr-2 h-4 w-4" />
                    Change Password
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSecurity} disabled={loading} className="cursor-pointer">
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
