import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
    Clock, Download, ShieldAlert, CheckCircle2, AlertCircle,
    Trash2, Loader2,
    FileText, Paperclip, ExternalLink,
    Edit, Plus, Minus, Eye,
    Brain, Search, AlertTriangle, CheckCircle, FileCode, Copy,
    RefreshCw
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatAssetType } from "@/lib/utils/formatAssetType"
import "@/styles/progress-slider.css"
import "@/styles/progress-stepper.css"
import "@/styles/enhanced-scan-design.css"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import React, { useState } from "react"
import { useAuth } from "@/hooks/useAuth"
import { useScans } from "@/context/ScanContext"
import { toast } from "@/components/ui/use-toast";

// File information type
type FileInfo = {
    url: string;
    originalName: string;
    uploadedAt: string;
    size?: number;
    contentType?: string;
}

// Define the types for AI summary
type Severity = "critical" | "high" | "medium" | "low";
type Priority = "immediate" | "high" | "medium" | "low";

type Finding = {
    finding: string;
    severity: Severity | string;
};

type Recommendation = {
    recommendation: string;
    priority: Priority | string;
};

type AISummaryData = {
    summary: string;
    keyFindings: Finding[];
    potentialImpacts: string[];
    recommendations: Recommendation[];
    rawAnalysis?: string;
};

type ScanType = {
    id: string
    name: string
    status: "pending" | "in-progress" | "completed" | "re-test"
    target: string
    requestedAt: Date
    completedAt?: Date
    criticalVulnerabilities: number
    highVulnerabilities: number
    mediumVulnerabilities: number
    lowVulnerabilities: number
    resultFileUrl?: string;
    userId?: string;
    files?: FileInfo[];
    aiSummary?: AISummaryData;
    [key: string]: any;
}

interface ScanCardProps {
    scan: ScanType;
    onStatusChange?: () => void;
}

export function ScanCardModal({ scan, onStatusChange }: ScanCardProps) {
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadStage, setUploadStage] = useState<string | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isDeletingReport, setIsDeletingReport] = useState(false);
    const [deleteConfirmationId, setDeleteConfirmationId] = useState<string | null>(null);
    const [deleteReportConfirmation, setDeleteReportConfirmation] = useState(false);
    const [animateStatus, setAnimateStatus] = useState<string | null>(null);
    const [isReportModalOpen, setIsReportModalOpen] = useState(false);

    // AI Summary state
    type AISummaryType = AISummaryData | null;
    const [aiSummary, setAiSummary] = useState<AISummaryType>(null);
    const [isLoadingAiSummary, setIsLoadingAiSummary] = useState(false);
    const [aiSummaryError, setAiSummaryError] = useState<string | null>(null);
    const [aiSummaryStage, setAiSummaryStage] = useState<string | null>(null);

    // Unified edit mode
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Combined edited data
    const [editedData, setEditedData] = useState({
        status: scan.status,
        criticalVulnerabilities: scan.criticalVulnerabilities,
        highVulnerabilities: scan.highVulnerabilities,
        mediumVulnerabilities: scan.mediumVulnerabilities,
        lowVulnerabilities: scan.lowVulnerabilities,
        progress: scan.progress || 0
    });

    const { user, role } = useAuth();
    const { deleteScan, updateScanLocally, fetchData } = useScans();

    const totalVulnerabilities =
        scan.criticalVulnerabilities + scan.highVulnerabilities + scan.mediumVulnerabilities + scan.lowVulnerabilities;

    // Ensure scan.files is always an array
    const ensureFilesArray = () => {
        if (!scan.files) {
            if (scan.resultFileUrl) {
                const defaultFile = {
                    originalName: "Report File",
                    size: 0,
                    contentType: "application/pdf",
                    uploadedAt: new Date().toISOString(),
                    url: scan.resultFileUrl
                };
                updateScanLocally(scan.id, { files: [defaultFile] });
                return [defaultFile];
            } else {
                updateScanLocally(scan.id, { files: [] });
                return [];
            }
        }
        return scan.files;
    };

    // Call this function once when the component mounts
    React.useEffect(() => {
        if (scan.status === "completed" && scan.resultFileUrl) {
            ensureFilesArray();
        }
    }, [scan.id, scan.resultFileUrl, scan.status]);

    // Helper functions for status display
    const getStatusIcon = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "pending": return <Clock className="h-4 w-4 text-yellow-500" />;
            case "in-progress": return <AlertCircle className="h-4 w-4 text-blue-500" />;
            case "re-test": return <RefreshCw className="h-4 w-4 text-purple-500" />;
            case "completed": return <CheckCircle2 className="h-4 w-4 text-green-500" />;
        }
    }

    const getStatusText = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "pending": return "Pending";
            case "in-progress": return "In Progress";
            case "re-test": return "Re-test";
            case "completed": return "Completed";
        }
    }

    const getStatusColor = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "pending": return "text-yellow-500 bg-yellow-500/10";
            case "in-progress": return "text-blue-500 bg-blue-500/10";
            case "re-test": return "text-purple-500 bg-purple-500/10";
            case "completed": return "text-green-500 bg-green-500/10";
        }
    }

    const getCardClass = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "pending": return "scan-card-pending";
            case "in-progress": return "scan-card-in-progress";
            case "re-test": return "scan-card-re-test";
            case "completed": return "scan-card-completed";
        }
    }

    const getProgressBgColor = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "in-progress": return "bg-gray-200 dark:bg-gray-700";
            case "re-test": return "bg-gray-200 dark:bg-gray-700";
            default: return "bg-muted";
        }
    };

    const getProgressFillColor = () => {
        const status = isEditing ? editedData.status : scan.status;
        switch (status) {
            case "in-progress": return "bg-blue-600";
            case "re-test": return "bg-purple-600";
            default: return "bg-blue-500";
        }
    };

    // Function to get AI summary of the report
    const getAiSummary = async () => {
        // Only proceed if there's a report URL
        if (!scan.resultFileUrl) {
            setAiSummaryError("No report file available for analysis");
            return;
        }

        try {
            setIsLoadingAiSummary(true);
            setAiSummaryError(null);
            setAiSummaryStage("initializing");

            // Get the authentication token
            const idToken = await user?.getIdToken();
            if (!idToken) {
                throw new Error("Authentication token not available");
            }

            // First check if the scan already has an AI summary stored
            if (scan.aiSummary) {
                console.log("Using pre-generated AI summary from database");
                setAiSummaryStage("loading-existing");

                // Convert the stored summary to the correct type
                const aiSummaryData = scan.aiSummary as AISummaryData;

                const typedSummary: AISummaryType = {
                    summary: aiSummaryData.summary,
                    keyFindings: aiSummaryData.keyFindings.map((finding: any) => ({
                        finding: finding.finding,
                        severity: finding.severity as Severity
                    })),
                    potentialImpacts: aiSummaryData.potentialImpacts,
                    recommendations: aiSummaryData.recommendations.map((rec: any) => ({
                        recommendation: rec.recommendation,
                        priority: rec.priority as Priority
                    })),
                    rawAnalysis: aiSummaryData.rawAnalysis
                };
                setAiSummary(typedSummary);
                setIsLoadingAiSummary(false);
                setAiSummaryStage(null);
                return;
            }

            console.log("No pre-generated summary found, generating new summary");
            setAiSummaryStage("downloading-report");

            // Make API call to backend service that will:
            // 1. Download the PDF from the resultFileUrl
            // 2. Send it to OpenAI for analysis
            // 3. Return the AI-generated summary
            const response = await fetch('/api/analyze-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`,
                },
                body: JSON.stringify({
                    reportUrl: scan.resultFileUrl,
                    scanId: scan.id,
                    target: scan.target,
                    vulnerabilities: {
                        critical: scan.criticalVulnerabilities,
                        high: scan.highVulnerabilities,
                        medium: scan.mediumVulnerabilities,
                        low: scan.lowVulnerabilities,
                    }
                }),
            });

            setAiSummaryStage("analyzing-report");

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to analyze report (${response.status})`);
            }

            setAiSummaryStage("processing-results");
            const data = await response.json();
            const summary = data.summary;
            setAiSummary(summary);

            // Update the local scan data with the AI summary
            updateScanLocally(scan.id, { aiSummary: summary });
            setAiSummaryStage("completed");

        } catch (error: any) {
            console.error("Error getting AI summary:", error);
            setAiSummaryError(error.message || "Failed to generate AI summary");
            setAiSummaryStage("error");
        } finally {
            setIsLoadingAiSummary(false);
        }
    };

    // Function to close the report modal
    const closeReportModal = () => {
        setIsReportModalOpen(false);
    };

    // Function to get animation class
    const getAnimationClass = () => {
        if (!animateStatus) return "";

        switch (animateStatus) {
            case "pending": return "animate-status-pending";
            case "in-progress": return "animate-status-in-progress";
            case "re-test": return "animate-status-re-test";
            case "completed": return "animate-status-completed";
            default: return "";
        }
    };

    // Handle file upload
    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file: File | undefined = event.target.files?.[0];
        if (!file || isUploading) return;

        setIsUploading(true);
        setUploadStage("preparing");
        console.log(`Uploading file for scan ${scan.id}`);

        try {
            // Get authentication token
            setUploadStage("authenticating");
            const idToken = await user?.getIdToken();
            if (!idToken) {
                throw new Error("Authentication token not available.");
            }

            // Prepare form data
            setUploadStage("preparing-upload");
            const formData = new FormData();
            formData.append('file', file);
            formData.append('scanId', scan.id);
            formData.append('replaceExisting', 'true'); // Signal to replace existing files

            // Upload the file
            setUploadStage("uploading");

            // Set a timeout to handle cases where the upload hangs
            const uploadTimeout = setTimeout(() => {
                console.error("Upload timed out after 3 minutes");
                setUploadStage("error");
                toast({
                    title: "Upload Timeout",
                    description: "The upload is taking longer than expected. Please try again with a smaller file or contact support.",
                    variant: "destructive"
                });
                setIsUploading(false);
            }, 180000); // 3 minutes timeout

            const response = await fetch('/api/upload-scan-result', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${idToken}`,
                    // Content-Type is set automatically for FormData
                },
                body: formData,
            });

            // Clear the timeout since we got a response
            clearTimeout(uploadTimeout);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Failed to upload file (${response.status})`);
            }

            // Process the response
            setUploadStage("processing-response");
            // Get the actual file data from the response
            const fileData = await response.json();

            // Update the scan with the actual file data from the server
            if (fileData.fileUrl) {
                setUploadStage("updating-scan-data");

                // Create a new file object with the server URL
                const serverFile = {
                    originalName: file.name,
                    size: file.size,
                    contentType: file.type,
                    uploadedAt: new Date().toISOString(),
                    url: fileData.fileUrl
                };

                // Update the scan locally with file info, vulnerability counts, and AI summary if available
                const updates: Partial<typeof scan> = {
                    files: [serverFile],
                    resultFileUrl: fileData.fileUrl
                };

                // If vulnerability counts are returned from the API, update them locally
                if (fileData.vulnerabilityCounts) {
                    setUploadStage("updating-vulnerability-counts");
                    updates.criticalVulnerabilities = fileData.vulnerabilityCounts.critical;
                    updates.highVulnerabilities = fileData.vulnerabilityCounts.high;
                    updates.mediumVulnerabilities = fileData.vulnerabilityCounts.medium;
                    updates.lowVulnerabilities = fileData.vulnerabilityCounts.low;
                }

                // If AI summary is returned from the API, update it locally
                if (fileData.aiSummary) {
                    setUploadStage("updating-ai-summary");
                    console.log("AI summary received from upload response and stored locally");
                    updates.aiSummary = fileData.aiSummary;
                }

                // If either vulnerability counts or AI summary aren't returned, fetch the updated scan data
                if (!fileData.vulnerabilityCounts || !fileData.aiSummary) {
                    setUploadStage("fetching-latest-data");
                    console.log("Fetching complete scan data to ensure we have the latest AI summary");
                    await fetchData('organization');
                } else {
                    // Update the scan locally with all the updates
                    setUploadStage("finalizing");
                    updateScanLocally(scan.id, updates);
                }
            }

            setUploadStage("completed");

            toast({
                title: "Success",
                description: "Result file uploaded successfully. Previous files have been replaced."
            });
        } catch (error: any) {
            console.error("Error uploading file:", error);
            setUploadStage("error");
            toast({
                title: "Error",
                description: error.message || "Could not upload result file.",
                variant: "destructive"
            });

            // Revert the local update in case of error
            updateScanLocally(scan.id, { files: scan.files || [] });
        } finally {
            // Reset file input value so the same file can be selected again if needed
            if (event.target) {
                event.target.value = '';
            }

            // Add a small delay before clearing the upload state
            // This ensures users can see the "Completed" or "Error" state
            setTimeout(() => {
                setIsUploading(false);
                setUploadStage(null);
            }, 1000);
        }
    };

    return (
        <Card
            className={cn(
                "w-full transition-all duration-300 glass-effect shadow-sm hover:shadow-md mb-6 scan-card",
                getCardClass(),
                getAnimationClass(),
                (isDeleting) && "opacity-50"
            )}
        >
            <CardHeader className="pb-4 pt-5 px-6">
                <div className="flex items-start justify-between">
                    <div className="space-y-3">
                        <div className="flex flex-wrap items-center gap-3">
                            <Badge
                                variant="outline"
                                className={cn(
                                    "flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full transition-all duration-200",
                                    getStatusColor(),
                                    animateStatus && "status-badge-animation"
                                )}
                            >
                                {getStatusIcon()}
                                {getStatusText()}
                            </Badge>

                            {scan.files && scan.files.length > 0 && (
                                <Badge
                                    variant="outline"
                                    className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 hover:bg-slate-100 dark:hover:bg-slate-900/50 transition-colors"
                                    asChild
                                >
                                    <a
                                        href={scan.resultFileUrl || scan.files[0].url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        title="View attached files"
                                        className="flex items-center gap-1.5"
                                    >
                                        <Paperclip className="h-3.5 w-3.5" />
                                        <span>{scan.files.length} File{scan.files.length !== 1 ? 's' : ''}</span>
                                    </a>
                                </Badge>
                            )}
                        </div>
                        <CardTitle className="text-sm font-semibold leading-tight tracking-tight">
                            <span className="text-lg font-semibold leading-none tracking-tight">
                                {scan.conversationTitle && scan.conversationTitle !== "Pentest Scan Request"
                                    ? scan.conversationTitle
                                    : scan.asset_type
                                        ? `${formatAssetType(scan.asset_type)} Pentest`
                                        : `${scan.target} Pentest`}
                            </span>
                        </CardTitle>
                    </div>
                    {role === 'admin' && (
                        <div className="flex items-center gap-2.5">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-9 w-9 p-0 rounded-full cursor-pointer bg-white/90 dark:bg-black/90 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors shadow-sm"
                                onClick={() => setIsEditing(true)}
                                disabled={isDeleting}
                            >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">Edit pentest</span>
                            </Button>
                            {deleteConfirmationId === scan.id ? (
                                <div className="flex space-x-1 bg-background/90 backdrop-blur-sm rounded-full p-0.5 shadow-sm border">
                                    <button
                                        type="button"
                                        className="p-1.5 text-green-500 rounded-full hover:bg-green-500/10 cursor-pointer transition-colors"
                                        onClick={async (e) => {
                                            e.preventDefault();
                                            e.stopPropagation();

                                            // Handle delete confirmation
                                            setIsDeleting(true);
                                            try {
                                                // Use context function and get the response data
                                                const response = await deleteScan(scan.id);

                                                // Create a more informative success message
                                                let description = `Scan for "${scan.target}" deleted successfully.`;

                                                // Add information about deleted files if available
                                                if (response && 'filesDeleted' in response && response.filesDeleted > 0) {
                                                    description += ` ${response.filesDeleted} associated file${response.filesDeleted > 1 ? 's were' : ' was'} also deleted.`;
                                                }

                                                // Add information about deleted vulnerabilities if any
                                                if (response && 'vulnerabilitiesDeleted' in response && response.vulnerabilitiesDeleted !== undefined && response.vulnerabilitiesDeleted > 0) {
                                                    description += ` ${response.vulnerabilitiesDeleted} vulnerability card${response.vulnerabilitiesDeleted > 1 ? 's were' : ' was'} also removed.`;
                                                }

                                                toast({
                                                    title: "Success",
                                                    description: description
                                                });

                                                // Call onStatusChange if provided
                                                if (onStatusChange) {
                                                    onStatusChange();
                                                }
                                            } catch (error: any) {
                                                console.error(`Error deleting scan ${scan.id}:`, error);
                                                toast({
                                                    title: "Error",
                                                    description: error.message || `Could not delete scan.`,
                                                    variant: "destructive"
                                                });
                                            } finally {
                                                setIsDeleting(false);
                                                setDeleteConfirmationId(null);
                                            }
                                        }}
                                        disabled={isDeleting}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                        <span className="sr-only">Confirm delete</span>
                                    </button>
                                    <button
                                        type="button"
                                        className="p-1.5 text-destructive rounded-full hover:bg-destructive/10 cursor-pointer transition-colors"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            setDeleteConfirmationId(null);
                                        }}
                                        disabled={isDeleting}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                        <span className="sr-only">Cancel delete</span>
                                    </button>
                                </div>
                            ) : (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-9 w-9 p-0 rounded-full cursor-pointer bg-white/90 dark:bg-black/90 hover:text-destructive transition-colors hover:bg-destructive/10 shadow-sm"
                                    onClick={() => setDeleteConfirmationId(scan.id)}
                                    disabled={isDeleting || isEditing}
                                >
                                    {isDeleting ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                                    <span className="sr-only">Delete pentest</span>
                                </Button>
                            )}
                        </div>
                    )}
                </div>
                <CardDescription className="mt-4">
                    <div className="flex flex-wrap items-center gap-x-6 gap-y-2.5 text-xs">
                        <div className="flex items-center gap-2 text-muted-foreground/90">
                            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                            <span>Requested {new Date(scan.requestedAt).toLocaleString(undefined, {
                                year: 'numeric',
                                month: 'numeric',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}</span>
                        </div>
                        {scan.completedAt && (
                            <div className="flex items-center gap-2 text-muted-foreground/90">
                                <CheckCircle2 className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>Completed {new Date(scan.completedAt).toLocaleString(undefined, {
                                    year: 'numeric',
                                    month: 'numeric',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}</span>
                            </div>
                        )}
                    </div>
                </CardDescription>
            </CardHeader>

            {/* Status-specific content (always visible) */}
            {scan.status === "in-progress" && (
                <div className="px-6 pt-2 pb-4 space-y-3">
                    <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center gap-2">
                            <div className="relative h-2.5 w-2.5">
                                <div className="absolute h-full w-full rounded-full status-dot-in-progress animate-pulse-custom opacity-75"></div>
                                <div className="relative h-2.5 w-2.5 rounded-full status-dot-in-progress"></div>
                            </div>
                            <span className="font-medium">Pentest in Progress</span>
                        </span>
                        <span className="font-medium text-blue-600 dark:text-blue-400">
                            {scan.progress || 0}%
                        </span>
                    </div>
                   <div className="stepper-container">
                       <div className="stepper-track"></div>
                       <div className="stepper-progress" style={{ width: `${scan.progress || 0}%` }}></div>
                       <div className="stepper-checkpoints">
                           {[0, 25, 50, 75, 100].map((value) => (
                               <div
                                   key={value}
                                   className={`stepper-checkpoint ${(scan.progress || 0) >= value ? 'active' : ''}`}
                               >
                               </div>
                           ))}
                       </div>
                   </div>
                </div>
            )}
            {scan.status === "pending" && (
                <div className="px-6 pt-2 pb-4">
                    <div className="flex items-center gap-3 p-3 rounded-md bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-100 dark:border-yellow-900/30 text-sm text-yellow-700 dark:text-yellow-300">
                        <div className="flex-shrink-0">
                            <Clock className="h-4 w-4 text-yellow-500" />
                        </div>
                        <span>This pentest is queued and will be processed shortly.</span>
                    </div>
                </div>
            )}
            {scan.status === "re-test" && (
                <div className="px-6 pt-2 pb-4 space-y-3">
                    <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center gap-2">
                            <div className="relative h-2.5 w-2.5">
                                <div className="absolute h-full w-full rounded-full status-dot-re-test animate-pulse-custom opacity-75"></div>
                                <div className="relative h-2.5 w-2.5 rounded-full status-dot-re-test"></div>
                            </div>
                            <span className="font-medium">Re-testing in Progress</span>
                        </span>
                        <span className="font-medium text-purple-600 dark:text-purple-400">
                            {scan.progress || 0}%
                        </span>
                    </div>
                   <div className="stepper-container">
                       <div className="stepper-track"></div>
                       <div className="stepper-progress re-test" style={{ width: `${scan.progress || 0}%` }}></div>
                       <div className="stepper-checkpoints">
                           {[0, 25, 50, 75, 100].map((value) => (
                               <div
                                   key={value}
                                   className={`stepper-checkpoint ${(scan.progress || 0) >= value ? 'active' : ''} re-test`}
                               >
                               </div>
                           ))}
                       </div>
                   </div>
                </div>
            )}
            {/* Vulnerability Summary for completed scans - always visible */}
            {scan.status === "completed" && (
                <div className="px-6 pt-2 pb-5">
                    <div className="space-y-4">
                        <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                                <h4 className="text-sm font-medium flex items-center gap-1.5">
                                    <ShieldAlert className="h-4 w-4 text-muted-foreground" />
                                    Vulnerabilities Found
                                </h4>
                            </div>
                        </div>

                        <div className="flex flex-wrap items-center gap-3">
                            {/* Total count */}
                            <div className="flex items-center gap-2 px-3 py-2 rounded-md border bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-700 dark:text-slate-300 shadow-sm">
                                <div className="h-2.5 w-2.5 rounded-full bg-slate-500" />
                                <span className="text-xs font-medium">
                                    {totalVulnerabilities} Total
                                </span>
                            </div>
                            {/* Critical */}
                            <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-md border shadow-sm transition-colors duration-200",
                                scan.criticalVulnerabilities > 0
                                    ? "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-900/30 text-red-700 dark:text-red-400"
                                    : "bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-500 dark:text-slate-400"
                            )}>
                                <div className="h-2.5 w-2.5 rounded-full status-dot-critical" />
                                <span className="text-xs font-medium">{scan.criticalVulnerabilities} Critical</span>
                            </div>
                            {/* High */}
                            <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-md border shadow-sm transition-colors duration-200",
                                scan.highVulnerabilities > 0
                                    ? "bg-orange-50 dark:bg-orange-950/30 border-orange-200 dark:border-orange-900/30 text-orange-700 dark:text-orange-400"
                                    : "bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-500 dark:text-slate-400"
                            )}>
                                <div className="h-2.5 w-2.5 rounded-full bg-orange-500" />
                                <span className="text-xs font-medium">{scan.highVulnerabilities} High</span>
                            </div>
                            {/* Medium */}
                            <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-md border shadow-sm transition-colors duration-200",
                                scan.mediumVulnerabilities > 0
                                    ? "bg-yellow-50 dark:bg-yellow-950/30 border-yellow-200 dark:border-yellow-900/30 text-yellow-700 dark:text-yellow-400"
                                    : "bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-500 dark:text-slate-400"
                            )}>
                                <div className="h-2.5 w-2.5 rounded-full bg-yellow-500" />
                                <span className="text-xs font-medium">{scan.mediumVulnerabilities} Medium</span>
                            </div>
                            {/* Low */}
                            <div className={cn(
                                "flex items-center gap-2 px-3 py-2 rounded-md border shadow-sm transition-colors duration-200",
                                scan.lowVulnerabilities > 0
                                    ? "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-900/30 text-blue-700 dark:text-blue-400"
                                    : "bg-slate-50 dark:bg-slate-950/30 border-slate-200 dark:border-slate-800 text-slate-500 dark:text-slate-400"
                            )}>
                                <div className="h-2.5 w-2.5 rounded-full bg-blue-500" />
                                <span className="text-xs font-medium">{scan.lowVulnerabilities} Low</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Footer Actions */}
            <CardFooter className="flex justify-between pt-4 pb-5 px-6 gap-4 border-t border-border/40 relative z-10">
                <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 flex-1 max-w-[140px] h-9 cursor-pointer bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-colors rounded-md shadow-sm"
                    onClick={() => setIsDetailsModalOpen(true)}
                >
                    <Search className="h-3.5 w-3.5" />
                    <span>View Details</span>
                </Button>

                <div className="flex items-center gap-4 flex-1 justify-end">
                    {/* Admin: Upload Button - Show if no report files exist or if currently uploading/deleting */}
                    {scan.status === "completed" && role === 'admin' && ((!scan.resultFileUrl || !scan.files || scan.files.length === 0) || isUploading || isDeletingReport) && (
                        <>
                            <Button
                                asChild
                                variant="outline"
                                size="sm"
                                disabled={isUploading || isDeletingReport}
                                className="h-9 px-4 gap-2 rounded-md bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-colors shadow-sm"
                            >
                                <label
                                    htmlFor={`file-upload-${scan.id}`}
                                    className={cn(
                                        "cursor-pointer flex items-center gap-2",
                                        (isUploading || isDeletingReport) && "cursor-not-allowed opacity-50"
                                    )}
                                >
                                    {isUploading ? (
                                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                    ) : isDeletingReport ? (
                                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                    ) : (
                                        <Paperclip className="h-3.5 w-3.5" />
                                    )}
                                    <span className="text-xs font-medium">
                                        {isUploading ? (
                                            <>
                                                {uploadStage === "preparing" && "Preparing..."}
                                                {uploadStage === "authenticating" && "Authenticating..."}
                                                {uploadStage === "preparing-upload" && "Preparing upload..."}
                                                {uploadStage === "uploading" && "Uploading file..."}
                                                {uploadStage === "processing-response" && "Processing..."}
                                                {uploadStage === "updating-scan-data" && "Updating pentest..."}
                                                {uploadStage === "updating-vulnerability-counts" && "Updating counts..."}
                                                {uploadStage === "updating-ai-summary" && "Updating AI summary..."}
                                                {uploadStage === "fetching-latest-data" && "Fetching latest data..."}
                                                {uploadStage === "finalizing" && "Finalizing..."}
                                                {uploadStage === "completed" && "Upload complete!"}
                                                {uploadStage === "error" && "Upload failed!"}
                                                {!uploadStage && "Uploading..."}
                                            </>
                                        ) : isDeletingReport ? "Deleting..." : "Upload Report"}
                                    </span>
                                </label>
                            </Button>
                            <input
                                id={`file-upload-${scan.id}`}
                                type="file"
                                accept=".pdf,.json,.xml,.txt,.csv" // Allow common report formats
                                className="sr-only"
                                onChange={handleFileUpload}
                                disabled={isUploading || isDeletingReport}
                            />
                        </>
                    )}

                    {/* Client: Request Re-test Button - Only for client/manager when scan is completed */}
                    {scan.status === "completed" && (role === 'client' || role === 'manager') && (
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-9 px-4 gap-2 rounded-md text-purple-600 dark:text-purple-300 border-0 bg-purple-50/50 dark:bg-purple-950/20 hover:bg-purple-100 dark:hover:bg-purple-900/20 hover:text-purple-700 cursor-pointer shadow-sm transition-colors"
                            onClick={async () => {
                                try {
                                    setIsSaving(true);
                                    const idToken = await user?.getIdToken();
                                    if (!idToken) {
                                        throw new Error("Authentication token not available.");
                                    }

                                    // Update the scan locally first for immediate UI feedback
                                    updateScanLocally(scan.id, {
                                        status: "re-test",
                                        progress: 0
                                    });

                                    // Send the update to the server
                                    const response = await fetch(`/api/scans`, {
                                        method: 'PUT',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'Authorization': `Bearer ${idToken}`,
                                        },
                                        body: JSON.stringify({
                                            scanId: scan.id,
                                            status: "re-test",
                                            progress: 0
                                        }),
                                    });

                                    if (!response.ok) {
                                        const errorData = await response.json().catch(() => ({}));
                                        throw new Error(errorData.error || `Failed to update pentest (${response.status})`);
                                    }

                                    // Send email notification for re-test request
                                    try {
                                        // Get recipients - admin and client
                                        const recipients = [scan.userEmail]; // Start with the scan owner

                                        // Add admin email
                                        if (scan.userEmail !== '<EMAIL>') {
                                            recipients.push('<EMAIL>');
                                        }

                                        // Filter out any undefined or null emails
                                        const validRecipients = recipients.filter(email => email);

                                        if (validRecipients.length > 0) {
                                            // Send email notification
                                            const emailResponse = await fetch('/api/send-email', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                    'Authorization': `Bearer ${idToken}`,
                                                },
                                                body: JSON.stringify({
                                                    scanId: scan.id,
                                                    recipients: validRecipients,
                                                    oldStatus: "completed",
                                                    newStatus: "re-test",
                                                    scanDetails: {
                                                        id: scan.id,
                                                        target: scan.target,
                                                        asset_type: scan.asset_type,
                                                        resultFileUrl: scan.resultFileUrl
                                                    }
                                                }),
                                            });

                                            if (emailResponse.ok) {
                                                console.log('Email notifications sent for re-test request');
                                            } else {
                                                console.error('Failed to send email notifications for re-test');
                                            }
                                        }
                                    } catch (emailError) {
                                        // Log the error but don't fail the overall operation
                                        console.error('Error sending re-test email notifications:', emailError);
                                    }

                                    toast({
                                        title: "Success",
                                        description: "Re-test for pentest requested successfully."
                                    });

                                    // Call onStatusChange if provided
                                    if (onStatusChange) {
                                        onStatusChange();
                                    }
                                } catch (error: any) {
                                    console.error("Error requesting re-test:", error);
                                    toast({
                                        title: "Error",
                                        description: error.message || "Could not request re-test for pentest.",
                                        variant: "destructive"
                                    });

                                    // Revert the local update in case of error
                                    updateScanLocally(scan.id, { status: "completed", progress: scan.progress });
                                } finally {
                                    setIsSaving(false);
                                }
                            }}
                            disabled={isSaving}
                        >
                            {isSaving ? (
                                <>
                                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                    <span className="text-xs font-medium">Requesting...</span>
                                </>
                            ) : (
                                <>
                                    <RefreshCw className="h-3.5 w-3.5" />
                                    <span className="text-xs font-medium">Request Re-test</span>
                                </>
                            )}
                        </Button>
                    )}

                    {/* Report Buttons - Available when result file exists and not currently uploading or deleting */}
                    {scan.status === "completed" && scan.resultFileUrl && !isUploading && !isDeletingReport && (
                        <div className="flex items-center gap-2.5">
                            {/* Admin-only Buttons */}
                            {role === 'admin' && (
                                <>
                                    {/* Delete Report Button */}
                                    {deleteReportConfirmation ? (
                                        <div className="flex space-x-1 bg-background/90 backdrop-blur-sm rounded-full p-0.5 shadow-sm border">
                                            <button /* Confirm */
                                                type="button"
                                                className="p-1.5 text-green-500 rounded-full hover:bg-green-500/10 cursor-pointer transition-colors"
                                                onClick={async (e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();

                                                    // Set deleting state
                                                    setIsDeletingReport(true);

                                                    try {
                                                        const idToken = await user?.getIdToken();
                                                        if (!idToken) {
                                                            throw new Error("Authentication token not available.");
                                                        }

                                                        // Call API to delete the report
                                                        const response = await fetch(`/api/delete-scan-report?scanId=${scan.id}`, {
                                                            method: 'DELETE',
                                                            headers: {
                                                                'Authorization': `Bearer ${idToken}`,
                                                            }
                                                        });

                                                        if (!response.ok) {
                                                            const errorData = await response.json().catch(() => ({}));
                                                            throw new Error(errorData.error || `Failed to delete report (${response.status})`);
                                                        }

                                                        // Get the response data
                                                        const responseData = await response.json();
                                                        const vulnerabilitiesDeleted = responseData.vulnerabilitiesDeleted || 0;

                                                        // Update local state
                                                        updateScanLocally(scan.id, {
                                                            resultFileUrl: undefined,
                                                            files: [],
                                                            aiSummary: undefined,
                                                            // Reset vulnerability counts
                                                            criticalVulnerabilities: 0,
                                                            highVulnerabilities: 0,
                                                            mediumVulnerabilities: 0,
                                                            lowVulnerabilities: 0
                                                        });

                                                        // Reset AI summary state
                                                        setAiSummary(null);

                                                        // Show success message with vulnerability deletion info
                                                        toast({
                                                            title: "Success",
                                                            description: vulnerabilitiesDeleted > 0
                                                                ? `Report deleted successfully. ${vulnerabilitiesDeleted} associated vulnerabilities were also removed.`
                                                                : "Report deleted successfully."
                                                        });
                                                    } catch (error: any) {
                                                        console.error("Error deleting report:", error);
                                                        toast({
                                                            title: "Error",
                                                            description: error.message || "Could not delete report.",
                                                            variant: "destructive"
                                                        });
                                                    } finally {
                                                        setIsDeletingReport(false);
                                                        setDeleteReportConfirmation(false);
                                                    }
                                                }}
                                                disabled={isDeletingReport}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><polyline points="20 6 9 17 4 12"></polyline></svg>
                                            </button>
                                            <button /* Cancel */
                                                type="button"
                                                className="p-1.5 text-destructive rounded-full hover:bg-destructive/10 cursor-pointer transition-colors"
                                                onClick={() => setDeleteReportConfirmation(false)}
                                                disabled={isDeletingReport}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                                            </button>
                                        </div>
                                    ) : (
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="h-9 w-9 p-0 rounded-full cursor-pointer bg-white/90 dark:bg-black/90 hover:text-destructive transition-colors hover:bg-destructive/10 shadow-sm"
                                            onClick={() => setDeleteReportConfirmation(true)}
                                            disabled={isDeletingReport}
                                        >
                                            {isDeletingReport ? <Loader2 className="h-3.5 w-3.5 animate-spin" /> : <Trash2 className="h-3.5 w-3.5" />}
                                            <span className="sr-only">Delete report</span>
                                        </Button>
                                    )}
                                </>
                            )}

                            {/* Download Report Button */}
                            <Button
                                asChild
                                variant="outline"
                                size="sm"
                                className="h-9 px-4 gap-2 rounded-md bg-white/90 dark:bg-black/90 hover:bg-muted/20 transition-colors shadow-sm"
                            >
                                <a href={scan.resultFileUrl} target="_blank" rel="noopener noreferrer">
                                    <Download className="h-3.5 w-3.5" />
                                    <span className="text-xs font-medium">Download</span>
                                </a>
                            </Button>

                            {/* Review Report Button */}
                            <Button
                                variant="default"
                                size="sm"
                                className="h-9 px-4 gap-2 rounded-md shadow-sm"
                                onClick={async () => {
                                    setIsReportModalOpen(true);

                                    // If we don't have an AI summary yet, try to load it
                                    if (!aiSummary && !isLoadingAiSummary && scan.resultFileUrl) {
                                        try {
                                            // Start loading the AI summary
                                            await getAiSummary();
                                        } catch (error) {
                                            console.error("Error preparing report view:", error);
                                            // Error will be shown in the modal via aiSummaryError state
                                        }
                                    }
                                }}
                            >
                                {isLoadingAiSummary ? (
                                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                ) : (
                                    <Eye className="h-3.5 w-3.5" />
                                )}
                                <span className="text-xs font-medium">
                                    {isLoadingAiSummary ? "Loading Report..." : "Review Report"}
                                </span>
                            </Button>
                        </div>
                    )}
                </div>
            </CardFooter>

            {/* Details Modal */}
            {isDetailsModalOpen && (
                <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
                    <DialogContent className="sm:max-w-4xl w-[95vw] max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2 duration-300">
                        <DialogHeader className="animate-in slide-in-from-top-2 duration-300 delay-100">
                            <DialogTitle>Pentest Details</DialogTitle>
                            <DialogDescription>
                                Detailed information about this security pentest
                            </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-6 py-4">
                            {/* Basic Information */}
                            <div className="space-y-4 animate-in slide-in-from-left-2 duration-300 delay-200">
                                <h3 className="text-lg font-semibold">Basic Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">Target</span>
                                            <span className="text-sm font-semibold">{scan.target}</span>
                                        </div>
                                        <Separator />

                                        <div className="flex justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">Asset Type</span>
                                            <span className="text-sm font-semibold">{scan.asset_type ? formatAssetType(scan.asset_type) : "Not specified"}</span>
                                        </div>
                                        <Separator />

                                        <div className="flex justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">Status</span>
                                            <span className="text-sm font-semibold flex items-center gap-1.5">
                                                {getStatusIcon()}
                                                {getStatusText()}
                                            </span>
                                        </div>
                                        <Separator />
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">Requested</span>
                                            <span className="text-sm font-semibold">{new Date(scan.requestedAt).toLocaleString()}</span>
                                        </div>
                                        <Separator />

                                        {scan.completedAt && (
                                            <>
                                                <div className="flex justify-between">
                                                    <span className="text-sm font-medium text-muted-foreground">Completed</span>
                                                    <span className="text-sm font-semibold">{new Date(scan.completedAt).toLocaleString()}</span>
                                                </div>
                                                <Separator />
                                            </>
                                        )}

                                        <div className="flex justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">Requested By</span>
                                            <span className="text-sm font-semibold">{scan.userEmail || "Unknown"}</span>
                                        </div>
                                        <Separator />
                                    </div>
                                </div>
                            </div>

                            {/* Vulnerability Summary */}
                            {scan.status === "completed" && (
                                <div className="space-y-4 animate-in slide-in-from-right-2 duration-300 delay-300">
                                    <h3 className="text-lg font-semibold">Vulnerability Summary</h3>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div className="p-4 bg-red-50 dark:bg-red-950/30 rounded-md border border-red-200 dark:border-red-900/30 flex flex-col items-center justify-center">
                                            <span className="text-2xl font-bold text-red-600 dark:text-red-400">{scan.criticalVulnerabilities}</span>
                                            <span className="text-sm text-red-600 dark:text-red-400">Critical</span>
                                        </div>
                                        <div className="p-4 bg-orange-50 dark:bg-orange-950/30 rounded-md border border-orange-200 dark:border-orange-900/30 flex flex-col items-center justify-center">
                                            <span className="text-2xl font-bold text-orange-600 dark:text-orange-400">{scan.highVulnerabilities}</span>
                                            <span className="text-sm text-orange-600 dark:text-orange-400">High</span>
                                        </div>
                                        <div className="p-4 bg-yellow-50 dark:bg-yellow-950/30 rounded-md border border-yellow-200 dark:border-yellow-900/30 flex flex-col items-center justify-center">
                                            <span className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{scan.mediumVulnerabilities}</span>
                                            <span className="text-sm text-yellow-600 dark:text-yellow-400">Medium</span>
                                        </div>
                                        <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-md border border-blue-200 dark:border-blue-900/30 flex flex-col items-center justify-center">
                                            <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">{scan.lowVulnerabilities}</span>
                                            <span className="text-sm text-blue-600 dark:text-blue-400">Low</span>
                                        </div>
                                    </div>

                                    {/* Additional vulnerability details */}
                                    {totalVulnerabilities > 0 ? (
                                        <div className="text-sm text-muted-foreground bg-muted/30 p-4 rounded-md">
                                            <p>This pentest detected {totalVulnerabilities} vulnerabilities across different severity levels.
                                            {scan.criticalVulnerabilities > 0 && ` ${scan.criticalVulnerabilities} critical vulnerabilities require immediate attention.`}
                                            {scan.highVulnerabilities > 0 && ` ${scan.highVulnerabilities} high-risk vulnerabilities should be addressed soon.`}
                                            </p>
                                        </div>
                                    ) : (
                                        <div className="text-sm text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20 p-4 rounded-md">
                                            <p>No vulnerabilities were detected in this pentest. The target appears to be secure based on the current security checks.</p>
                                        </div>
                                    )}

                                    {scan.resultFileUrl && (
                                        <div className="flex justify-end mt-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="gap-2"
                                                onClick={() => {
                                                    setIsDetailsModalOpen(false);
                                                    setIsReportModalOpen(true);
                                                }}
                                            >
                                                <Eye className="h-4 w-4" />
                                                <span>View Full Report</span>
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Attached Files Section */}
                            {(scan.resultFileUrl || (scan.files && scan.files.length > 0)) && (
                                <div className="space-y-3 animate-in slide-in-from-left-2 duration-300 delay-400">
                                    <h3 className="text-lg font-semibold">Attached Files</h3>
                                    <div className="space-y-2">
                                        {ensureFilesArray().map((file, index) => (
                                            <div key={index} className="flex flex-col text-sm border rounded-md p-3 bg-muted/30">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-2 overflow-hidden">
                                                        <FileText className="h-4 w-4 flex-shrink-0 text-primary" />
                                                        <span className="truncate font-medium" title={file.originalName}>
                                                            {file.originalName}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <Button asChild variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                            <a href={file.url} target="_blank" rel="noopener noreferrer" title="Open file">
                                                                <ExternalLink className="h-4 w-4" />
                                                                <span className="sr-only">Open file</span>
                                                            </a>
                                                        </Button>
                                                    </div>
                                                </div>
                                                <div className="flex items-center justify-between text-xs text-muted-foreground mt-1 pl-6">
                                                    <div className="flex items-center gap-2">
                                                        {file.size && (
                                                            <span>
                                                                {(file.size / 1024 / 1024).toFixed(1)} MB
                                                            </span>
                                                        )}
                                                        {file.uploadedAt && (
                                                            <span>
                                                                • Uploaded {new Date(file.uploadedAt).toLocaleString(undefined, {
                                                                    year: 'numeric',
                                                                    month: 'numeric',
                                                                    day: 'numeric',
                                                                    hour: '2-digit',
                                                                    minute: '2-digit'
                                                                })}
                                                            </span>
                                                        )}
                                                    </div>
                                                    {file.contentType && (
                                                        <span className="text-xs bg-muted rounded px-1.5 py-0.5">
                                                            {file.contentType.split('/')[1]?.toUpperCase() || file.contentType}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Progress Information */}
                            {(scan.status === "in-progress" || scan.status === "re-test") && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold">Progress</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm font-medium">Completion</span>
                                            <span className="text-sm font-semibold">{scan.progress || 0}%</span>
                                        </div>
                                       <div className="stepper-container">
                                           <div className="stepper-track"></div>
                                           <div className={`stepper-progress ${scan.status === 're-test' ? 're-test' : ''}`} style={{ width: `${scan.progress || 0}%` }}></div>
                                           <div className="stepper-checkpoints">
                                               {[0, 25, 50, 75, 100].map((value) => (
                                                   <div
                                                       key={value}
                                                       className={`stepper-checkpoint ${(scan.progress || 0) >= value ? 'active' : ''} ${scan.status === 're-test' ? 're-test' : ''}`}
                                                   >
                                                   </div>
                                               ))}
                                           </div>
                                       </div>
                                    </div>
                                </div>
                            )}

                            {/* Additional Scan Details */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold">Additional Details</h3>
                                <div className="rounded-lg border bg-card/50 overflow-hidden shadow-xs">
                                    <table className="w-full text-sm">
                                        <thead className="bg-muted/50 border-b border-muted/20">
                                            <tr>
                                                <th className="py-2 px-3 text-left font-medium text-xs text-muted-foreground">Property</th>
                                                <th className="py-2 px-3 text-right font-medium text-xs text-muted-foreground">Value</th>
                                            </tr>
                                        </thead>
                                        <tbody className="overflow-y-auto max-h-[300px]">
                                           {Object.entries(scan).map(([key, value]) => {
                                               if (
                                                   ["id", "name", "status", "target", "platform", "environment", "requestedAt", "completedAt", "criticalVulnerabilities", "highVulnerabilities", "mediumVulnerabilities", "lowVulnerabilities", "resultFileUrl", "userId", "files", "aiSummary", "application_name", "asset_type", "user_email", "conversation_title", "progress"].includes(key) ||
                                                   typeof value === 'object' ||
                                                   value === null ||
                                                   value === undefined ||
                                                   value === ''
                                               ) { return null; }

                                               return (
                                                   <tr key={key} className="border-b border-muted/20 last:border-0 hover:bg-muted/20 transition-colors">
                                                       <td className="py-2 px-3 text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1').replace(/_/g, ' ')}</td>
                                                       <td className="py-2 px-3 font-medium text-right">
                                                           {key.toLowerCase().includes('at') && key !== 'conversationTitle' && key !== 'conversationId' && typeof value === 'string' && (value.includes('T') || value.includes('-'))
                                                               ? new Date(value).toLocaleString(undefined, {
                                                                   year: 'numeric',
                                                                   month: 'numeric',
                                                                   day: 'numeric',
                                                                   hour: '2-digit',
                                                                   minute: '2-digit'
                                                               })
                                                               : String(value)
                                                           }
                                                       </td>
                                                   </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            )}

            {/* Edit Dialog */}
            {isEditing && (
                <Dialog open={isEditing} onOpenChange={setIsEditing}>
                    <DialogContent className="sm:max-w-md animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2 duration-300">
                        <DialogHeader className="animate-in slide-in-from-top-2 duration-300 delay-100">
                            <DialogTitle>Edit Pentest</DialogTitle>
                            <DialogDescription>
                                Update pentest status, progress, and vulnerability counts.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <h4 className="text-sm font-medium">Status</h4>
                                <div className="flex flex-wrap gap-2">
                                    <Button
                                        type="button"
                                        variant={editedData.status === "pending" ? "default" : "outline"}
                                        size="sm"
                                        className={cn(
                                            "h-9 px-4 gap-2",
                                            editedData.status === "pending" && "bg-yellow-500 hover:bg-yellow-600"
                                        )}
                                        onClick={() => setEditedData({ ...editedData, status: "pending" })}
                                    >
                                        <Clock className="h-3.5 w-3.5" />
                                        <span>Pending</span>
                                    </Button>
                                    <Button
                                        type="button"
                                        variant={editedData.status === "in-progress" ? "default" : "outline"}
                                        size="sm"
                                        className={cn(
                                            "h-9 px-4 gap-2",
                                            editedData.status === "in-progress" && "bg-blue-500 hover:bg-blue-600"
                                        )}
                                        onClick={() => setEditedData({ ...editedData, status: "in-progress" })}
                                    >
                                        <AlertCircle className="h-3.5 w-3.5" />
                                        <span>In Progress</span>
                                    </Button>
                                    <Button
                                        type="button"
                                        variant={editedData.status === "re-test" ? "default" : "outline"}
                                        size="sm"
                                        className={cn(
                                            "h-9 px-4 gap-2",
                                            editedData.status === "re-test" && "bg-purple-500 hover:bg-purple-600"
                                        )}
                                        onClick={() => setEditedData({ ...editedData, status: "re-test" })}
                                    >
                                        <RefreshCw className="h-3.5 w-3.5" />
                                        <span>Re-test</span>
                                    </Button>
                                    <Button
                                        type="button"
                                        variant={editedData.status === "completed" ? "default" : "outline"}
                                        size="sm"
                                        className={cn(
                                            "h-9 px-4 gap-2",
                                            editedData.status === "completed" && "bg-green-500 hover:bg-green-600"
                                        )}
                                        onClick={() => setEditedData({ ...editedData, status: "completed" })}
                                    >
                                        <CheckCircle2 className="h-3.5 w-3.5" />
                                        <span>Completed</span>
                                    </Button>
                                </div>
                            </div>

                            {(editedData.status === "in-progress" || editedData.status === "re-test") && (
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <h4 className="text-sm font-medium">Progress</h4>
                                        <span className="text-sm">{editedData.progress}%</span>
                                    </div>
                                    <div className="stepper-container">
                                        <div className="stepper-track"></div>
                                        <div className={`stepper-progress ${editedData.status === 're-test' ? 're-test' : ''}`} style={{ width: `${editedData.progress}%` }}></div>
                                        <div className="stepper-checkpoints">
                                            {[0, 25, 50, 75, 100].map((value) => (
                                                <div
                                                    key={value}
                                                    className={`stepper-checkpoint ${editedData.progress >= value ? 'active' : ''} ${editedData.status === 're-test' ? 're-test' : ''}`}
                                                    onClick={() => setEditedData({ ...editedData, progress: value })}
                                                >
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {editedData.status === "completed" && (
                                <div className="space-y-4">
                                    <h4 className="text-sm font-medium">Vulnerability Counts</h4>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <label className="text-xs text-red-600 font-medium">Critical</label>
                                            <div className="flex items-center">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, criticalVulnerabilities: Math.max(0, editedData.criticalVulnerabilities - 1) })}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <input
                                                    type="number"
                                                    min="0"
                                                    value={editedData.criticalVulnerabilities}
                                                    onChange={(e) => setEditedData({ ...editedData, criticalVulnerabilities: parseInt(e.target.value) || 0 })}
                                                    className="h-8 w-12 text-center mx-2 border rounded"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, criticalVulnerabilities: editedData.criticalVulnerabilities + 1 })}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="space-y-2">
                                            <label className="text-xs text-orange-600 font-medium">High</label>
                                            <div className="flex items-center">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, highVulnerabilities: Math.max(0, editedData.highVulnerabilities - 1) })}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <input
                                                    type="number"
                                                    min="0"
                                                    value={editedData.highVulnerabilities}
                                                    onChange={(e) => setEditedData({ ...editedData, highVulnerabilities: parseInt(e.target.value) || 0 })}
                                                    className="h-8 w-12 text-center mx-2 border rounded"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, highVulnerabilities: editedData.highVulnerabilities + 1 })}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="space-y-2">
                                            <label className="text-xs text-yellow-600 font-medium">Medium</label>
                                            <div className="flex items-center">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, mediumVulnerabilities: Math.max(0, editedData.mediumVulnerabilities - 1) })}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <input
                                                    type="number"
                                                    min="0"
                                                    value={editedData.mediumVulnerabilities}
                                                    onChange={(e) => setEditedData({ ...editedData, mediumVulnerabilities: parseInt(e.target.value) || 0 })}
                                                    className="h-8 w-12 text-center mx-2 border rounded"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, mediumVulnerabilities: editedData.mediumVulnerabilities + 1 })}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="space-y-2">
                                            <label className="text-xs text-blue-600 font-medium">Low</label>
                                            <div className="flex items-center">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, lowVulnerabilities: Math.max(0, editedData.lowVulnerabilities - 1) })}
                                                >
                                                    <Minus className="h-3 w-3" />
                                                </Button>
                                                <input
                                                    type="number"
                                                    min="0"
                                                    value={editedData.lowVulnerabilities}
                                                    onChange={(e) => setEditedData({ ...editedData, lowVulnerabilities: parseInt(e.target.value) || 0 })}
                                                    className="h-8 w-12 text-center mx-2 border rounded"
                                                />
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 w-8 p-0"
                                                    onClick={() => setEditedData({ ...editedData, lowVulnerabilities: editedData.lowVulnerabilities + 1 })}
                                                >
                                                    <Plus className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setIsEditing(false);
                                    // Reset edited data to current scan values
                                    setEditedData({
                                        status: scan.status,
                                        criticalVulnerabilities: scan.criticalVulnerabilities,
                                        highVulnerabilities: scan.highVulnerabilities,
                                        mediumVulnerabilities: scan.mediumVulnerabilities,
                                        lowVulnerabilities: scan.lowVulnerabilities,
                                        progress: scan.progress || 0
                                    });
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="button"
                                disabled={isSaving}
                                onClick={async () => {
                                    try {
                                        setIsSaving(true);
                                        const idToken = await user?.getIdToken();
                                        if (!idToken) {
                                            throw new Error("Authentication token not available.");
                                        }

                                        // Store the old status for notification purposes
                                        const oldStatus = scan.status;

                                        // Update the scan locally first for immediate UI feedback
                                        updateScanLocally(scan.id, editedData);

                                        // Send the update to the server
                                        const response = await fetch(`/api/scans`, {
                                            method: 'PUT',
                                            headers: {
                                                'Content-Type': 'application/json',
                                                'Authorization': `Bearer ${idToken}`,
                                            },
                                            body: JSON.stringify({
                                                scanId: scan.id,
                                                ...editedData
                                            }),
                                        });

                                        if (!response.ok) {
                                            const errorData = await response.json().catch(() => ({}));
                                            throw new Error(errorData.error || `Failed to update pentest (${response.status})`);
                                        }

                                        // If status changed, send email notification
                                        if (oldStatus !== editedData.status) {
                                            try {
                                                // Get recipients - admin and client
                                                const recipients = [scan.userEmail]; // Start with the scan owner

                                                // Add admin email if not already included
                                                if (scan.userEmail !== '<EMAIL>') {
                                                    recipients.push('<EMAIL>');
                                                }

                                                // Filter out any undefined or null emails
                                                const validRecipients = recipients.filter(email => email);

                                                if (validRecipients.length > 0) {
                                                    // Send email notification
                                                    const emailResponse = await fetch('/api/send-email', {
                                                        method: 'POST',
                                                        headers: {
                                                            'Content-Type': 'application/json',
                                                            'Authorization': `Bearer ${idToken}`,
                                                        },
                                                        body: JSON.stringify({
                                                            scanId: scan.id,
                                                            recipients: validRecipients,
                                                            oldStatus: oldStatus,
                                                            newStatus: editedData.status,
                                                            scanDetails: {
                                                                id: scan.id,
                                                                target: scan.target,
                                                                asset_type: scan.asset_type,
                                                                resultFileUrl: scan.resultFileUrl
                                                            }
                                                        }),
                                                    });

                                                    if (emailResponse.ok) {
                                                        console.log('Email notifications sent for status change');
                                                    } else {
                                                        console.error('Failed to send email notifications');
                                                    }
                                                }
                                            } catch (emailError) {
                                                // Log the error but don't fail the overall operation
                                                console.error('Error sending email notifications:', emailError);
                                            }
                                        }

                                        toast({
                                            title: "Success",
                                            description: "Pentest updated successfully."
                                        });

                                        // Call onStatusChange if provided
                                        if (onStatusChange) {
                                            onStatusChange();
                                        }

                                        setIsEditing(false);
                                    } catch (error: any) {
                                        console.error("Error updating pentest:", error);
                                        toast({
                                            title: "Error",
                                            description: error.message || "Could not update pentest.",
                                            variant: "destructive"
                                        });

                                        // Revert the local update in case of error
                                        updateScanLocally(scan.id, {
                                            status: scan.status,
                                            criticalVulnerabilities: scan.criticalVulnerabilities,
                                            highVulnerabilities: scan.highVulnerabilities,
                                            mediumVulnerabilities: scan.mediumVulnerabilities,
                                            lowVulnerabilities: scan.lowVulnerabilities,
                                            progress: scan.progress || 0
                                        });
                                    } finally {
                                        setIsSaving(false);
                                    }
                                }}
                            >
                                {isSaving ? (
                                    <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    <>Save</>
                                )}
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            )}

            {/* Report Review Modal */}
            {isReportModalOpen && (
                <Dialog open={isReportModalOpen} onOpenChange={setIsReportModalOpen}>
                    <DialogContent className="sm:max-w-5xl w-[95vw] max-h-[90vh] overflow-y-auto animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2 duration-300">
                        <DialogHeader className="animate-in slide-in-from-top-2 duration-300 delay-100">
                            <DialogTitle>Security Pentest Report</DialogTitle>
                            <DialogDescription>
                                Explore comprehensive vulnerability findings, potential security impacts, and actionable recommendations from your security assessment.
                            </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-6 py-4">
                            {/* AI Summary Section */}
                            <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-semibold">Summary</h3>
                                    {!isLoadingAiSummary && !aiSummaryError && (
                                        <div className="flex items-center gap-2">
                                            {aiSummary && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="h-8 gap-1.5"
                                                    onClick={() => {
                                                        if (!aiSummary) return;

                                                        // Create a text version of the summary for clipboard
                                                        const textContent = `
SECURITY PENTEST SUMMARY - ${scan.target}
Generated on ${new Date().toLocaleDateString()}

SUMMARY:
${aiSummary.summary}

${aiSummary.keyFindings.length > 0 ? `
KEY FINDINGS:
${aiSummary.keyFindings.map(item => `- [${item.severity.toUpperCase()}] ${item.finding}`).join('\n')}
` : ''}

${aiSummary.potentialImpacts.length > 0 ? `
POTENTIAL IMPACTS:
${aiSummary.potentialImpacts.map(impact => `- ${impact}`).join('\n')}
` : ''}

${aiSummary.recommendations.length > 0 ? `
RECOMMENDATIONS:
${aiSummary.recommendations.map(item => `- [${item.priority.toUpperCase()}] ${item.recommendation}`).join('\n')}
` : ''}

This is an AI-generated summary. For complete details and remediation steps, please refer to the full report.
Generated by DeepScan by GrowthGuard
                                                        `;

                                                        // Copy to clipboard
                                                        navigator.clipboard.writeText(textContent.trim())
                                                            .then(() => {
                                                                toast({
                                                                    title: "Copied to clipboard",
                                                                    description: "Summary has been copied to your clipboard",
                                                                });
                                                            })
                                                            .catch(err => {
                                                                console.error('Failed to copy: ', err);
                                                                toast({
                                                                    title: "Copy failed",
                                                                    description: "Could not copy to clipboard",
                                                                    variant: "destructive",
                                                                });
                                                            });
                                                    }}
                                                >
                                                    <Copy className="h-3.5 w-3.5" />
                                                    <span className="text-xs">Copy Summary</span>
                                                </Button>
                                            )}
                                            <Button
                                                asChild
                                                variant="default"
                                                size="sm"
                                                className="h-8 gap-1.5"
                                                disabled={!scan.resultFileUrl}
                                            >
                                                <a href={scan.resultFileUrl} target="_blank" rel="noopener noreferrer">
                                                    <Download className="h-3.5 w-3.5" />
                                                    <span className="text-xs">Download Report</span>
                                                </a>
                                            </Button>
                                        </div>
                                    )}
                                </div>

                                <div className="p-6 bg-muted/30 rounded-md border text-sm max-h-[90vh] overflow-y-auto">
                                    {isLoadingAiSummary && (
                                        <div className="flex flex-col items-center justify-center py-10 space-y-4">
                                            <div className="relative">
                                                <div className="absolute inset-0 flex items-center justify-center">
                                                    <Brain className="h-8 w-8 text-primary/30" />
                                                </div>
                                                <svg className="h-16 w-16 animate-spin text-primary/20" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="50" cy="50" r="45" stroke="currentColor" strokeWidth="4" strokeDasharray="70 30" strokeLinecap="round" />
                                                </svg>
                                            </div>
                                            <p className="text-sm text-muted-foreground mt-4 animate-pulse">
                                                {aiSummaryStage === "initializing" ? "Initializing..." :
                                                 aiSummaryStage === "loading-existing" ? "Loading existing analysis..." :
                                                 aiSummaryStage === "fetching-latest-data" ? "Fetching latest data..." :
                                                 aiSummaryStage === "generating-new-summary" ? "Preparing to generate summary..." :
                                                 aiSummaryStage === "downloading-report" ? "Downloading report..." :
                                                 aiSummaryStage === "analyzing-report" ? "Analyzing report with AI..." :
                                                 aiSummaryStage === "processing-results" ? "Processing results..." :
                                                 aiSummaryStage === "completed" ? "Analysis complete!" :
                                                 aiSummaryStage === "error" ? "Error analyzing report" :
                                                 scan.aiSummary ? "Loading existing report analysis..." : "Analyzing report with AI..."}
                                            </p>
                                        </div>
                                    )}

                                    {!isLoadingAiSummary && aiSummaryError && (
                                        <div className="space-y-4">
                                            <div className="p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-900/30 rounded-md">
                                                <p className="text-red-600 dark:text-red-400 font-medium mb-2">
                                                    Unable to generate AI summary
                                                </p>
                                                <p className="text-red-500 dark:text-red-300 text-sm">
                                                    Error: {aiSummaryError}
                                                </p>
                                            </div>
                                            <div className="flex items-center justify-between mt-4 pt-4 border-t">
                                                <p className="text-sm text-muted-foreground">
                                                    Please try again or download the report to view the full details.
                                                </p>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={getAiSummary}
                                                    className="ml-4"
                                                >
                                                    <Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
                                                    Retry Analysis
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {!isLoadingAiSummary && !aiSummaryError && aiSummary && (
                                        <div className="space-y-6">
                                            {/* Main Summary */}
                                            <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-200 dark:border-blue-900/30">
                                                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2 flex items-center gap-2">
                                                    <FileText className="h-4 w-4" />
                                                    Summary
                                                </h4>
                                                <p className="text-base">{aiSummary.summary}</p>
                                            </div>

                                            {/* Key Findings */}
                                            {aiSummary.keyFindings.length > 0 && (
                                                <div className="bg-amber-50 dark:bg-amber-950/30 p-4 rounded-md border border-amber-200 dark:border-amber-900/30" style={{ alignItems: 'center' }}>
                                                    <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2 flex items-center gap-2">
                                                        <Search className="h-4 w-4" />
                                                        Key Findings
                                                    </h4>
                                                    <ul className="pl-0 space-y-3">
                                                        {aiSummary.keyFindings.map((item, index) => (
                                                            <li key={index} className="flex items-start gap-3">
                                                                <div className="flex-shrink-0">
                                                                    {item.severity === 'critical' && (
                                                                        <Badge className="bg-red-500 text-white hover:bg-red-600">Critical</Badge>
                                                                    )}
                                                                    {item.severity === 'high' && (
                                                                        <Badge className="bg-orange-500 text-white hover:bg-orange-600">High</Badge>
                                                                    )}
                                                                    {item.severity === 'medium' && (
                                                                        <Badge className="bg-amber-500 text-white hover:bg-amber-600">Medium</Badge>
                                                                    )}
                                                                    {item.severity === 'low' && (
                                                                        <Badge className="bg-blue-500 text-white hover:bg-blue-600">Low</Badge>
                                                                    )}
                                                                </div>
                                                                <div className="text-amber-700 dark:text-amber-400">
                                                                    {item.finding}
                                                                </div>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Potential Impacts */}
                                            {aiSummary.potentialImpacts.length > 0 && (
                                                <div className="bg-red-50 dark:bg-red-950/30 p-4 rounded-md border border-red-200 dark:border-red-900/30">
                                                    <h4 className="font-semibold text-red-800 dark:text-red-300 mb-2 flex items-center gap-2">
                                                        <AlertTriangle className="h-4 w-4" />
                                                        Potential Impacts
                                                    </h4>
                                                    <ul className="list-disc pl-5 space-y-1">
                                                        {aiSummary.potentialImpacts.map((impact, index) => (
                                                            <li key={index} className="text-red-700 dark:text-red-400 ">
                                                                {impact}
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Recommendations */}
                                            {aiSummary.recommendations.length > 0 && (
                                                <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-md border border-green-200 dark:border-green-900/30" style={{ alignItems: 'center' }}>
                                                    <h4 className="font-semibold text-green-800 dark:text-green-300 mb-2 flex items-center gap-2">
                                                        <CheckCircle className="h-4 w-4" />
                                                        Recommendations
                                                    </h4>
                                                    <ul className="pl-0 space-y-3">
                                                        {aiSummary.recommendations.map((item, index) => (
                                                            <li key={index} className="flex items-start gap-3">
                                                                <div className="flex-shrink-0">
                                                                    {item.priority === 'immediate' && (
                                                                        <Badge className="bg-red-500 text-white hover:bg-red-600">Critical</Badge>
                                                                    )}
                                                                    {item.priority === 'high' && (
                                                                        <Badge className="bg-orange-500 text-white hover:bg-orange-600">High</Badge>
                                                                    )}
                                                                    {item.priority === 'medium' && (
                                                                        <Badge className="bg-amber-500 text-white hover:bg-amber-600">Medium</Badge>
                                                                    )}
                                                                    {item.priority === 'low' && (
                                                                        <Badge className="bg-blue-500 text-white hover:bg-blue-600">Low</Badge>
                                                                    )}
                                                                </div>
                                                                <div className="text-green-700 dark:text-green-400">
                                                                    {item.recommendation}
                                                                </div>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Raw Analysis (fallback) */}
                                            {aiSummary.rawAnalysis && (
                                                <div className="bg-gray-50 dark:bg-gray-900/30 p-4 rounded-md border border-gray-200 dark:border-gray-800">
                                                    <h4 className="font-semibold text-gray-800 dark:text-gray-300 mb-2 flex items-center gap-2">
                                                        <FileCode className="h-4 w-4" />
                                                        Raw Analysis
                                                    </h4>
                                                    <pre className="whitespace-pre-wrap text-sm">{aiSummary.rawAnalysis}</pre>
                                                </div>
                                            )}

                                            <div className="mt-4 border-t pt-4">
                                                <p className="text-xs text-muted-foreground">
                                                    For complete details and remediation steps, please download the full report.
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {!isLoadingAiSummary && !aiSummaryError && !aiSummary && (
                                        <div className="space-y-6">
                                            {/* Fallback content when no AI summary is available yet */}
                                            {/* Main Summary */}
                                            <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-200 dark:border-blue-900/30">
                                                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2 flex items-center gap-2">
                                                    <FileText className="h-4 w-4" />
                                                    Summary
                                                </h4>
                                                <p className="text-base">
                                                    {totalVulnerabilities > 0
                                                        ? `Security pentest for ${scan.target} detected ${totalVulnerabilities} vulnerabilities that require attention.`
                                                        : `Security pentest for ${scan.target} found no vulnerabilities based on the current security checks.`
                                                    }
                                                </p>
                                            </div>

                                            {/* Key Findings */}
                                            {totalVulnerabilities > 0 && (
                                                <div className="bg-amber-50 dark:bg-amber-950/30 p-4 rounded-md border border-amber-200 dark:border-amber-900/30">
                                                    <h4 className="font-semibold text-amber-800 dark:text-amber-300 mb-2">Key Findings</h4>
                                                    <ul className="list-disc pl-5 space-y-1">
                                                        {scan.criticalVulnerabilities > 0 && (
                                                            <li className="text-amber-700 dark:text-amber-400">
                                                                {scan.criticalVulnerabilities} critical vulnerabilities detected
                                                            </li>
                                                        )}
                                                        {scan.highVulnerabilities > 0 && (
                                                            <li className="text-amber-700 dark:text-amber-400">
                                                                {scan.highVulnerabilities} high severity vulnerabilities detected
                                                            </li>
                                                        )}
                                                        {scan.mediumVulnerabilities > 0 && (
                                                            <li className="text-amber-700 dark:text-amber-400">
                                                                {scan.mediumVulnerabilities} medium severity vulnerabilities detected
                                                            </li>
                                                        )}
                                                        {scan.lowVulnerabilities > 0 && (
                                                            <li className="text-amber-700 dark:text-amber-400">
                                                                {scan.lowVulnerabilities} low severity vulnerabilities detected
                                                            </li>
                                                        )}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Potential Impacts */}
                                            {totalVulnerabilities > 0 && (
                                                <div className="bg-red-50 dark:bg-red-950/30 p-4 rounded-md border border-red-200 dark:border-red-900/30">
                                                    <h4 className="font-semibold text-red-800 dark:text-red-300 mb-2">Potential Impacts</h4>
                                                    <ul className="list-disc pl-5 space-y-1">
                                                        {scan.criticalVulnerabilities > 0 && (
                                                            <li className="text-red-700 dark:text-red-400">
                                                                Critical vulnerabilities may lead to unauthorized system access or data breaches
                                                            </li>
                                                        )}
                                                        {scan.highVulnerabilities > 0 && (
                                                            <li className="text-red-700 dark:text-red-400">
                                                                High severity issues could compromise sensitive information
                                                            </li>
                                                        )}
                                                        {(scan.criticalVulnerabilities > 0 || scan.highVulnerabilities > 0) && (
                                                            <li className="text-red-700 dark:text-red-400">
                                                                Potential for significant business disruption and reputational damage
                                                            </li>
                                                        )}
                                                    </ul>
                                                </div>
                                            )}

                                            {/* Recommendations */}
                                            <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-md border border-green-200 dark:border-green-900/30">
                                                <h4 className="font-semibold text-green-800 dark:text-green-300 mb-2">Recommendations</h4>
                                                <ul className="list-disc pl-5 space-y-1">
                                                    {totalVulnerabilities > 0 ? (
                                                        <>
                                                            {scan.criticalVulnerabilities > 0 && (
                                                                <li className="text-green-700 dark:text-green-400">
                                                                    Address critical vulnerabilities immediately
                                                                </li>
                                                            )}
                                                            <li className="text-green-700 dark:text-green-400">
                                                                Download the full report for detailed remediation steps
                                                            </li>
                                                            <li className="text-green-700 dark:text-green-400">
                                                                Schedule a follow-up scan after implementing fixes
                                                            </li>
                                                        </>
                                                    ) : (
                                                        <>
                                                            <li className="text-green-700 dark:text-green-400">
                                                                Continue regular security pentests to maintain security posture
                                                            </li>
                                                            <li className="text-green-700 dark:text-green-400">
                                                                Keep all systems and dependencies up to date
                                                            </li>
                                                        </>
                                                    )}
                                                </ul>
                                            </div>

                                            <p className="text-xs text-muted-foreground mt-4 border-t pt-4">
                                                This is a preliminary summary. For complete details, please download the full report.
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </Card>
    );
}
