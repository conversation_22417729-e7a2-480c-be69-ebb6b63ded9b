"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import {
  MessagesSquare,
  User,
  Search,
  RefreshCw,
  CheckCircle,
  Circle,
  Send,
  Loader2,
  ChevronLeft,
  Clock
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/useAuth"
import { useTeamMessages } from "@/context/TeamMessagesContext"
import { cn } from "@/lib/utils"

export const MessagesInterface = () => {
  const router = useRouter()
  const { user, role, loading: authLoading } = useAuth()
  const {
    messages,
    unreadCount,
    loading,
    fetchMessages,
    markAsRead,
    respondToMessage
  } = useTeamMessages()

  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)
  const [responseText, setResponseText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState<"all" | "unread">("all")
  const [isMounted, setIsMounted] = useState(false)
  const [historySidebarOpen, setHistorySidebarOpen] = useState(false)

  // Set mounted state to avoid hydration mismatch
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Redirect non-admin users
  useEffect(() => {
    if (isMounted && !authLoading && role !== "admin") {
      router.replace("/")
    }
  }, [isMounted, authLoading, role, router])

  // Handle message selection
  const handleSelectMessage = (messageId: string) => {
    setSelectedMessageId(messageId)
    setResponseText("")

    // Mark as read if it's unread
    const message = messages.find(msg => msg.id === messageId)
    if (message && message.status === "unread") {
      markAsRead(messageId)
    }
  }

  // Handle sending a response
  const handleSendResponse = async () => {
    if (!selectedMessageId || !responseText.trim() || isSubmitting) return

    setIsSubmitting(true)
    try {
      await respondToMessage(selectedMessageId, responseText)
      setResponseText("")
    } catch (error) {
      console.error("Error sending response:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Filter messages based on search query and active tab
  const filteredMessages = messages.filter(message => {
    const matchesSearch =
      message.userEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
      message.message.toLowerCase().includes(searchQuery.toLowerCase())

    if (activeTab === "unread") {
      return matchesSearch && message.status === "unread"
    }

    return matchesSearch
  })

  // Get the selected message
  const selectedMessage = selectedMessageId
    ? messages.find(msg => msg.id === selectedMessageId)
    : null

  // Conditional returns
  if (!isMounted) return null
  if (authLoading) return <div className="flex items-center justify-center h-[calc(100vh-4rem)]">Loading Authentication...</div>
  if (!user || role !== "admin") return null // Should be redirected

  return (
    <div className="flex h-[calc(100vh-4rem)] flex-col w-full relative overflow-hidden growthguard-gradient-bg dark:bg-[#111827]">
      {/* History Sidebar */}
      <div
        className={cn(
          "absolute inset-y-0 left-0 z-30 w-full sm:w-80 bg-white/90 dark:bg-[#111827]/90 backdrop-blur-md border-r transform transition-transform duration-300 ease-in-out",
          historySidebarOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        {/* Sidebar Content */}
        <div className="flex flex-col h-full dark:bg-[#111827]">
          <div className="p-4 border-b flex items-center justify-between">
            <h3 className="font-semibold">Messages</h3>
            <Button variant="ghost" size="icon" onClick={() => setHistorySidebarOpen(false)} className="dark:hover:bg-[#111827]">
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Close History</span>
            </Button>
          </div>
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search messages..."
                className="pl-8 rounded-full h-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Tabs
              defaultValue="all"
              className="mt-4"
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as "all" | "unread")}
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="all">All Messages</TabsTrigger>
                <TabsTrigger value="unread">
                  Unread
                  {unreadCount > 0 && (
                    <span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs">
                      {unreadCount}
                    </span>
                  )}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="all"></TabsContent>
              <TabsContent value="unread"></TabsContent>
            </Tabs>
          </div>
          <ScrollArea className="flex-1 dark:bg-[#111827]">
            <div className="p-2 space-y-1">
              {loading ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-primary mb-2" />
                  <p className="text-sm text-muted-foreground">Loading messages...</p>
                </div>
              ) : filteredMessages.length === 0 ? (
                <p className="text-center text-muted-foreground p-4 text-sm">No matching messages</p>
              ) : (
                filteredMessages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "p-3 rounded-lg cursor-pointer hover:bg-accent group relative card-hover min-h-[4.5rem]",
                      selectedMessageId === message.id ? "bg-accent" : "",
                      message.status === "unread" ? "border-l-4 border-primary" : ""
                    )}
                    onClick={() => handleSelectMessage(message.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center">
                        <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 text-primary">
                          <User className="h-4 w-4" />
                        </div>
                        <div className="ml-2 overflow-hidden">
                          <p className="font-medium truncate">{message.userEmail}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {message.status === "unread" ? (
                          <Circle className="h-3 w-3 fill-primary text-primary" />
                        ) : (
                          <CheckCircle className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                    </div>
                    <p className="mt-1 text-sm text-muted-foreground line-clamp-2">
                      {message.message}
                    </p>
                    <p className="mt-1 text-xs text-muted-foreground">
                      {format(new Date(message.timestamp), "MMM d, h:mm a")}
                    </p>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex flex-col flex-1 min-h-0 overflow-hidden dark:bg-[#111827]">
        {/* Header */}
        <div className="flex items-center justify-between border-b px-6 py-4">
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <MessagesSquare className="h-5 w-5 text-primary" />
              <h3 className="text-2xl font-bold">Team Messages</h3>
              {unreadCount > 0 && (
                <span className="ml-2 rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                  {unreadCount} unread
                </span>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {loading && "Loading messages..."}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (!loading) {
                  fetchMessages();
                }
              }}
              disabled={loading}
              className="dark:hover:bg-[#111827] dark:border-gray-700"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setHistorySidebarOpen(!historySidebarOpen)}
              className="dark:hover:bg-[#111827]"
            >
              {historySidebarOpen ? <ChevronLeft className="h-5 w-5" /> : <Clock className="h-5 w-5" />}
              <span className="sr-only">{historySidebarOpen ? "Close History" : "Open History"}</span>
            </Button>
          </div>
        </div>

        {/* Messages - This container will scroll */}
        <div className="flex-1 p-4 overflow-y-auto dark:bg-[#111827]">
          <div className="space-y-4 max-w-3xl mx-auto">
            {loading && filteredMessages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                <p className="text-muted-foreground">Loading messages...</p>
              </div>
            ) : selectedMessage ? (
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold">Message Details</h2>
                  <Badge
                    variant={selectedMessage.status === "unread" ? "default" : "outline"}
                    className="ml-2"
                  >
                    {selectedMessage.status === "unread" ? (
                      <Circle className="h-3 w-3 mr-1" />
                    ) : (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    )}
                    {selectedMessage.status.charAt(0).toUpperCase() + selectedMessage.status.slice(1)}
                  </Badge>
                </div>

                {/* User Message */}
                <div className="bg-card p-5 rounded-lg border dark:border-gray-700 mb-6 animate-fade-in">
                  <div className="flex items-center mb-4">
                    <div className="flex h-10 w-10 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 text-primary">
                      <User className="h-5 w-5" />
                    </div>
                    <div className="ml-3">
                      <p className="font-medium">{selectedMessage.userEmail}</p>
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(selectedMessage.timestamp), "MMMM d, yyyy 'at' h:mm a")}
                      </p>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h3 className="font-medium mb-2 text-sm text-muted-foreground">Message:</h3>
                    <div className="bg-muted/30 p-3 rounded-md">
                      <p className="whitespace-pre-wrap">{selectedMessage.message}</p>
                    </div>
                  </div>
                </div>

                {/* Admin Response */}
                {selectedMessage.response && (
                  <div className="bg-card p-5 rounded-lg border dark:border-gray-700 mb-6 animate-fade-in">
                    <div className="flex items-center mb-4">
                      <div className="flex h-10 w-10 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 text-primary">
                        <MessagesSquare className="h-5 w-5" />
                      </div>
                      <div className="ml-3">
                        <p className="font-medium">Your Response</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedMessage.responseTimestamp
                            ? format(new Date(selectedMessage.responseTimestamp), "MMMM d, yyyy 'at' h:mm a")
                            : "No timestamp available"}
                        </p>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="font-medium mb-2 text-sm text-muted-foreground">Response:</h3>
                      <div className="bg-muted/30 p-3 rounded-md">
                        <p className="whitespace-pre-wrap">{selectedMessage.response}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-6">
                <div className="bg-muted/30 p-8 rounded-lg border border-dashed dark:border-gray-700 flex flex-col items-center max-w-md">
                  <MessagesSquare className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium">Select a Message</h3>
                  <p className="text-muted-foreground mt-2 text-center">
                    {loading
                      ? "Loading messages..."
                      : messages.length === 0
                        ? "No messages found. When users send messages using the 'Talk to Team' button, they will appear here."
                        : filteredMessages.length === 0
                          ? "No messages match your current filters. Try adjusting your search or filters."
                          : `Found ${filteredMessages.length} messages (${unreadCount} unread). Select a message from the list to view details.`
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Input Area */}
        {selectedMessage && (
          <div className="p-4">
            <div className="bg-card p-5 rounded-lg border dark:border-gray-700">
              <h3 className="font-medium mb-3">
                {selectedMessage.response ? "Send Another Response:" : "Respond to User:"}
              </h3>
              <Textarea
                placeholder="Type your response to the user here..."
                className="w-full min-h-[120px] mb-3"
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                disabled={isSubmitting}
              />
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  This response will be visible to the user in their chat interface.
                </p>
                <Button
                  onClick={handleSendResponse}
                  disabled={!responseText.trim() || isSubmitting}
                  className="px-4 dark:bg-[#843DF5] dark:hover:bg-[#843DF5]/90"
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  {selectedMessage.response ? "Send Another Response" : "Send Response"}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Sidebar Overlay */}
      {historySidebarOpen && (
        <div
          className="fixed inset-0 bg-white/60 dark:bg-[#111827]/60 backdrop-blur-sm z-20 lg:hidden"
          onClick={() => setHistorySidebarOpen(false)}
        />
      )}
    </div>
  )
}
