"use client";

import React, { useState, useMemo, useCallback, useTransition } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, ChevronLeft, Loader2, Trash2, CheckCircle2, X } from "lucide-react";
import { ConversationType } from "@/lib/models/conversation";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { debounce } from "@/lib/scan-utils";

interface OptimizedConversationHistoryProps {
  conversations: ConversationType[];
  activeConversationId: string | null;
  isOpen: boolean;
  loading: boolean;
  historyLoaded: boolean;
  onClose: () => void;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onConfirmDelete: (id: string) => void;
  onCancelDelete: () => void;
  deleteConfirmationId: string | null;
  deletingConversations: string[];
  onLoadHistory: () => void;
}

const CONVERSATIONS_PER_PAGE = 15;

// Memoized conversation item component
const ConversationItem = React.memo(({
  conversation,
  isActive,
  isDeleting,
  deleteConfirmationId,
  onClick,
  onDelete,
  onConfirmDelete,
  onCancelDelete
}: {
  conversation: ConversationType;
  isActive: boolean;
  isDeleting: boolean;
  deleteConfirmationId: string | null;
  onClick: () => void;
  onDelete: () => void;
  onConfirmDelete: () => void;
  onCancelDelete: () => void;
}) => {
  const preview = useMemo(() => {
    if (!conversation.messages || conversation.messages.length === 0) return "Empty conversation";
    const lastUserMsg = [...conversation.messages].reverse().find(m => m.role === "user" && typeof m.content === 'string');
    if (lastUserMsg && typeof lastUserMsg.content === 'string') {
      return lastUserMsg.content.length > 40 ? `${lastUserMsg.content.substring(0, 40)}...` : lastUserMsg.content;
    }
    return "Conversation started";
  }, [conversation.messages]);

  const formattedDate = useMemo(() => 
    format(new Date(conversation.createdAt), "MMM d, h:mm a"), 
    [conversation.createdAt]
  );

  return (
    <div 
      className={cn(
        "p-3 rounded-lg cursor-pointer hover:bg-accent group relative card-hover min-h-[4.5rem] transition-all duration-200",
        isActive && "bg-accent",
        isDeleting && "opacity-50 pointer-events-none"
      )} 
      onClick={onClick}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1 pr-6 overflow-hidden">
          <h6 className="font-medium text-xs break-words line-clamp-2">{conversation.title}</h6>
          <p className="text-xs text-muted-foreground truncate mt-1">{preview}</p>
          <p className="text-xs text-muted-foreground mt-1">{formattedDate}</p>
        </div>
        
        {deleteConfirmationId === conversation.id ? (
          <div className="absolute right-1 top-1 flex space-x-1 bg-background/80 backdrop-blur-sm rounded-full p-0.5 shadow-sm border z-10">
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 w-7 p-0 text-green-500 rounded-full hover:bg-green-500/10" 
              onClick={(e) => { e.stopPropagation(); onConfirmDelete(); }}
            >
              <CheckCircle2 className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 w-7 p-0 text-destructive rounded-full hover:bg-destructive/10" 
              onClick={(e) => { e.stopPropagation(); onCancelDelete(); }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : isDeleting ? (
          <div className="absolute right-1 top-1 p-1 bg-background/80 backdrop-blur-sm rounded-full shadow-sm border z-10">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <Button 
            variant="outline" 
            size="sm" 
            className="absolute right-1 top-1 h-7 w-7 p-0 opacity-0 group-hover:opacity-100 rounded-full hover:text-destructive transition-all duration-200 hover:bg-destructive/10 z-10" 
            onClick={(e) => { e.stopPropagation(); onDelete(); }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
});

ConversationItem.displayName = 'ConversationItem';

export const OptimizedConversationHistory = React.memo(({
  conversations,
  activeConversationId,
  isOpen,
  loading,
  historyLoaded,
  onClose,
  onSelectConversation,
  onDeleteConversation,
  onConfirmDelete,
  onCancelDelete,
  deleteConfirmationId,
  deletingConversations,
  onLoadHistory
}: OptimizedConversationHistoryProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [visibleCount, setVisibleCount] = useState(CONVERSATIONS_PER_PAGE);
  const [isPending, startTransition] = useTransition();

  // Debounced search
  const debouncedSetSearch = useCallback(
    debounce((query: string) => {
      startTransition(() => {
        setDebouncedSearchQuery(query);
        setVisibleCount(CONVERSATIONS_PER_PAGE); // Reset pagination on search
      });
    }, 300),
    []
  );

  React.useEffect(() => {
    debouncedSetSearch(searchQuery);
  }, [searchQuery, debouncedSetSearch]);

  // Optimized filtering
  const filteredConversations = useMemo(() => {
    let filtered = conversations.filter(conv => !deletingConversations.includes(conv.id));
    
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase();
      filtered = filtered.filter(conv => 
        conv.title.toLowerCase().includes(query) || 
        conv.messages.some(msg => 
          msg.role === "user" && 
          typeof msg.content === 'string' && 
          msg.content.toLowerCase().includes(query)
        )
      );
    }
    
    return filtered;
  }, [conversations, deletingConversations, debouncedSearchQuery]);

  // Paginated conversations
  const visibleConversations = useMemo(() => 
    filteredConversations.slice(0, visibleCount),
    [filteredConversations, visibleCount]
  );

  const hasMore = visibleCount < filteredConversations.length;

  const loadMore = useCallback(() => {
    setVisibleCount(prev => Math.min(prev + CONVERSATIONS_PER_PAGE, filteredConversations.length));
  }, [filteredConversations.length]);

  // Lazy load conversations when sidebar opens
  React.useEffect(() => {
    if (isOpen && !historyLoaded && !loading) {
      console.log("Loading conversation history on sidebar open");
      onLoadHistory();
    }
  }, [isOpen, historyLoaded, loading, onLoadHistory]);

  return (
    <div className={cn(
      "absolute inset-y-0 left-0 z-30 w-full sm:w-80 bg-white/90 dark:bg-[#111827]/90 backdrop-blur-md border-r transform transition-transform duration-300 ease-in-out",
      isOpen ? "translate-x-0" : "-translate-x-full",
      // Add pointer-events-none when closed to prevent interaction with hidden element
      !isOpen && "pointer-events-none"
    )}>
      <div className="flex flex-col h-full dark:bg-[#111827]">
        {/* Header */}
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="font-semibold">Chats</h3>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onClose} 
            className="dark:hover:bg-[#111827]"
          >
            <ChevronLeft className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        {/* Search */}
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Search history..." 
              className="pl-8 rounded-full h-9" 
              value={searchQuery} 
              onChange={(e) => setSearchQuery(e.target.value)} 
            />
            {isPending && (
              <div className="absolute right-2.5 top-2.5">
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              </div>
            )}
          </div>
        </div>

        {/* Conversations List */}
        <ScrollArea className="flex-1 dark:bg-[#111827] chat-scrollbar">
          <div className="p-2 space-y-1">
            {!historyLoaded ? (
              <div className="flex flex-col items-center justify-center py-8">
                <p className="text-sm text-muted-foreground mb-2">History not loaded</p>
                <Button variant="outline" size="sm" onClick={onLoadHistory}>
                  Load History
                </Button>
              </div>
            ) : loading && visibleConversations.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-primary mb-2" />
                <p className="text-sm text-muted-foreground">Loading history...</p>
              </div>
            ) : visibleConversations.length === 0 && !debouncedSearchQuery ? (
              <p className="text-center text-muted-foreground p-4 text-sm">No conversations yet.</p>
            ) : visibleConversations.length === 0 && debouncedSearchQuery ? (
              <p className="text-center text-muted-foreground p-4 text-sm">No matching history.</p>
            ) : (
              <>
                {visibleConversations.map(conv => (
                  <ConversationItem
                    key={conv.id}
                    conversation={conv}
                    isActive={activeConversationId === conv.id}
                    isDeleting={deletingConversations.includes(conv.id)}
                    deleteConfirmationId={deleteConfirmationId}
                    onClick={() => {
                      if (!deletingConversations.includes(conv.id)) {
                        onSelectConversation(conv.id);
                        onClose();
                      }
                    }}
                    onDelete={() => onDeleteConversation(conv.id)}
                    onConfirmDelete={() => onConfirmDelete(conv.id)}
                    onCancelDelete={onCancelDelete}
                  />
                ))}
                
                {/* Load More Button */}
                {hasMore && (
                  <div className="p-2 text-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={loadMore}
                      className="text-xs"
                    >
                      Load {Math.min(CONVERSATIONS_PER_PAGE, filteredConversations.length - visibleCount)} more
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
});

OptimizedConversationHistory.displayName = 'OptimizedConversationHistory';