"use client"

import * as React from "react" // Add React import
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Shield, User, Settings, LogOut } from "lucide-react"
import { useAuth } from "@/hooks/useAuth" // Import useAuth
import { signOut } from "firebase/auth" // Import signOut
import { auth } from "@/lib/firebase" // Import auth instance
import { useRouter } from "next/navigation" // Import useRouter
import Cookies from 'js-cookie'; // Import js-cookie

export function UserNav() {
  const { user, role } = useAuth(); // Get user and role from hook
  const router = useRouter(); // Get router instance
  const [isOpen, setIsOpen] = React.useState(false); // Add state for dropdown
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close dropdown
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        setIsOpen(false);
      }
    };

    // Add event listener when dropdown is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleLogout = async () => {
    try {
      // Remove the role cookie
      Cookies.remove('user_role', { path: '/' });

      // Sign out from Firebase
      await signOut(auth);
      router.push("/login"); // Redirect to login after sign out
    } catch (error) {
      console.error("Error signing out: ", error);
      // Optionally show an error message to the user
    }
  };

  // Don't render the component if user data isn't loaded yet
  if (!user) {
    // You might want a loading state or skeleton here instead of null
    // depending on how useAuth handles loading states
    return null;
  }

  return (
    <div ref={dropdownRef} className="relative">
      <Button
        variant="ghost"
        className="relative flex items-center gap-2 rounded-md pr-2 pl-1 dark:hover:bg-[#111827]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Avatar className="h-8 w-8 border-2 border-primary/20">
          <AvatarFallback className="flex items-center justify-center">
            <User className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
        <span className="hidden md:inline-block text-sm font-medium">
          {user.displayName || (user.email ? user.email.split('@')[0] : "User")}
        </span>
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-lg border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 z-50">
          {/* User info */}
          <div className="px-2 py-1.5 font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
              <div className="mt-1.5 text-xs bg-primary/10 text-primary rounded-sm px-1.5 py-0.5 inline-block">
                {role === 'admin' ? 'Administrator' :
                 role === 'manager' ? 'Team Manager' : 'Client User'}
              </div>
            </div>
          </div>

          {/* Separator */}
          <div className="-mx-1 my-1 h-px bg-muted"></div>

          {/* Menu items */}
          <div className="group">
            {/* Settings */}
            <div
              className="relative flex cursor-pointer select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground dark:hover:bg-[#111827]"
              onClick={() => {
                setIsOpen(false);
                router.push("/settings");
              }}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </div>
          </div>

          {/* Separator */}
          <div className="-mx-1 my-1 h-px bg-muted"></div>

          {/* Logout */}
          <div
            className="relative flex cursor-pointer select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground"
            onClick={() => {
              setIsOpen(false);
              handleLogout();
            }}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </div>
        </div>
      )}
    </div>
  )
}
