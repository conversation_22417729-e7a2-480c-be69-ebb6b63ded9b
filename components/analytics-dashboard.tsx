"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs" // Added Tabs
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts"
import { ShieldAlert, Shield, Download, Calendar, Clock, CheckCircle2, BarChart3, Filter, CalendarDays, User, Users } from "lucide-react" // Added User, Users
import { useScans } from "@/context/ScanContext"
import { useVulnerabilities } from "@/context/VulnerabilitiesContext"
import { useState, useMemo, useEffect, useCallback, useRef, useDeferredValue, useTransition, startTransition } from "react"
import { VulnerabilityFilter } from "@/types/vulnerability-types"; // Added import for VulnerabilityFilter

interface AnalyticsDashboardProps {
  initialViewMode?: "personal" | "organization"; // Renamed to initialViewMode and made optional
}

export function AnalyticsDashboard({ initialViewMode = "personal" }: AnalyticsDashboardProps) {
  const {
    scans: contextScans,
    organizationData: contextOrgData,
    isOrganizationView: contextIsScanOrgView, // Renamed for clarity
    fetchData
  } = useScans();
  
  const {
    vulnerabilities: contextVulnerabilities,
    setFilters,
    fetchVulnerabilities: baseFetchVulnerabilities, // Renamed for clarity
    isOrganizationView: contextIsVulnerabilitiesOrgView
  } = useVulnerabilities();

  const [currentViewMode, setCurrentViewMode] = useState<"personal" | "organization">(initialViewMode);
  const [timeFilter, setTimeFilter] = useState("30"); // Default to 30 days
  const [vulnerabilityStatusFilter, setVulnerabilityStatusFilter] = useState("all"); // Default to all
  const [isExporting, setIsExporting] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isPending, startTransition] = useTransition();
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Defer heavy computations
  const deferredScans = useDeferredValue(contextScans);
  const deferredVulnerabilities = useDeferredValue(contextVulnerabilities);

  const handleViewModeChange = useCallback(async (newMode: "personal" | "organization") => {
    if (newMode === currentViewMode) return;
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    startTransition(() => {
      setCurrentViewMode(newMode);
      setIsLoadingData(true);
    });
    
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        if (newMode === "personal") {
          await Promise.all([
            fetchData("personal", true),
            baseFetchVulnerabilities(false)
          ]);
        } else {
          await Promise.all([
            fetchData("organization", true),
            baseFetchVulnerabilities(true)
          ]);
        }
      } catch (error) {
        console.error("Error switching view mode:", error);
      } finally {
        startTransition(() => setIsLoadingData(false));
      }
    }, 300);
  }, [currentViewMode, fetchData, baseFetchVulnerabilities]);

  // This effect ensures that when the component mounts or initialViewMode changes,
  // the correct data is fetched if not already aligned with the contexts.
  useEffect(() => {
    const alignDataWithViewMode = async () => {
      setIsLoadingData(true);
      try {
        if (currentViewMode === "personal") {
          // If ScanContext is in org view OR VulnerabilitiesContext is in org view, refetch personal
          if (contextIsScanOrgView || contextIsVulnerabilitiesOrgView) {
            await fetchData("personal", true);
            await baseFetchVulnerabilities(false);
          }
        } else { // currentViewMode === "organization"
          // If ScanContext is NOT in org view OR VulnerabilitiesContext is NOT in org view, refetch org
          if (!contextIsScanOrgView || !contextIsVulnerabilitiesOrgView) {
            await fetchData("organization", true);
            await baseFetchVulnerabilities(true);
          }
        }
      } catch (error) {
        console.error("Error aligning data with view mode:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    // Only run if the view mode determined by contexts doesn't match the current dashboard view mode
    if (currentViewMode === "personal" && (contextIsScanOrgView || contextIsVulnerabilitiesOrgView)) {
        alignDataWithViewMode();
    } else if (currentViewMode === "organization" && (!contextIsScanOrgView || !contextIsVulnerabilitiesOrgView)) {
        alignDataWithViewMode();
    }
  }, [currentViewMode, contextIsScanOrgView, contextIsVulnerabilitiesOrgView, fetchData, baseFetchVulnerabilities]);


  const dataSource = useMemo(() => {
    const dashboardWantsOrg = currentViewMode === "organization";
    const scansContextIsInOrgMode = contextIsScanOrgView;
    const vulnerabilitiesContextIsInOrgMode = contextIsVulnerabilitiesOrgView;

    if (dashboardWantsOrg && scansContextIsInOrgMode && vulnerabilitiesContextIsInOrgMode) {
      return {
        scans: deferredScans,
        vulnerabilities: deferredVulnerabilities,
        isOrgData: true,
      };
    } else if (!dashboardWantsOrg && !scansContextIsInOrgMode && !vulnerabilitiesContextIsInOrgMode) {
      return {
        scans: deferredScans,
        vulnerabilities: deferredVulnerabilities,
        isOrgData: false,
      };
    }

    return {
      scans: [],
      vulnerabilities: [],
      isOrgData: dashboardWantsOrg,
    };
  }, [currentViewMode, contextIsScanOrgView, contextIsVulnerabilitiesOrgView, deferredScans, deferredVulnerabilities]);
  
  // Calculate filtered scans based on time filter
  const filteredScans = useMemo(() => {
    if (!dataSource.scans.length) return [];

    const now = new Date();
    const filterDays = parseInt(timeFilter);
    const cutoffDate = new Date(now.setDate(now.getDate() - filterDays));

    return dataSource.scans.filter(scan => new Date(scan.requestedAt) >= cutoffDate);
  }, [dataSource.scans, timeFilter]);

  const filteredVulnerabilities = useMemo(() => {
    let vulnerabilitiesToFilter = dataSource.vulnerabilities;

    // Time filter
    const now = new Date();
    const filterDays = parseInt(timeFilter);
    const cutoffDate = new Date(now.setDate(now.getDate() - filterDays));
    vulnerabilitiesToFilter = vulnerabilitiesToFilter.filter(v => new Date(v.createdAt) >= cutoffDate);

    // Status filter
    if (vulnerabilityStatusFilter !== "all") {
      vulnerabilitiesToFilter = vulnerabilitiesToFilter.filter(v => v.status === vulnerabilityStatusFilter);
    }

    return vulnerabilitiesToFilter;
  }, [dataSource.vulnerabilities, timeFilter, vulnerabilityStatusFilter]);

  // Calculate statistics
  const stats = useMemo(() => {
    const criticalVulnerabilities = filteredVulnerabilities.filter(v => v.severity === 'critical').length;
    const highVulnerabilities = filteredVulnerabilities.filter(v => v.severity === 'high').length;
    const mediumVulnerabilities = filteredVulnerabilities.filter(v => v.severity === 'medium').length;
    const lowVulnerabilities = filteredVulnerabilities.filter(v => v.severity === 'low').length;
    const totalVulnerabilities = filteredVulnerabilities.length;

    if (!filteredScans.length) {
      return {
        totalScans: 0,
        pendingScans: 0,
        inProgressScans: 0,
        reTestScans: 0,
        completedScans: 0,
        criticalVulnerabilities,
        highVulnerabilities,
        mediumVulnerabilities,
        lowVulnerabilities,
        totalVulnerabilities,
        remediationRate: 0,
        averageResponseTime: 0
      };
    }

    const pendingScans = filteredScans.filter(scan => scan.status === "pending").length;
    const inProgressScans = filteredScans.filter(scan => scan.status === "in-progress").length;
    const reTestScans = filteredScans.filter(scan => scan.status === "re-test").length;
    const completedScans = filteredScans.filter(scan => scan.status === "completed").length;

    // Calculate remediation rate (completed scans / total scans)
    const remediationRate = filteredScans.length > 0 ? (completedScans / filteredScans.length) * 100 : 0;

    // Calculate average response time (hours between request and completion)
    let totalResponseTime = 0;
    let scansWithCompletionTime = 0;

    filteredScans.forEach(scan => {
      try {
        if (scan.completedAt && scan.requestedAt) {
          // Ensure we're working with Date objects
          const requestDate = scan.requestedAt instanceof Date ? scan.requestedAt : new Date(scan.requestedAt);
          const completionDate = scan.completedAt instanceof Date ? scan.completedAt : new Date(scan.completedAt);

          // Only calculate if both dates are valid
          if (!isNaN(requestDate.getTime()) && !isNaN(completionDate.getTime())) {
            const responseTimeHours = (completionDate.getTime() - requestDate.getTime()) / (1000 * 60 * 60);

            // Only count positive response times
            if (responseTimeHours > 0) {
              totalResponseTime += responseTimeHours;
              scansWithCompletionTime++;
            }
          }
        }
      } catch (error) {
        console.error("Error calculating response time for pentest:", scan.id, error);
      }
    });

    const averageResponseTime = scansWithCompletionTime > 0 ? totalResponseTime / scansWithCompletionTime : 0;

    return {
      totalScans: filteredScans.length,
      pendingScans,
      inProgressScans,
      reTestScans,
      completedScans,
      criticalVulnerabilities,
      highVulnerabilities,
      mediumVulnerabilities,
      lowVulnerabilities,
      totalVulnerabilities,
      remediationRate,
      averageResponseTime
    };
  }, [filteredScans, filteredVulnerabilities]);

  // Create severity distribution data for pie chart
  const severityDistributionData = useMemo(() => {
    return [
      { name: "Critical", value: stats.criticalVulnerabilities, color: "#ef4444" },
      { name: "High", value: stats.highVulnerabilities, color: "#f97316" },
      { name: "Medium", value: stats.mediumVulnerabilities, color: "#eab308" },
      { name: "Low", value: stats.lowVulnerabilities, color: "#3b82f6" },
    ].filter(item => item.value > 0); // Only include non-zero values
  }, [stats]);

  // Memoized chart components
  const MemoizedPieChart = useMemo(() => {
    if (isLoadingData || stats.totalVulnerabilities === 0) return null;
    
    return (
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <defs>
            <linearGradient id="criticalGradient" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#ef4444" stopOpacity={0.8}/>
              <stop offset="100%" stopColor="#dc2626" stopOpacity={1}/>
            </linearGradient>
            <linearGradient id="highGradient" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#f97316" stopOpacity={0.8}/>
              <stop offset="100%" stopColor="#ea580c" stopOpacity={1}/>
            </linearGradient>
            <linearGradient id="mediumGradient" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#eab308" stopOpacity={0.8}/>
              <stop offset="100%" stopColor="#ca8a04" stopOpacity={1}/>
            </linearGradient>
            <linearGradient id="lowGradient" x1="0" y1="0" x2="1" y2="1">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8}/>
              <stop offset="100%" stopColor="#2563eb" stopOpacity={1}/>
            </linearGradient>
          </defs>
          <Pie
            data={severityDistributionData}
            cx="50%"
            cy="50%"
            innerRadius={65}
            outerRadius={95}
            paddingAngle={4}
            dataKey="value"
            animationBegin={0}
            animationDuration={800}
          >
            {severityDistributionData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={
                  entry.name === 'Critical' ? 'url(#criticalGradient)' :
                  entry.name === 'High' ? 'url(#highGradient)' :
                  entry.name === 'Medium' ? 'url(#mediumGradient)' :
                  'url(#lowGradient)'
                }
              />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  }, [severityDistributionData, isLoadingData, stats.totalVulnerabilities]);

  // Generate monthly vulnerability data
  const monthlyVulnerabilityData = useMemo(() => {
    // Define interface for month data
    interface MonthData {
      date?: Date;
      name: string;
      Critical: number;
      High: number;
      Medium: number;
      Low: number;
    }

    // If no scans, return empty months with zero values
    if (!filteredScans.length) {
      // Calculate number of months to display based on time filter
      const filterDays = parseInt(timeFilter);
      // Approximate number of months (30 days per month)
      let monthsToShow = Math.max(Math.ceil(filterDays / 30), 1);
      // Cap at 12 months for readability
      monthsToShow = Math.min(monthsToShow, 12);

      // Get current date and calculate the past N months with zero values
      const now = new Date();
      const emptyMonths: MonthData[] = [];

      for (let i = monthsToShow - 1; i >= 0; i--) {
        const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
        emptyMonths.push({
          date: month,
          name: month.toLocaleString('default', { month: 'short' }) +
                (month.getFullYear() !== now.getFullYear() ? ' ' + month.getFullYear().toString().slice(2, 4) : ''),
          Critical: 0,
          High: 0,
          Medium: 0,
          Low: 0
        });
      }

      return emptyMonths;
    }

    // Calculate number of months to display based on time filter
    const filterDays = parseInt(timeFilter);
    // Approximate number of months (30 days per month)
    let monthsToShow = Math.max(Math.ceil(filterDays / 30), 1);

    // Cap at 12 months for readability
    monthsToShow = Math.min(monthsToShow, 12);

    // Get current date and calculate the past N months
    const now = new Date();
    const months: MonthData[] = [];

    for (let i = monthsToShow - 1; i >= 0; i--) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        date: month,
        name: month.toLocaleString('default', { month: 'short' }) +
              (month.getFullYear() !== now.getFullYear() ? ' ' + month.getFullYear().toString().slice(2, 4) : ''),
        Critical: 0,
        High: 0,
        Medium: 0,
        Low: 0
      });
    }

    // Group scans by month and count vulnerabilities
    filteredScans.forEach(scan => {
      // Skip scans without vulnerability data
      if (scan.criticalVulnerabilities === undefined &&
          scan.highVulnerabilities === undefined &&
          scan.mediumVulnerabilities === undefined &&
          scan.lowVulnerabilities === undefined) {
        return;
      }

      // Use completedAt or requestedAt date
      const scanDate = scan.completedAt ? new Date(scan.completedAt) : new Date(scan.requestedAt);

      // Find the matching month
      const monthIndex = months.findIndex(m =>
        m.date &&
        m.date.getMonth() === scanDate.getMonth() &&
        m.date.getFullYear() === scanDate.getFullYear()
      );

      if (monthIndex !== -1) {
        months[monthIndex].Critical += scan.criticalVulnerabilities || 0;
        months[monthIndex].High += scan.highVulnerabilities || 0;
        months[monthIndex].Medium += scan.mediumVulnerabilities || 0;
        months[monthIndex].Low += scan.lowVulnerabilities || 0;
      }
    });

    return months;
  }, [filteredScans, filteredVulnerabilities]);

  // Function to export dashboard data as CSV
  const exportDashboardData = () => {
    setIsExporting(true);

    try {
      // Create CSV content
      let csvContent = "data:text/csv;charset=utf-8,";

      // Add dashboard summary
      csvContent += "Security Dashboard Summary\n";
      csvContent += `Time Period,${timeFilter === "30" ? "Last 30 Days" : timeFilter === "90" ? "Last 90 Days" : "Last Year"}\n`;
      csvContent += `Export Date,${new Date().toLocaleDateString()}\n\n`;

      // Add scan statistics
      csvContent += "Scan Statistics\n";
      csvContent += "Metric,Value\n";
      csvContent += `Total Scans,${stats.totalScans}\n`;
      csvContent += `Pending Scans,${stats.pendingScans}\n`;
      csvContent += `In Progress Scans,${stats.inProgressScans}\n`;
      csvContent += `Re-test Scans,${stats.reTestScans}\n`;
      csvContent += `Completed Scans,${stats.completedScans}\n`;
      csvContent += `Remediation Rate,${stats.remediationRate.toFixed(1)}%\n`;
      csvContent += `Average Response Time,${stats.averageResponseTime.toFixed(1)} hours\n\n`;

      // Add vulnerability statistics
      csvContent += "Vulnerability Statistics\n";
      csvContent += "Severity,Count,Percentage\n";
      if (stats.totalVulnerabilities > 0) {
        csvContent += `Critical,${stats.criticalVulnerabilities},${((stats.criticalVulnerabilities / stats.totalVulnerabilities) * 100).toFixed(1)}%\n`;
        csvContent += `High,${stats.highVulnerabilities},${((stats.highVulnerabilities / stats.totalVulnerabilities) * 100).toFixed(1)}%\n`;
        csvContent += `Medium,${stats.mediumVulnerabilities},${((stats.mediumVulnerabilities / stats.totalVulnerabilities) * 100).toFixed(1)}%\n`;
        csvContent += `Low,${stats.lowVulnerabilities},${((stats.lowVulnerabilities / stats.totalVulnerabilities) * 100).toFixed(1)}%\n`;
        csvContent += `Total,${stats.totalVulnerabilities},100%\n\n`;
      } else {
        csvContent += "No vulnerabilities found in the selected time period\n\n";
      }

      // Add monthly vulnerability data
      csvContent += "Monthly Vulnerability Trends\n";
      csvContent += "Month,Critical,High,Medium,Low,Total\n";
      monthlyVulnerabilityData.forEach(month => {
        const monthTotal = month.Critical + month.High + month.Medium + month.Low;
        csvContent += `${month.name},${month.Critical},${month.High},${month.Medium},${month.Low},${monthTotal}\n`;
      });

      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `security-dashboard-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);

      // Trigger download
      link.click();

      // Clean up
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error exporting dashboard data:", error);
      // You could add a toast notification here for error feedback
    } finally {
      setIsExporting(false);
    }
  };

  useEffect(() => {
    const newStatusArray = vulnerabilityStatusFilter === 'all' ? [] : [vulnerabilityStatusFilter];
    // Create new filter object
    const newFilters: VulnerabilityFilter = {
      status: newStatusArray
    };
    setFilters(newFilters);
  }, [vulnerabilityStatusFilter, setFilters]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="p-6 animate-fade-in dark:bg-[#111827]">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-8">
        <div className="space-y-1">
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-3 whitespace-nowrap">
            <div className="p-2 rounded-xl bg-primary/10 border border-primary/20">
              <BarChart3 className="h-6 w-6 text-primary" />
            </div>
            {currentViewMode === "organization" ? "Organization Dashboard" : "Personal Dashboard"}
          </h2>
          {isLoadingData && (
             <p className="text-sm text-primary animate-pulse">Loading data for {currentViewMode} view...</p>
          )}
        </div>
        
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:justify-end w-full">
          {/* View Mode Toggle */}
          <Tabs value={currentViewMode} onValueChange={(value: string) => handleViewModeChange(value as "personal" | "organization")}>
            <TabsList className="grid w-fit grid-cols-2 h-9 rounded-lg border-2 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-200 focus:ring-2 focus:ring-primary/20">
              <TabsTrigger value="personal" className="flex items-center gap-2 text-xs sm:text-sm" disabled={isLoadingData}>
                <User className="h-3.5 w-3.5 sm:h-4 sm:w-4" /> Personal
              </TabsTrigger>
              <TabsTrigger value="organization" className="flex items-center gap-2 text-xs sm:text-sm" disabled={isLoadingData}>
                <Users className="h-3.5 w-3.5 sm:h-4 sm:w-4" /> Organisation
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Time Period Filter */}
          <div className="flex items-center gap-3">
            <div className="hidden 2xl:flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <CalendarDays className="h-4 w-4" />
              Time Period
            </div>
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-[155px] h-9 rounded-lg border-2 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent className="rounded-lg border-2">
                <SelectItem value="30" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Last 30 Days
                  </div>
                </SelectItem>
                <SelectItem value="90" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Last 90 Days
                  </div>
                </SelectItem>
                <SelectItem value="365" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Last Year
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Vulnerability Status Filter */}
          <div className="flex items-center gap-3">
            <div className="hidden 2xl:flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <Filter className="h-4 w-4" />
              Status
            </div>
            <Select value={vulnerabilityStatusFilter} onValueChange={setVulnerabilityStatusFilter}>
              <SelectTrigger className="w-[165px] h-9 rounded-lg border-2 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-200 focus:ring-2 focus:ring-primary/20">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="rounded-lg border-2">
                <SelectItem value="all" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-slate-500"></div>
                    All Status
                  </div>
                </SelectItem>
                <SelectItem value="open" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500"></div>
                    Open
                  </div>
                </SelectItem>
                <SelectItem value="closed" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    Closed
                  </div>
                </SelectItem>
                <SelectItem value="pending-retest" className="rounded-md">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                    Pending Retest
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Export Button */}
          <Button
            variant="outline"
            size="sm"
            className="rounded-lg border-2 bg-background/50 backdrop-blur-sm hover:bg-background/80 transition-all duration-200 gap-2"
            onClick={exportDashboardData}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <svg className="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Export Data
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 hover:border-primary/20">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="flex flex-row items-center justify-between pb-3 relative z-10">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Pentests</CardTitle>
            <div className="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 group-hover:scale-110 transition-transform duration-300">
              <Shield className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-3xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {isLoadingData ? <span className="text-sm animate-pulse">Loading...</span> : stats.totalScans}
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center gap-2 text-xs p-2 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                <span className="font-medium">Pending: {stats.pendingScans}</span>
              </div>
              <div className="flex items-center gap-2 text-xs p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                <span className="font-medium">Progress: {stats.inProgressScans}</span>
              </div>
              <div className="flex items-center gap-2 text-xs p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
                <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                <span className="font-medium">Re-test: {stats.reTestScans}</span>
              </div>
              <div className="flex items-center gap-2 text-xs p-2 rounded-lg bg-green-500/10 border border-green-500/20">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <span className="font-medium">Done: {stats.completedScans}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-red-500/5 transition-all duration-300 hover:border-red-500/20">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="flex flex-row items-center justify-between pb-3 relative z-10">
            <CardTitle className="text-sm font-medium text-muted-foreground">Critical Vulnerabilities</CardTitle>
            <div className="p-2 rounded-xl bg-gradient-to-br from-red-500/20 to-red-500/10 border border-red-500/20 group-hover:scale-110 transition-transform duration-300">
              <ShieldAlert className="h-4 w-4 text-red-500" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-3xl font-bold mb-4 bg-gradient-to-r from-red-500 to-red-400 bg-clip-text text-transparent">
              {isLoadingData ? <span className="text-sm animate-pulse">Loading...</span> : stats.criticalVulnerabilities}
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Total Vulnerabilities</span>
                <span className="font-medium">{stats.totalVulnerabilities}</span>
              </div>
              <Progress
                value={stats.totalVulnerabilities > 0 ? (stats.criticalVulnerabilities / stats.totalVulnerabilities) * 100 : 0}
                className="h-2 bg-red-500/10"
                indicatorClassName="bg-gradient-to-r from-red-500 to-red-400"
              />
              <div className="text-xs text-muted-foreground text-center">
                <span className="font-medium text-red-500">
                  {stats.totalVulnerabilities > 0 ? ((stats.criticalVulnerabilities / stats.totalVulnerabilities) * 100).toFixed(1) : 0}% of total
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300 hover:border-green-500/20">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="flex flex-row items-center justify-between pb-3 relative z-10">
            <CardTitle className="text-sm font-medium text-muted-foreground">Remediation Rate</CardTitle>
            <div className="p-2 rounded-xl bg-gradient-to-br from-green-500/20 to-green-500/10 border border-green-500/20 group-hover:scale-110 transition-transform duration-300">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-3xl font-bold mb-4 bg-gradient-to-r from-green-500 to-green-400 bg-clip-text text-transparent">
              {isLoadingData ? <span className="text-sm animate-pulse">Loading...</span> : `${stats.remediationRate.toFixed(1)}%`}
            </div>
            <div className="space-y-2">
              <Progress
                value={stats.remediationRate}
                className="h-2 bg-green-500/10"
                indicatorClassName="bg-gradient-to-r from-green-500 to-green-400"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Completed</span>
                <span className="font-medium text-green-500">{stats.completedScans} of {stats.totalScans}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300 hover:border-blue-500/20">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="flex flex-row items-center justify-between pb-3 relative z-10">
            <CardTitle className="text-sm font-medium text-muted-foreground">Average Response Time</CardTitle>
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-500/10 border border-blue-500/20 group-hover:scale-110 transition-transform duration-300">
              <Clock className="h-4 w-4 text-blue-500" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-blue-400 bg-clip-text text-transparent">
              {isLoadingData ? <span className="text-sm animate-pulse">Loading...</span> : `${stats.averageResponseTime.toFixed(1)}h`}
            </div>
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-2 p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <Clock className="h-3 w-3 text-blue-500" />
                <span>Request to completion time</span>
              </div>
              <div className="flex items-center gap-2 p-2 rounded-lg bg-green-500/10 border border-green-500/20">
                <CheckCircle2 className="h-3 w-3 text-green-500" />
                <span className="font-medium">{stats.completedScans} completed pentests</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 grid gap-6 md:grid-cols-2">
        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 hover:border-primary/20">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="relative z-10">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20">
                <ShieldAlert className="h-4 w-4 text-primary" />
              </div>
              Vulnerability Severity
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Distribution of vulnerabilities by severity level
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80 relative z-10">
            {isLoadingData ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground animate-pulse">Loading chart data...</p>
              </div>
            ) : MemoizedPieChart ? MemoizedPieChart : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-3">
                  <div className="p-3 rounded-full bg-muted/50 w-fit mx-auto">
                    <ShieldAlert className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-medium text-muted-foreground">No vulnerabilities found</p>
                    <p className="text-sm text-muted-foreground/70">in the selected time period</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="group relative overflow-hidden border-2 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 hover:border-primary/20">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <CardHeader className="relative z-10">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20">
                <BarChart3 className="h-4 w-4 text-primary" />
              </div>
              Vulnerability Trends
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              Monthly vulnerability trends by severity
            </CardDescription>
          </CardHeader>
          <CardContent className="h-80 relative z-10">
            {isLoadingData ? (
               <div className="flex items-center justify-center h-full">
                 <p className="text-muted-foreground animate-pulse">Loading chart data...</p>
               </div>
            ) : stats.totalVulnerabilities > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyVulnerabilityData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="2 4" opacity={0.15} />
                  <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="Critical" fill="#ef4444" radius={[2, 2, 0, 0]} animationDuration={600} />
                  <Bar dataKey="High" fill="#f97316" radius={[2, 2, 0, 0]} animationDuration={600} />
                  <Bar dataKey="Medium" fill="#eab308" radius={[2, 2, 0, 0]} animationDuration={600} />
                  <Bar dataKey="Low" fill="#3b82f6" radius={[2, 2, 0, 0]} animationDuration={600} />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-3">
                  <div className="p-3 rounded-full bg-muted/50 w-fit mx-auto">
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-medium text-muted-foreground">No vulnerability trends</p>
                    <p className="text-sm text-muted-foreground/70">in the selected time period</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

    </div>
  )
}
