"use client"

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo, ChangeEvent, KeyboardEvent } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Send, ShieldAlert, Bot, User as UserIcon, Plus, Search, Clock, ChevronLeft, Trash2, Loader2, Paperclip, FileText, Download, Bell, CheckCircle2, X, FileDown } from "lucide-react"; // Renamed User to UserIcon
import { cn } from "@/lib/utils";
import { MAX_FILE_SIZE, validateFileExtension, formatFileSize } from "@/lib/file-utils";
import { useScanCount } from "@/context/ScanCountContext";
import { ChatScanCard } from "@/components/chat-scan-card";
import { format } from "date-fns";
import { useAuth, User } // Assuming User type is exported from useAuth
    from "@/hooks/useAuth"; // Adjusted path
import { useScans } from "@/context/ScanContext";
import { useConversations } from "@/context/ConversationsContext";
import { useMessageQueue } from "@/context/MessageQueueContext";
import { toast } from "sonner";
import {
    ConversationType,
    MessageType,
    FileInfoType,
    MessageContent,
    ScanCardContent,
    DownloadCardContent,
    AiResponseContent,
    FileUploadContent,
    SystemNotificationContent
} from "@/lib/models/conversation"; // Ensure this path is correct

// --- Constants ---
const API_EXTRACT_SCAN_FIELDS = "/api/extract-scan-fields";
const API_SCANS = "/api/scans";
const API_CONVERSATION_ASSOCIATE_SCAN = (conversationId: string) => `/api/conversations/${conversationId}/associate-scan`;

const TITLE_PENTEST_SCAN_REQUEST = "Pentest Request"; // Standardized title

// --- Internal Custom Hooks ---
const APP_TYPE_KEYWORDS = [
    'web application', 'mobile app', 'api', 'network', 'cloud infrastructure',
    'web app', 'website', 'mobile application', 'ios app', 'android app',
    'web', 'mobile', 'desktop', 'cloud', 'server', 'application'
];

function useScanInfoDetector(messages: MessageType[]) {
    const [scanInfoProvided, setScanInfoProvided] = useState({ appType: false, appName: false, appUrl: false });

    useEffect(() => {
        const hasScanCard = messages.some(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard);
        if (hasScanCard) { setScanInfoProvided({ appType: true, appName: true, appUrl: true }); return; } // If scan card, all info is considered present for UI purposes

        const assistantMessages = messages.filter(m => m.role === "assistant");
        const latestAssistantMessage = assistantMessages.length > 0 ? assistantMessages[assistantMessages.length - 1] : null;

        if (latestAssistantMessage && typeof latestAssistantMessage.content === 'object' && (latestAssistantMessage.content as AiResponseContent).scanInfoFlags) {
            const flags = (latestAssistantMessage.content as AiResponseContent).scanInfoFlags!;
            setScanInfoProvided({ appType: flags.appType, appName: flags.appName, appUrl: flags.appUrl });
            return;
        }

        const userMessages = messages.filter(m => m.role === "user" && typeof m.content === 'string');
        const hasSubmitKeyword = userMessages.some(m => typeof m.content === 'string' && /\bsubmit\b/i.test(m.content));

        const hasAppType = userMessages.some(m => {
            if (typeof m.content !== 'string') return false;
            const cl = m.content.toLowerCase();
            return APP_TYPE_KEYWORDS.some(kw => cl.includes(kw)) || /\b(asset|application)?\s*type\s*:/i.test(cl);
        });

        let appNameDetected = userMessages.some(m => {
            if (typeof m.content !== 'string') return false;
            const cl = m.content.toLowerCase();
            return /\b(app(lication)?|asset)?\s*name\s*:/i.test(cl) || /\b(called|named)\b/i.test(cl);
        });

        if (!appNameDetected) {
            for (const m of assistantMessages) {
                const mc = m.content;
                let text = typeof mc === 'string' ? mc : (mc && typeof mc === 'object' && 'text' in mc && typeof (mc as any).text === 'string') ? (mc as AiResponseContent).text : '';
                if (text) {
                    const tl = text.toLowerCase();
                    if (tl.includes('application name:') || tl.includes('asset name:')) {
                        const match = text.match(/\b(?:application|asset)\s+name\s*:\s*([^\n<]+)/i);
                        if (match && match[1]?.trim() && match[1].trim().toLowerCase() !== "unknown") { appNameDetected = true; break; }
                    }
                }
            }
        }

        const hasUrl = userMessages.some(m => {
            if (typeof m.content !== 'string') return false;
            const cl = m.content.toLowerCase();
            return /\b(url|target|domain|location|address)\s*:/i.test(cl) || /https?:\/\//i.test(cl) || /\b[a-z0-9][a-z0-9-]{1,61}[a-z0-9]\.[a-z]{2,}/i.test(cl);
        });

        setScanInfoProvided({
            appType: hasAppType || hasSubmitKeyword,
            appName: appNameDetected || hasSubmitKeyword,
            appUrl: hasUrl || hasSubmitKeyword
        });
    }, [messages]);

    const allScanInfoProvided = useMemo(() => scanInfoProvided.appType && scanInfoProvided.appUrl, [scanInfoProvided]);
    return { scanInfoProvided, allScanInfoProvided };
}

interface UseScanSubmissionProps {
    user: User | null;
    activeConversationId: string | null;
    activeConversation: ConversationType | null;
    messages: MessageType[];
    allScanInfoProvided: boolean;
    scanInfoProvided: { appType: boolean; appName: boolean; appUrl: boolean; };
    addMessageToConversation: (message: Omit<MessageType, "id" | "timestamp">) => Promise<string>;
    fetchData: (view: 'personal' | 'organization', forceRefresh?: boolean) => Promise<void>;
    updateConversationTitle: (id: string, title: string) => Promise<boolean>;
    isChatDisabled: boolean;
    role: 'client' | 'admin' | 'manager' | null;
}

function useScanSubmission({
    user, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided,
    addMessageToConversation, fetchData, updateConversationTitle, isChatDisabled, role
}: UseScanSubmissionProps) {
    const [isSubmittingScan, setIsSubmittingScan] = useState(false);

    const _extractFields = useCallback(async (idToken: string) => {
        const convText = messages.map(m => `${m.role}: ${prepareContentForAI(m.content)}`).join("\n\n");
        const res = await fetch(API_EXTRACT_SCAN_FIELDS, { method: "POST", headers: { "Content-Type": "application/json", 'Authorization': `Bearer ${idToken}` }, body: JSON.stringify({ conversation: convText }) });
        if (!res.ok) { const d = await res.json().catch(() => ({})); throw new Error(d.error || `Extract failed (${res.status})`); }
        const { fields } = await res.json();
        if (!fields || typeof fields !== 'object') throw new Error("Extraction failed: Invalid data.");
        return fields;
    }, [messages]);

    const _validatePayload = useCallback((fields: any, currentMessages: MessageType[], currentActiveConv: ConversationType | null) => {
        let assetType = fields.asset_type || fields['Scope definition'];
        if (!assetType) throw new Error("Could not determine asset type.");
        if (!fields.targetDetails || String(fields.targetDetails).trim() === '') {
            const targetMsg = currentMessages.find(m => m.role === "user" && typeof m.content === 'string' && (m.content.includes("target") || m.content.includes("url") || m.content.includes("http")));
            fields.targetDetails = targetMsg && typeof targetMsg.content === 'string' ? (targetMsg.content.match(/(https?:\/\/[^\s]+)|([a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,})/)?.[0] || `${assetType.replace(/_/g, ' ')} target`) : `${assetType.replace(/_/g, ' ')} target`;
        }
        const reqFields = ['targetDetails', 'function', 'contact_name', 'contact_email'];
        if (!reqFields.some(f => fields[f]?.trim())) throw new Error("Missing required scan details.");
        const files = currentActiveConv?.fileInfo?.map(f => ({ ...f, uploadedAt: typeof f.uploadedAt === 'string' ? f.uploadedAt : new Date(f.uploadedAt).toISOString(), contentType: f.contentType || 'application/octet-stream' })) || [];
        return { ...fields, asset_type: assetType, files, conversationId: currentActiveConv?.id };
    }, []);

    const _submitToServer = useCallback(async (scanData: any, idToken: string) => {
        const res = await fetch(API_SCANS, { method: "POST", headers: { "Content-Type": "application/json", 'Authorization': `Bearer ${idToken}` }, body: JSON.stringify(scanData) });
        const d = await res.json().catch(() => ({}));
        if (!res.ok) throw new Error(d.error || `Scan creation failed (${res.status})`);
        return d.scan?.scan_id || d.scanId;
    }, []);

    const _associateScan = useCallback(async (scanId: string, convId: string, idToken: string) => {
        try { await fetch(API_CONVERSATION_ASSOCIATE_SCAN(convId), { method: "POST", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` }, body: JSON.stringify({ scanId }), cache: 'no-store' }); }
        catch (e) { console.error('[Scan Submission] Error associating scan:', e); }
    }, []);

    const _handleError = useCallback(async (error: any) => {
        console.error("[Scan Submission] Error:", error);
        let userMsg = "Issue submitting scan.";
        let detailMsg = error.message || "Unknown error.";
        if (detailMsg.includes("rate_limit") || detailMsg.includes("OpenAI")) userMsg = "AI service busy. Try again.";
        else if (detailMsg.includes("Missing required")) userMsg = "Provide more scan details.";
        else if (detailMsg.includes("asset type")) userMsg = "Specify asset type (Web App, etc.).";
        else if (detailMsg.includes("targetDetails")) userMsg = "Specify target (URL, domain).";
        else if (detailMsg.includes("500")) userMsg = "Service technical difficulties.";

        try { await addMessageToConversation({ role: "system", content: { isSystemNotification: true, text: `${userMsg} ${detailMsg}`, severity: 'error' } }); }
        catch (e) { console.error("Failed to add error message:", e); }
        toast.error(userMsg);
    }, [addMessageToConversation]);

    const handleSubmitScanRequest = useCallback(async () => {
        if (isSubmittingScan || !activeConversationId || !user || isChatDisabled) return;
        setIsSubmittingScan(true);
        try {
            if (!allScanInfoProvided) {
                const missing = [];
                if (!scanInfoProvided.appType) missing.push("application type");
                if (!scanInfoProvided.appUrl) missing.push("target URL/location");
                if (missing.length > 0) throw new Error(`Please provide: ${missing.join(", ")}.`);
            }
            const idToken = await user.getIdToken(true);
            const fields = await _extractFields(idToken);
            const payload = _validatePayload(fields, messages, activeConversation);
            const scanId = await _submitToServer(payload, idToken);
            if (!scanId) throw new Error("Failed to get scan ID.");

            await addMessageToConversation({ role: "assistant", content: { isScanCard: true, scanId, assetType: payload.asset_type, target: payload.targetDetails || 'N/A', status: "pending", timestamp: new Date().toISOString() } });
            if (activeConversationId) await _associateScan(scanId, activeConversationId, idToken);
            toast.success("Scan request submitted!");
            if (role) {
                fetchData(role === 'client' ? 'organization' : 'personal', true);
            }
            if (updateConversationTitle && activeConversationId && activeConversation && fields.conversationTitle) {
                const newTitle = fields.conversationTitle;
                await updateConversationTitle(activeConversationId, newTitle);
                try { await fetch(`/api/scans/${scanId}/update`, { method: "PUT", headers: { "Content-Type": "application/json", Authorization: `Bearer ${idToken}` }, body: JSON.stringify({ conversationTitle: newTitle }), cache: 'no-store' }); }
                catch (e) { console.warn("Failed to update scan title directly:", e); }
            }
        } catch (error: any) { await _handleError(error); }
        finally { setIsSubmittingScan(false); }
    }, [
        user, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided, isChatDisabled, isSubmittingScan,
        _extractFields, _validatePayload, _submitToServer, _associateScan, _handleError,
        addMessageToConversation, fetchData, updateConversationTitle, role
    ]);
    return { handleSubmitScanRequest, isSubmittingScan };
}

// --- Helper: Prepare content for AI (used by useScanSubmission) ---
const prepareContentForAI = (content: MessageContent): string => {
    if (typeof content === 'string') return content;
    if (content && typeof content === 'object') {
        if ('text' in content && typeof (content as AiResponseContent).text === 'string') return (content as AiResponseContent).text;
        if ('isScanCard' in content) { const sc = content as ScanCardContent; return `[User initiated a scan for ${sc.assetType}: ${sc.target}]`; }
        if ('isDownloadCard' in content) return `[System provided a download link for a report]`;
        if ('isSystemFileUpload' in content) { const fuc = content as FileUploadContent; return `[User uploaded file: ${fuc.fileName}]`; }
        if ('isSystemNotification' in content) return `[System notification: ${(content as SystemNotificationContent).text}]`;
        return "[Structured message content]";
    }
    return "[Unknown message content]";
};


// --- Internal Rendering Components ---
const RenderTextContent: React.FC<{ text: string }> = React.memo(({ text }) => (
    <div className="text-sm leading-relaxed whitespace-pre-line message-content">{text}</div>
));
const RenderAiResponse: React.FC<{ content: AiResponseContent }> = React.memo(({ content }) => (
    <div className="text-sm leading-relaxed whitespace-pre-line message-content">{content.text}</div>
));
const RenderScanCardMessage: React.FC<{ content: ScanCardContent }> = React.memo(({ content }) => (
    <div className="w-full flex justify-center py-2"><div className="w-full max-w-[800px]">
        <ChatScanCard scanId={content.scanId} assetType={content.assetType} target={content.target} status={content.status} timestamp={content.timestamp} />
    </div></div>
));
const RenderDownloadCardMessage: React.FC<{ content: DownloadCardContent }> = React.memo(({ content }) => (
    <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800/30 text-green-600 dark:text-green-400"><FileDown className="h-6 w-6" /></div>
        <div className="flex-1"><h4 className="text-sm font-medium text-green-800 dark:text-green-300">{content.assetType || 'Scan'} Report Available</h4><p className="text-xs text-green-700 dark:text-green-400 mt-1">Your pentest is complete. Download the report.</p></div>
        <Button variant="outline" size="sm" className="bg-white dark:bg-green-800/30 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-800/50" onClick={() => window.open(content.reportUrl, '_blank')}><Download className="h-4 w-4 mr-2" /> Download</Button>
    </div>
));
const RenderFileUploadMessage: React.FC<{ content: FileUploadContent; findFileInfoByName: (fileName: string) => FileInfoType | undefined; }> = React.memo(({ content, findFileInfoByName }) => {
    const fileInfo = content.status === 'uploaded' && content.fileUrl ? findFileInfoByName(content.fileName) : null;
    return (
        <div className="flex items-center gap-2 p-2 bg-background/50 rounded-md border">
            <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-md bg-primary/10">{content.status === 'uploading' ? <Loader2 className="h-5 w-5 text-primary animate-spin" /> : <FileText className="h-5 w-5 text-primary" />}</div>
            <div className="flex-1 min-w-0"><p className="text-sm font-medium truncate">{content.fileName}</p><p className="text-xs text-muted-foreground">{content.fileSize}</p></div>
            {content.status === 'uploading' ? <p className="text-xs text-muted-foreground mr-2">Uploading...</p> : fileInfo?.url ? <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-primary/10" onClick={() => window.open(fileInfo.url, '_blank')}><Download className="h-4 w-4" /></Button> : null}
        </div>
    );
});
const RenderSystemNotificationMessage: React.FC<{ content: SystemNotificationContent }> = React.memo(({ content }) => (
    <div className={cn("text-sm italic p-2 bg-muted/30 rounded-md", content.severity === 'error' ? "text-red-600 dark:text-red-400 border border-red-200 dark:border-red-700" : "text-muted-foreground")}>{content.text}</div>
));

interface HistorySidebarItemProps { conversation: ConversationType; isActive: boolean; isDeleting: boolean; deleteConfirmationId: string | null; onClick: () => void; onDelete: () => void; onConfirmDelete: () => void; onCancelDelete: () => void; }
const HistorySidebarItem: React.FC<HistorySidebarItemProps> = React.memo(({ conversation, isActive, isDeleting, deleteConfirmationId, onClick, onDelete, onConfirmDelete, onCancelDelete }) => (
    <div className={cn("p-3 rounded-lg cursor-pointer hover:bg-accent group relative card-hover min-h-[4.5rem]", isActive && "bg-accent", isDeleting && "opacity-50 pointer-events-none")} onClick={onClick}>
        <div className="flex justify-between items-start">
            <div className="flex-1 pr-6 overflow-hidden"><h6 className="font-medium text-xs break-words">{conversation.title}</h6><p className="text-xs text-muted-foreground truncate">{getConversationPreview(conversation)}</p><p className="text-xs text-muted-foreground mt-1">{format(new Date(conversation.createdAt), "MMM d, h:mm a")}</p></div>
            {deleteConfirmationId === conversation.id ? <div className="absolute right-1 top-1 flex space-x-1 bg-background/80 backdrop-blur-sm rounded-full p-0.5 shadow-sm border z-10"><Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-green-500 rounded-full hover:bg-green-500/10" onClick={(e) => { e.stopPropagation(); onConfirmDelete(); }}><CheckCircle2 className="h-4 w-4" /></Button><Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-destructive rounded-full hover:bg-destructive/10" onClick={(e) => { e.stopPropagation(); onCancelDelete(); }}><X className="h-4 w-4" /></Button></div>
            : isDeleting ? <div className="absolute right-1 top-1 p-1 bg-background/80 backdrop-blur-sm rounded-full shadow-sm border z-10"><Loader2 className="h-4 w-4 animate-spin text-muted-foreground" /></div>
            : <Button variant="outline" size="sm" className="absolute right-1 top-1 h-7 w-7 p-0 opacity-0 group-hover:opacity-100 rounded-full hover:text-destructive transition-opacity hover:bg-destructive/10 z-10" onClick={(e) => { e.stopPropagation(); onDelete(); }}><Trash2 className="h-4 w-4" /></Button>}
        </div>
    </div>
));
HistorySidebarItem.displayName = 'HistorySidebarItem';

interface ChatMessageItemProps { message: MessageType; findFileInfoByName: (fileName: string) => FileInfoType | undefined; }
const ChatMessageItem: React.FC<ChatMessageItemProps> = React.memo(({ message, findFileInfoByName }) => {
    const { content } = message;
    let renderedContent;
    if (typeof content === 'string') renderedContent = <RenderTextContent text={content} />;
    else if ((content as AiResponseContent).isAiResponse || (content as AiResponseContent).text) renderedContent = <RenderAiResponse content={content as AiResponseContent} />;
    else if ((content as ScanCardContent).isScanCard) renderedContent = <RenderScanCardMessage content={content as ScanCardContent} />;
    else if ((content as DownloadCardContent).isDownloadCard) renderedContent = <RenderDownloadCardMessage content={content as DownloadCardContent} />;
    else if ((content as FileUploadContent).isSystemFileUpload) renderedContent = <RenderFileUploadMessage content={content as FileUploadContent} findFileInfoByName={findFileInfoByName} />;
    else if ((content as SystemNotificationContent).isSystemNotification) renderedContent = <RenderSystemNotificationMessage content={content as SystemNotificationContent} />;
    else renderedContent = <div className="text-sm leading-relaxed whitespace-pre-line message-content">[Unsupported content]</div>;

    if (message.role === "system") {
        return (
            <div className="flex flex-col animate-slide-up">
                <div data-message-id={message.id} className="mx-auto w-full max-w-3xl p-2">
                    <RenderSystemNotificationMessage content={content as SystemNotificationContent} />
                    <p className="text-xs text-muted-foreground mt-2 text-center">{format(new Date(message.timestamp), "h:mm a")}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col animate-slide-up">
            <div data-message-id={message.id} className={cn("flex items-start gap-3 p-4 animate-fade-in", message.role === "user" ? "chat-message-user ml-auto max-w-[80%]" : "chat-message-assistant mr-auto max-w-[80%]")}>
                {message.role === "assistant" && <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-primary/10 dark:bg-[#843DF5]/30 text-primary dark:text-white"><Bot className="h-4 w-4" /></div>}
                <div className="flex-1 space-y-2">{renderedContent}<p className="text-xs text-muted-foreground mt-2">{format(new Date(message.timestamp), "h:mm a")}</p></div>
                {message.role === "user" && <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-secondary dark:bg-[#111827]"><UserIcon className="h-4 w-4 dark:text-white" /></div>}
            </div>
        </div>
    );
});
ChatMessageItem.displayName = 'ChatMessageItem';

const getConversationPreview = (conversation: ConversationType): string => {
    if (!conversation.messages || conversation.messages.length === 0) return "Empty conversation";
    const lastUserMsg = [...conversation.messages].reverse().find(m => m.role === "user" && typeof m.content === 'string');
    if (lastUserMsg && typeof lastUserMsg.content === 'string') return lastUserMsg.content.length > 40 ? `${lastUserMsg.content.substring(0, 40)}...` : lastUserMsg.content;
    const firstMsgContent = conversation.messages[0]?.content;
    if (typeof firstMsgContent === 'string') return firstMsgContent.length > 40 ? `${firstMsgContent.substring(0, 40)}...` : firstMsgContent;
    if (typeof firstMsgContent === 'object' && (firstMsgContent as AiResponseContent).text) { const t = (firstMsgContent as AiResponseContent).text; return t.length > 40 ? `${t.substring(0, 40)}...` : t; }
    return "Conversation started";
};

// --- Main ChatInterface Component ---
export function ChatInterface() {
    const router = useRouter();
    const { user, role, loading: authLoading } = useAuth();
    const { scans, fetchData } = useScans();
    const { totalScans } = useScanCount();
    const [isMounted, setIsMounted] = useState(false);
    const [isChatDisabled, setIsChatDisabled] = useState(false);

    const { conversations, activeConversationId, activeConversation, setActiveConversationId, createNewConversation: contextCreateNewConversation, deleteConversation: contextDeleteConversation, addMessageToConversation, loading: conversationsLoading, updateConversationTitle } = useConversations();
    const { sendMessage, uploadFile } = useMessageQueue();

    const [input, setInput] = useState("");
    const [deleteConfirmationId, setDeleteConfirmationId] = useState<string | null>(null);
    const [deletingConversations, setDeletingConversations] = useState<string[]>([]); // Track IDs being deleted
    const [isCreatingNewConversation, setIsCreatingNewConversation] = useState(false);
    const [historySidebarOpen, setHistorySidebarOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);

    const messages: MessageType[] = useMemo(() => (activeConversation?.messages as MessageType[] || []), [activeConversation]);
    const { scanInfoProvided, allScanInfoProvided } = useScanInfoDetector(messages);
    const { handleSubmitScanRequest, isSubmittingScan } = useScanSubmission({ user, role, activeConversationId, activeConversation, messages, allScanInfoProvided, scanInfoProvided, addMessageToConversation, fetchData, updateConversationTitle, isChatDisabled });

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLTextAreaElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => { setIsMounted(true); }, []);
    useEffect(() => { if (isMounted && !authLoading && !user) router.replace("/login"); }, [isMounted, user, authLoading, router]);
    const scrollToBottom = useCallback(() => { requestAnimationFrame(() => { messagesEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' }); }); }, []);
    useEffect(scrollToBottom, [messages, activeConversationId, scrollToBottom]);
    useEffect(() => { if (inputRef.current && !historySidebarOpen && !isSubmittingMessage) { inputRef.current.focus(); if (!input) inputRef.current.style.height = 'auto'; } }, [activeConversationId, historySidebarOpen, input, isSubmittingMessage]);

    useEffect(() => {
        if (conversations.length > 0 && !conversationsLoading && updateConversationTitle) {
            conversations.forEach(conv => {
                if (conv.title === "Security Scan Request" || conv.title === "Pentest Scan Request") { // Keep old "Pentest Scan Request" for migration
                    updateConversationTitle(conv.id, TITLE_PENTEST_SCAN_REQUEST).catch(error => console.error(`Failed to update title for ${conv.id}:`, error));
                }
            });
        }
    }, [conversations, conversationsLoading, updateConversationTitle]);

    useEffect(() => {
        if (!activeConversation || !scans || scans.length === 0) { setIsChatDisabled(false); return; }
        const scanCardMsg = messages.find(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard);
        if (scanCardMsg) {
            const scanId = (scanCardMsg.content as ScanCardContent).scanId;
            const scan = scans.find(s => s.id === scanId);
            setIsChatDisabled(scan?.status === "completed");
        } else { setIsChatDisabled(false); }
    }, [activeConversation, messages, scans]);

    const filteredConversations = useMemo(() => conversations
        .filter(conv => !deletingConversations.includes(conv.id))
        .filter(conv => conv.title.toLowerCase().includes(searchQuery.toLowerCase()) || conv.messages.some(msg => msg.role === "user" && typeof msg.content === 'string' && msg.content.toLowerCase().includes(searchQuery.toLowerCase()))),
        [conversations, deletingConversations, searchQuery]
    );

    const handleSendMessageUI = useCallback(async () => {
        if (!input.trim() || isSubmittingMessage || isChatDisabled) return;
        const messageContent = input.trim();
        setInput("");
        if (inputRef.current) inputRef.current.style.height = 'auto'; // Reset height
        setIsSubmittingMessage(true);
        try { await sendMessage(messageContent); }
        catch (error) { console.error("Error UI handleSendMessage:", error); toast.error("Failed to send message."); }
        finally { setIsSubmittingMessage(false); }
    }, [input, isSubmittingMessage, isChatDisabled, sendMessage]);

    const handleNewConversationUI = useCallback(async () => {
        if (isCreatingNewConversation) return;
        setIsCreatingNewConversation(true); setInput(""); setHistorySidebarOpen(false);
        try { await contextCreateNewConversation(); }
        catch (error) { console.error("Error UI new conv:", error); toast.error("Failed to create new conversation"); }
        finally { setIsCreatingNewConversation(false); }
    }, [isCreatingNewConversation, contextCreateNewConversation]);

    const handleDeleteConversationUI = useCallback((id: string) => setDeleteConfirmationId(prevId => prevId === id ? null : id), []);
    const confirmDeleteConversationUI = useCallback(async (id: string) => {
        if (!id || deletingConversations.includes(id)) return;
        setDeletingConversations(prev => [...prev, id]); setDeleteConfirmationId(null);
        try {
            const success = await contextDeleteConversation(id);
            if (!success) { toast.error("Failed to delete conversation"); setDeletingConversations(prev => prev.filter(convId => convId !== id));}
            // Success toast is handled in context
        } catch (error) { console.error("Error UI delete conv:", error); toast.error("Failed to delete conversation"); setDeletingConversations(prev => prev.filter(convId => convId !== id));}
    }, [deletingConversations, contextDeleteConversation]);
    const cancelDeleteConversationUI = useCallback(() => setDeleteConfirmationId(null), []);

    const findFileInfoByName = useCallback((fileName: string): FileInfoType | undefined => {
        return activeConversation?.fileInfo?.find(f => f.originalName === fileName);
    }, [activeConversation]);

    const handleFileSelectUI = useCallback(async (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const target = event.target; // Capture target before async
        if (!file || isSubmittingMessage || isChatDisabled) { if (target) target.value = ''; return; }
        if (!user) { toast.error("Authentication error."); if (target) target.value = ''; return; }
        if (!activeConversationId) { toast.error("Select or start a conversation first."); if (target) target.value = ''; return; }
        if (file.size > MAX_FILE_SIZE) { toast.error(`File too large (max ${formatFileSize(MAX_FILE_SIZE)}).`); if (target) target.value = ''; return; }
        if (!validateFileExtension(file.name)) { toast.error("Invalid file type."); if (target) target.value = ''; return; }

        setIsSubmittingMessage(true);
        try { await uploadFile(file); }
        catch (error) {
            // Error handling (toast, adding system message) is now primarily in MessageQueueContext's uploadFile/uploadFileInBackground
            console.error("Error UI handleFileSelect:", error);
            // If uploadFile re-throws, you might still want a generic UI toast here, but avoid double-messaging.
        } finally {
            setIsSubmittingMessage(false);
            if (target) target.value = ''; // Reset file input
        }
    }, [isSubmittingMessage, isChatDisabled, user, activeConversationId, uploadFile]);


    if (!isMounted) return <div className="flex h-screen items-center justify-center"><Loader2 className="h-10 w-10 animate-spin text-primary" /></div>;
    if (authLoading) return <div className="flex h-[calc(100vh-4rem)] items-center justify-center"><Loader2 className="h-8 w-8 animate-spin text-primary" /><p className="ml-2 text-muted-foreground">Loading authentication...</p></div>;
    if (!user) return null; // Redirect handled by useEffect
    if (role === "admin") { router.replace("/dashboard/admin"); return null; }


    const renderHistorySidebar = () => (
        <div className={cn("absolute inset-y-0 left-0 z-30 w-full sm:w-80 bg-white/90 dark:bg-[#111827]/90 backdrop-blur-md border-r transform transition-transform duration-300 ease-in-out will-change-transform", historySidebarOpen ? "translate-x-0" : "-translate-x-full")}>
            <div className="flex flex-col h-full dark:bg-[#111827]">
                <div className="p-4 border-b flex items-center justify-between"><h3 className="font-semibold">History</h3><Button variant="ghost" size="icon" onClick={() => setHistorySidebarOpen(false)} className="dark:hover:bg-[#111827]"><ChevronLeft className="h-5 w-5" /> <span className="sr-only">Close</span></Button></div>
                <div className="p-4 border-b"><div className="relative"><Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" /><Input placeholder="Search history..." className="pl-8 rounded-full h-9" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} /></div></div>
                <ScrollArea className="flex-1 dark:bg-[#111827] chat-scrollbar"><div className="p-2 space-y-1">
                    {conversationsLoading && filteredConversations.length === 0 ? <div className="flex flex-col items-center justify-center py-8"><Loader2 className="h-6 w-6 animate-spin text-primary mb-2" /><p className="text-sm text-muted-foreground">Loading history...</p></div>
                    : filteredConversations.length === 0 && !searchQuery ? <p className="text-center text-muted-foreground p-4 text-sm">No conversations yet.</p>
                    : filteredConversations.length === 0 && searchQuery ? <p className="text-center text-muted-foreground p-4 text-sm">No matching history.</p>
                    : filteredConversations.map(conv => <HistorySidebarItem key={conv.id} conversation={conv} isActive={activeConversationId === conv.id} isDeleting={deletingConversations.includes(conv.id)} deleteConfirmationId={deleteConfirmationId} onClick={() => { if (!deletingConversations.includes(conv.id)) { setActiveConversationId(conv.id); setHistorySidebarOpen(false); }}} onDelete={() => handleDeleteConversationUI(conv.id)} onConfirmDelete={() => confirmDeleteConversationUI(conv.id)} onCancelDelete={cancelDeleteConversationUI} />)}
                </div></ScrollArea>
            </div>
        </div>
    );

    const renderChatHeader = () => (
        <div className="flex items-center justify-between px-4 sm:px-6 py-3 bg-transparent backdrop-blur-md sticky top-0 z-10">
            <div className="flex items-center space-x-2">
                <Button variant="ghost" size="icon" onClick={() => setHistorySidebarOpen(!historySidebarOpen)} className="mr-2 dark:hover:bg-[#111827] text-primary bg-primary/10 p-4 dark:bg-[#111827]/90 rounded-lg"><span className="sr-only">{historySidebarOpen ? "Close" : "Open"} History</span>{historySidebarOpen ? <ChevronLeft className="h-6 w-6 sm:h-10 sm:w-10" /> : <Clock className="h-6 w-6 sm:h-10 sm:w-10" />}</Button>
                <div className="relative overflow-hidden w-full max-w-[calc(100vw-200px)] sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl"><h2 className="text-base font-semibold overflow-hidden truncate">{activeConversation?.title || TITLE_PENTEST_SCAN_REQUEST}</h2></div>
            </div>
            <Button onClick={handleNewConversationUI} className="rounded-full bg-[#843DF5] text-white border-none hover:bg-[#843DF5]/90" size="sm" disabled={isCreatingNewConversation}>{isCreatingNewConversation ? <Loader2 className="h-4 w-4 sm:mr-2 animate-spin" /> : <Plus className="h-4 w-4 sm:mr-2" />}<span className="hidden sm:inline">New Request</span></Button>
        </div>
    );

    const renderMessageList = () => (
        <div className="flex-1 p-4 overflow-y-auto dark:bg-[#111827] chat-scrollbar">
            <div className="space-y-4 max-w-3xl mx-auto">
                {conversationsLoading && messages.length === 0 ? <div className="flex flex-col items-center justify-center h-64"><Loader2 className="h-8 w-8 animate-spin text-primary mb-2" /><p className="text-muted-foreground">Loading conversation...</p></div>
                : messages.map(message => <ChatMessageItem key={message.id} message={message} findFileInfoByName={findFileInfoByName} />)}
                <div ref={messagesEndRef} className="h-px" />
            </div>
        </div>
    );

    const renderChatInputArea = () => (
        <div className="p-3 sm:p-4 bg-white/90 dark:bg-[#111827]/90 backdrop-blur-md border-t border-border/50 sticky bottom-0">
            {isChatDisabled ? <div className="flex items-center justify-center max-w-4xl mx-auto p-2 bg-muted/50 rounded-full"><CheckCircle2 className="h-4 w-4 text-green-500 mr-2" /><p className="text-sm text-muted-foreground">Chat disabled: pentest completed.</p></div>
            : <div className="max-w-4xl mx-auto">
                {(allScanInfoProvided || scanInfoProvided.appUrl) && !messages.some(m => typeof m.content === 'object' && (m.content as ScanCardContent).isScanCard) && (
                    <div className="flex items-center justify-between mb-3 p-3 px-5 bg-purple-100 dark:bg-[#1E293B]/70 rounded-full border-2 border-purple-400 dark:border-[#843DF5]/60 animate-fade-in shadow-sm">
                        <div className="flex items-center"><ShieldAlert className="h-5 w-5 text-purple-600 dark:text-white mr-2 flex-shrink-0" /><p className="text-sm text-purple-700 dark:text-gray-100">Ready to submit? Or add more details.</p></div>
                        <Button variant="default" size="sm" className="rounded-full ml-2 flex-shrink-0 bg-[#843DF5] hover:bg-[#843DF5]/90 text-white" disabled={isSubmittingScan || isSubmittingMessage} onClick={handleSubmitScanRequest}>{isSubmittingScan ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : <ShieldAlert className="h-4 w-4 mr-1" />}{isSubmittingScan ? "Submitting..." : "Submit Request"}</Button>
                    </div>
                )}
                <div className="relative">
                    <Textarea ref={inputRef} placeholder="Type your message..." value={input} onChange={(e) => { setInput(e.target.value); e.target.style.height = 'auto'; e.target.style.height = `${Math.min(e.target.scrollHeight, 120)}px`; }} onKeyDown={(e: KeyboardEvent<HTMLTextAreaElement>) => { if (e.key === "Enter" && !e.shiftKey) { e.preventDefault(); handleSendMessageUI(); }}} className="w-full rounded-full pr-16 pl-14 min-h-[50px] max-h-[120px] py-3 resize-none" disabled={isSubmittingMessage || isSubmittingScan || isChatDisabled} autoFocus rows={1} />
                    <Button variant="ghost" size="icon" className="absolute left-2 top-1/2 h-9 w-9 rounded-full cursor-pointer -translate-y-1/2" onClick={() => fileInputRef.current?.click()} disabled={isSubmittingMessage || isSubmittingScan || isChatDisabled}><Paperclip className="h-5 w-5" /> <span className="sr-only">Attach</span></Button>
                    <input type="file" id="file-upload" className="hidden" ref={fileInputRef} onChange={handleFileSelectUI} accept=".pdf,.doc,.docx,.txt,.json,.xml,.csv" disabled={isSubmittingMessage || isSubmittingScan || isChatDisabled} />
                    <Button type="submit" size="icon" className="absolute right-2 top-1/2 h-9 w-9 rounded-full -translate-y-1/2" onClick={handleSendMessageUI} disabled={!input.trim() || isSubmittingMessage || isSubmittingScan || isChatDisabled}>{isSubmittingMessage && !isSubmittingScan ? <Loader2 className="h-5 w-5 animate-spin" /> : <Send className="h-5 w-5" />}<span className="sr-only">Send</span></Button>
                </div>
            </div>}
        </div>
    );

    return (
        <div className="flex h-[calc(100vh-4rem)] flex-col w-full relative overflow-hidden growthguard-gradient-bg dark:bg-[#111827]">
            {renderHistorySidebar()}
            <div className="flex flex-col flex-1 relative overflow-hidden dark:bg-[#111827]">
                {renderChatHeader()}
                {renderMessageList()}
                {renderChatInputArea()}
            </div>
            {historySidebarOpen && <div className="fixed inset-0 bg-white/60 dark:bg-[#111827]/60 backdrop-blur-sm z-20" onClick={() => setHistorySidebarOpen(false)} />}
        </div>
    );
}