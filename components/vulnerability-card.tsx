"use client";

import React, { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ModernCheckbox } from "@/components/ui/modern-checkbox";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronRight, ExternalLink, ChevronDown, MessageSquare, User, Calendar } from "lucide-react";
import { Vulnerability, VulnerabilitySeverity } from "@/types/vulnerability-types";
import { cn, formatDate } from "@/lib/utils"; // Import formatDate
 
interface VulnerabilityCardProps {
  vulnerability: Vulnerability;
  onClick: () => void;
  onStatusChange: (id: string, status: Vulnerability['status'], note?: string) => void; // Add note parameter
  role?: string | null; // Allow role to be string or null
  onRequestRetest: (vulnerability: Vulnerability) => void; // Add onRequestRetest prop
  isSelected?: boolean;
  onSelectionChange?: (vulnerabilityId: string) => void;
  showCheckbox?: boolean;
}
 
const VulnerabilityCardComponent = ({
  vulnerability,
  onClick,
  onStatusChange,
  role,
  onRequestRetest,
  isSelected = false,
  onSelectionChange,
  showCheckbox = false
}: VulnerabilityCardProps) => {
  const [isNotesExpanded, setIsNotesExpanded] = useState(false);

  const getSeverityColor = (severity: VulnerabilitySeverity) => {
    switch (severity) {
      case "critical":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "low":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "closed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  const handleCheckboxChange = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSelectionChange) {
      onSelectionChange(vulnerability.id);
    }
  }, [onSelectionChange, vulnerability.id]);

  return (
    <div className="flex items-start gap-4 group transition-all duration-300">
      {showCheckbox && (
        <div onClick={handleCheckboxChange} className="flex-shrink-0 pt-6">
          <ModernCheckbox
            checked={isSelected}
            variant="primary"
            size="md"
            aria-label={`Select ${vulnerability.name}`}
          />
        </div>
      )}
      <Card
        className={cn(
          "flex-1 cursor-pointer vulnerability-card-hover overflow-hidden",
          "border border-border/50 bg-card/50 backdrop-blur-sm",
          "shadow-sm hover:shadow-md transition-all duration-300",
          "transform hover:scale-[1.01] hover:-translate-y-1",
          isSelected && "vulnerability-card-selected ring-2",
          !isSelected && "ring-0"
        )}
        onClick={onClick}
      >
      <CardHeader className="pb-3 relative">
        <div className={cn(
          "absolute top-0 left-0 right-0 h-1 rounded-t-lg transition-all duration-300",
          vulnerability.severity === "critical" && "bg-red-500",
          vulnerability.severity === "high" && "bg-orange-500",
          vulnerability.severity === "medium" && "bg-yellow-500",
          vulnerability.severity === "low" && "bg-blue-500"
        )} />
        
        <div className="flex justify-between items-start gap-3 mt-2">
          <div className="flex items-center gap-2 flex-wrap">
            <Badge className={cn(
              "capitalize font-medium px-3 py-1 shadow-sm transition-all duration-200 hover:scale-105",
              getSeverityColor(vulnerability.severity)
            )}>
              {vulnerability.severity}
            </Badge>
            {vulnerability.cvss && (
              <Badge variant="outline" className="font-mono text-xs px-2 py-1 bg-background/50 hover:bg-background transition-colors duration-200">
                CVSS: {vulnerability.cvss}
              </Badge>
            )}
            {vulnerability.cveId && (
              <Badge variant="outline" className="flex items-center gap-1 px-2 py-1 bg-background/50 hover:bg-background transition-colors duration-200">
                {vulnerability.cveId}
                <ExternalLink className="h-3 w-3" />
              </Badge>
            )}
          </div>
          {vulnerability.status && (
            <Badge className={cn(
              "capitalize font-medium px-3 py-1 shadow-sm transition-all duration-200 hover:scale-105",
              getStatusColor(vulnerability.status)
            )}>
              {vulnerability.status}
            </Badge>
          )}
        </div>
        <CardTitle className="text-xl font-semibold mt-4 line-clamp-2 leading-tight text-foreground group-hover:text-violet transition-colors duration-300">
          {vulnerability.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="space-y-3">
          {vulnerability.description && (
            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
              {vulnerability.description}
            </p>
          )}
          {vulnerability.scanName && (
            <div className="flex items-center gap-2 p-2 bg-muted/30 rounded-lg">
              <div className="w-2 h-2 rounded-full bg-violet"></div>
              <span className="text-xs text-muted-foreground">Pentest:</span>
              <span className="text-xs font-medium text-foreground truncate flex-1">
                {vulnerability.scanName}
              </span>
            </div>
          )}
          {vulnerability.affectedComponent && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>Affected Component:</span>
              <span className="font-medium text-foreground">{vulnerability.affectedComponent}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 pb-4 flex justify-between items-center bg-muted/20 border-t border-border/30">
        <div className="flex items-center gap-2">
          <Button
            variant="default"
            size="sm"
            className="h-10 px-6 gap-2 rounded-lg bg-violet hover:bg-violet/90 text-white shadow-md hover:shadow-lg transition-all duration-300 modern-focus transform hover:scale-105"
            onClick={onClick}
          >
            View Details
            <ChevronRight className="h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
          </Button>
          {vulnerability.notes && vulnerability.notes.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="ring-1 ring-gray-300 h-10 px-4 gap-2 rounded-lg modern-focus text-muted-foreground hover:text-foreground transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setIsNotesExpanded((prev) => !prev);
              }}
            >
                {vulnerability.notes.length} Notes <ChevronDown
                  className={cn(
                    "h-10 w-10 mt-0.5 transition-transform duration-300",
                    isNotesExpanded ? "rotate-180" : ""
                  )}
                />
            </Button>
          )}
        </div>
        <div className="flex gap-2">
          {role === "admin" && (
            <>
              {vulnerability.status === "Closed" && (
                <Button
                  onClick={(e) => { e.stopPropagation(); onStatusChange(vulnerability.id, "Open"); }}
                  variant="outline"
                  size="sm"
                  className="modern-focus transition-all duration-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transform hover:scale-105"
                >
                  Mark as Open
                </Button>
              )}
              {vulnerability.status === "Open" && (
                <Button
                  onClick={(e) => { e.stopPropagation(); onStatusChange(vulnerability.id, "Closed"); }}
                  variant="outline"
                  size="sm"
                  className="modern-focus transition-all duration-300 hover:bg-green-50 hover:border-green-300 hover:text-green-700 transform hover:scale-105"
                >
                  Mark as Closed
                </Button>
              )}
            </>
          )}
          {role !== "admin" && vulnerability.status !== "Closed" && vulnerability.status !== "Pending Retest" && (
            <Button
              onClick={(e) => { e.stopPropagation(); onRequestRetest(vulnerability); }}
              variant="outline"
              size="sm"
              className="modern-focus transition-all duration-300 hover:bg-violet/10 hover:border-violet/30 hover:text-violet transform hover:scale-105"
            >
              Request Retest
            </Button>
          )}
        </div>
      </CardFooter>
      {vulnerability.notes && vulnerability.notes.length > 0 && (
        <div className="border-t border-border/30 bg-gradient-to-r from-muted/10 to-muted/20">
          <Collapsible open={isNotesExpanded} onOpenChange={setIsNotesExpanded}>
            <CollapsibleContent className="px-6 pt-4 pb-6">
              <div className="space-y-4">
                {vulnerability.notes.slice(-3).map((note, index) => (
                  <div
                    key={index}
                    className="bg-background/70 backdrop-blur-sm rounded-xl p-4 border border-border/40 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-[1.01]"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <p className="text-sm text-foreground leading-relaxed mb-3 font-medium">
                      {note.text}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1.5 px-2 py-1 bg-muted/30 rounded-full">
                        <User className="h-3 w-3" />
                        <span className="font-medium">{note.author}</span>
                      </div>
                      <div className="flex items-center gap-1.5 px-2 py-1 bg-muted/30 rounded-full">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(note.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                ))}
                
                {vulnerability.notes.length > 3 && (
                  <div className="text-center pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs text-muted-foreground hover:text-violet hover:bg-violet/10 transition-all duration-300 rounded-full px-4 py-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClick();
                      }}
                    >
                      View all {vulnerability.notes.length} notes →
                    </Button>
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      )}
      </Card>
    </div>
  );
};

export const VulnerabilityCard = React.memo(VulnerabilityCardComponent);
