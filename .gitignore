# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# Bun
bun.lockb

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.bun-debug.log*

# local env files
.env*.local
.env
.env.development
.env.test
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Firebase
firebase-debug.log*
firebase-debug.*.log*
.firebase/
serviceAccountKey.json

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db
# Local Netlify folder
.netlify
