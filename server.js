const next = require('next');
const { Server } = require('socket.io');
const http = require('http');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Debug flag for verbose logging
const DEBUG = true;

// Helper function for logging
const logDebug = (message, ...args) => {
  if (DEBUG) {
    console.log(`[Socket.io Debug] ${message}`, ...args);
  }
};

app.prepare().then(() => {
  // Create HTTP server
  const server = http.createServer(async (req, res) => {
    try {
      await handle(req, res);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal Server Error');
    }
  });

  // Initialize Socket.io with the server
  const io = new Server(server, {
    path: '/api/socket',
    addTrailingSlash: false,
    transports: ['websocket', 'polling'],
    cors: {
      origin: '*',
      methods: ['GET', 'POST'],
      credentials: true
    },
    // Ping settings
    pingTimeout: 60000,
    pingInterval: 10000,
    // Connection timeout
    connectTimeout: 45000,
    // Allow upgrades
    allowUpgrades: true,
    // Disable compression
    perMessageDeflate: false,
    // Increase buffer size
    maxHttpBufferSize: 1e8, // 100 MB
  });

  // Store the io instance globally
  global.io = io;

  // Set up connection handler
  io.on('connection', (socket) => {
    logDebug(`Client connected: ${socket.id}`);

    // Track socket state
    const joinedRooms = new Set();
    let userId = null;

    // Handle room joining
    socket.on('join_room', (data) => {
      try {
        const { userId: newUserId, role } = data;
        userId = newUserId;

        // Create room key
        const userRoomKey = `user-${userId}`;

        // Only join if not already joined
        if (!joinedRooms.has(userRoomKey)) {
          logDebug(`User ${userId} joined their room`);
          socket.join(userRoomKey);
          joinedRooms.add(userRoomKey);

          // Send acknowledgment to client
          socket.emit('room_joined', { userId, success: true });
        }

        // If user is admin, join admin room
        if (role === 'admin' && !joinedRooms.has('admin-room')) {
          socket.join('admin-room');
          joinedRooms.add('admin-room');
        }

        // If user is manager, join team room
        if (role === 'manager') {
          const teamRoomKey = `team-${userId}`;
          if (!joinedRooms.has(teamRoomKey)) {
            socket.join(teamRoomKey);
            joinedRooms.add(teamRoomKey);
          }
        }
      } catch (error) {
        console.error('Error in join_room handler:', error);
        socket.emit('error', { message: 'Failed to join room' });
      }
    });

    // Handle room leaving
    socket.on('leave_room', (leaveUserId) => {
      try {
        const roomKey = `user-${leaveUserId}`;
        if (joinedRooms.has(roomKey)) {
          logDebug(`User ${leaveUserId} left their room`);
          socket.leave(roomKey);
          joinedRooms.delete(roomKey);
        }
      } catch (error) {
        console.error('Error in leave_room handler:', error);
      }
    });

    // Handle ping events to keep connection alive
    socket.on('ping', () => {
      try {
        socket.emit('pong');
      } catch (error) {
        console.error('Error in ping handler:', error);
      }
    });

    // Handle transport errors
    socket.on('error', (error) => {
      console.error(`Socket error for ${socket.id}:`, error);
    });

    // Handle disconnection with improved error handling
    socket.on('disconnect', (reason) => {
      logDebug(`Client disconnected: ${socket.id}, reason: ${reason}`);

      // Clean up resources
      joinedRooms.clear();

      // Log additional information for debugging
      if (reason === 'transport close' || reason === 'transport error') {
        // Only log as warning if we know the user, otherwise it's likely just a page refresh before auth
        if (userId) {
          console.warn(`Transport issue for socket ${socket.id}. User: ${userId}`);
        } else {
          // For unknown users (likely pre-auth), log at debug level to reduce noise
          logDebug(`Transport issue for socket ${socket.id}. User not yet authenticated.`);
        }
      }
    });
  });

  // Start the server
  const PORT = process.env.PORT || 3000;
  server.listen(PORT, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://localhost:${PORT}`);
    console.log(`> Socket.io server initialized on path: /api/socket`);
  });
});
