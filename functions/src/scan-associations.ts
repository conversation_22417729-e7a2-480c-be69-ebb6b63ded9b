import {onDocumentWritten} from "firebase-functions/v2/firestore";
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();
const auth = admin.auth();

// No longer generating stable IDs since all users must have Firebase IDs

/**
 * Firestore trigger that runs when a scan document is created or updated
 * This ensures that scans are properly associated with team members
 */
export const onScanWriteUpdateAssociations = onDocumentWritten({
  document: "scans/{scanId}",
  region: "us-central1",
}, async (event) => {
  const scanId = event.params.scanId;
  const change = event.data;

  // Skip if the document was deleted
  if (!change?.after.exists) {
    console.log(`Scan ${scanId} was deleted, skipping association update`);
    return null;
  }

  const scanData = change.after.data();
  if (!scanData) {
    console.log(`No data found for scan ${scanId}, skipping`);
    return null;
  }

  // Skip if the scan already has all the necessary associations
  if (
    scanData.userId &&
    scanData.userEmail &&
    scanData.teamId &&
    scanData.managerId &&
    scanData.stableId
  ) {
    console.log(`Scan ${scanId} already has all necessary associations, skipping`);
    return null;
  }

  const updates: Record<string, unknown> = {};
  let needsUpdate = false;

  // If we have a userEmail but no userId, try to find the user in Firebase Auth
  if (scanData.userEmail && !scanData.userId) {
    try {
      const userRecord = await auth.getUserByEmail(scanData.userEmail);
      updates.userId = userRecord.uid;
      needsUpdate = true;
      console.log(`Adding userId ${userRecord.uid} to scan ${scanId}`);
    } catch (error) {
      // User not found in Firebase Auth, log error but don't create a stable ID
      console.log(`User with email ${scanData.userEmail} not found in Firebase Auth, skipping`);
    }
  }

  // If we have a userId but no userEmail, try to find the user in Firebase Auth
  if (scanData.userId && !scanData.userEmail) {
    try {
      // Only try to get user by ID if it's not a stable ID
      if (!scanData.userId.startsWith("email-")) {
        const userRecord = await auth.getUser(scanData.userId);
        if (userRecord.email) {
          updates.userEmail = userRecord.email;
          needsUpdate = true;
          console.log(`Adding userEmail ${userRecord.email} to scan ${scanId}`);
        }
      }
    } catch (error) {
      console.log(`Error getting user for userId ${scanData.userId}:`, error);
    }
  }

  // No longer generating stable IDs since all users must have Firebase IDs

  // If we have a userEmail but no team info, try to find the team
  if (scanData.userEmail && (!scanData.teamId || !scanData.managerId)) {
    try {
      // Look for teams where this user's email is in teamMemberEmails
      const teamQuery = db.collection("teams").where("teamMemberEmails", "array-contains", scanData.userEmail);
      const teamSnapshot = await teamQuery.get();

      if (!teamSnapshot.empty) {
        // User is part of a team
        const teamDoc = teamSnapshot.docs[0];
        const teamData = teamDoc.data();

        updates.teamId = teamDoc.id;
        updates.managerId = teamData.managerId;
        updates.managerEmail = teamData.managerEmail;
        needsUpdate = true;

        console.log(`Adding team info to scan ${scanId}: team=${teamDoc.id}, manager=${teamData.managerId}`);
      }
    } catch (error) {
      console.error(`Error finding team for scan ${scanId}:`, error);
    }
  }

  // Update the scan if needed
  if (needsUpdate) {
    try {
      await db.collection("scans").doc(scanId).update(updates);
      console.log(`Updated scan ${scanId} with associations:`, updates);
    } catch (error) {
      console.error(`Error updating scan ${scanId}:`, error);
    }
  }

  return null;
});

/**
 * Firestore trigger that runs when a team document is created or updated
 * This ensures that all scans for team members are properly associated with the team
 */
export const onTeamWriteUpdateScanAssociations = onDocumentWritten({
  document: "teams/{teamId}",
  region: "us-central1",
}, async (event) => {
  const teamId = event.params.teamId;
  const change = event.data;

  // Skip if the document was deleted
  if (!change?.after.exists) {
    console.log(`Team ${teamId} was deleted, skipping scan association update`);
    return null;
  }

  const teamData = change.after.data();
  if (!teamData) {
    console.log(`No data found for team ${teamId}, skipping`);
    return null;
  }

  const teamMemberEmails = teamData.teamMemberEmails || [];
  if (teamMemberEmails.length === 0) {
    console.log(`Team ${teamId} has no member emails, skipping`);
    return null;
  }

  const managerId = teamData.managerId;
  const managerEmail = teamData.managerEmail;

  if (!managerId) {
    console.log(`Team ${teamId} has no manager ID, skipping`);
    return null;
  }

  console.log(`Updating scan associations for team ${teamId} with ${teamMemberEmails.length} members`);

  // Find all scans for team members that don't have team info
  const scanQuery = db.collection("scans")
    .where("userEmail", "in", teamMemberEmails)
    .where("teamId", "==", null);

  try {
    const scanSnapshot = await scanQuery.get();

    if (scanSnapshot.empty) {
      console.log(`No scans found for team ${teamId} members without team info`);
      return null;
    }

    console.log(`Found ${scanSnapshot.size} scans to update for team ${teamId}`);

    // Update each scan with team info
    const batch = db.batch();
    let batchCount = 0;

    for (const scanDoc of scanSnapshot.docs) {
      const scanRef = scanDoc.ref;

      batch.update(scanRef, {
        teamId: teamId,
        managerId: managerId,
        managerEmail: managerEmail,
      });

      batchCount++;

      // Firestore batches are limited to 500 operations
      if (batchCount >= 500) {
        await batch.commit();
        console.log(`Committed batch of ${batchCount} scan updates`);
        batchCount = 0;
      }
    }

    // Commit any remaining updates
    if (batchCount > 0) {
      await batch.commit();
      console.log(`Committed final batch of ${batchCount} scan updates`);
    }

    console.log(`Successfully updated all scans for team ${teamId}`);
  } catch (error) {
    console.error(`Error updating scans for team ${teamId}:`, error);
  }

  return null;
});
