# Environment Setup Guide

This guide explains how to set up and switch between different environments (development and production) in the DeepScan Platform.

## Overview

The application supports two environments:
- **Development**: For local development with a separate Firebase project
- **Production**: For production deployment with the main Firebase project

Each environment uses its own Firebase project, allowing you to develop and test without affecting production data.

## Environment Configuration Files

The application uses the following environment files:

- `.env.development` - Development environment configuration
- `.env.production` - Production environment configuration
- `.env.local` - The active environment configuration (copied from one of the above)

## Setting Up Development Environment

1. **Create a Firebase Project for Development**

   - Go to the [Firebase Console](https://console.firebase.google.com/)
   - Create a new project for development
   - Set up Firestore, Storage, and Authentication services

2. **Configure Development Environment**

   - Open `.env.development`
   - Replace the placeholder values with your development Firebase project credentials:
     ```
     NEXT_PUBLIC_FIREBASE_API_KEY=YOUR_DEV_API_KEY
     NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=YOUR_DEV_PROJECT_ID.firebaseapp.com
     NEXT_PUBLIC_FIREBASE_PROJECT_ID=YOUR_DEV_PROJECT_ID
     FIREBASE_STORAGE_BUCKET="YOUR_DEV_PROJECT_ID.firebasestorage.app"
     NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=YOUR_DEV_MESSAGING_SENDER_ID
     NEXT_PUBLIC_FIREBASE_APP_ID=YOUR_DEV_APP_ID
     NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=YOUR_DEV_MEASUREMENT_ID

     FIREBASE_PROJECT_ID=YOUR_DEV_PROJECT_ID
     FIREBASE_CLIENT_EMAIL=YOUR_DEV_CLIENT_EMAIL
     FIREBASE_PRIVATE_KEY="YOUR_DEV_PRIVATE_KEY"
     ```

3. **Generate a Service Account Key**

   - In the Firebase Console, go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file as `service-account-dev.json` in the project root
   - Extract the values from the JSON file to update the `.env.development` file

## Switching Between Environments

### Using NPM Scripts

The application provides npm scripts to easily switch between environments:

```bash
# Switch to development environment
npm run use:dev

# Switch to production environment
npm run use:prod
```

### Manual Switching

You can also manually switch environments by copying the appropriate file:

```bash
# For development
cp .env.development .env.local

# For production
cp .env.production .env.local
```

After switching environments, restart your development server:

```bash
npm run dev
```

## Verifying the Active Environment

When the application starts, it will log the active environment in the console:

```
Running in development mode
```

or

```
Running in production mode
```

## Important Notes

1. **Never commit sensitive credentials** to version control. The `.env.local`, `.env.development`, and `.env.production` files are listed in `.gitignore`.

2. **Be careful when working with production data**. Always double-check which environment you're using before making changes.

3. **Keep your service account keys secure**. These keys provide administrative access to your Firebase projects.

4. **Different database structures**. If you make schema changes in development, remember to apply them to production when deploying.

## Troubleshooting

If you encounter issues with environment switching:

1. **Check the active environment**: Look at the console logs when starting the application.

2. **Verify environment files**: Make sure your `.env.development` and `.env.production` files have the correct values.

3. **Restart the server**: After switching environments, always restart your development server.

4. **Clear browser cache**: Sometimes the browser caches Firebase configuration. Clear your browser cache if you experience unexpected behavior.
