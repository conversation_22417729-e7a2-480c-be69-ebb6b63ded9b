"use client"

import { DashboardLayout } from "@/components/dashboard-layout"
import { OptimizedChatInterface } from "@/components/optimized-chat-interface"
import { useAuth } from "@/hooks/useAuth"
import { useRouter } from "next/navigation"
import { useEffect, Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default function RequestPentestPage() {
  const { user, role, loading } = useAuth()
  const router = useRouter()

  // Redirect non-managers back to home
  useEffect(() => {
    if (!loading && user) {
      if (role === "admin") {
        router.replace("/scans")
      } else if (role !== "manager") {
        router.replace("/")
      }
    }
  }, [loading, user, role, router])

  const ChatSkeleton = () => (
    <div className="flex h-[calc(100vh-4rem)] flex-col w-full">
      <div className="p-4 border-b">
        <Skeleton className="h-8 w-64" />
      </div>
      <div className="flex-1 p-4 space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="flex gap-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ))}
      </div>
      <div className="p-4 border-t">
        <Skeleton className="h-12 w-full rounded-full" />
      </div>
    </div>
  );

  return (
    <DashboardLayout>
      <OptimizedChatInterface />
    </DashboardLayout>
  )
}
