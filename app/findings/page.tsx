"use client";

import { useState, useEffect, useMemo, useCallback, useDeferredValue, useTransition, Suspense } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import { VulnerabilitiesDashboard } from "../../components/vulnerabilities-dashboard";
import { VulnerabilityFilterToolbar } from "@/components/VulnerabilityFilterToolbar";
import { ShieldAlert } from "lucide-react";
import { useVulnerabilities } from "@/context/VulnerabilitiesContext";
import { VulnerabilitySeverity } from "@/types/vulnerability-types";

const FindingsSkeleton = () => (
  <div className="w-full 2xl:max-w-7xl mx-auto p-6 space-y-6">
    <div className="flex justify-between items-center">
      <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
      <div className="flex gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-16 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
        ))}
      </div>
    </div>
    <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    <div className="space-y-4">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
      ))}
    </div>
  </div>
);

export default function VulnerabilitiesPage() {
  const { filters, setFilters, clearFilters, vulnerabilities } = useVulnerabilities();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSeverities, setSelectedSeverities] = useState<VulnerabilitySeverity[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<("Open" | "Closed" | "Pending Retest")[]>(["Open"]);
  const [selectedScan, setSelectedScan] = useState<string | null>(null);
  const [scanNames, setScanNames] = useState<string[]>([]);
  const [isPending, startTransition] = useTransition();
  
  // Defer heavy computations
  const deferredVulnerabilities = useDeferredValue(vulnerabilities);

  useEffect(() => {
    const uniqueScanNames = [...new Set(deferredVulnerabilities.map((v) => v.scanName).filter(Boolean) as string[])];
    setScanNames(uniqueScanNames);
  }, [deferredVulnerabilities]);

  const handleSeverityChange = useCallback((severity: VulnerabilitySeverity, isChecked: boolean) => {
    setSelectedSeverities((prev) =>
      isChecked
        ? [...prev, severity]
        : prev.filter((s) => s !== severity)
    );
  }, []);

  const handleStatusChange = useCallback((status: "Open" | "Closed" | "Pending Retest", isChecked: boolean) => {
    setSelectedStatuses((prev) =>
      isChecked
        ? [...prev, status]
        : prev.filter((s) => s !== status)
    );
  }, []);

  const handleScanChange = useCallback((scanName: string | null) => {
    setSelectedScan(scanName);
  }, []);

  useEffect(() => {
    startTransition(() => {
      setFilters({
        ...filters,
        search: searchQuery,
        severity: selectedSeverities.length > 0 ? selectedSeverities : undefined,
        status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
        scanName: selectedScan,
      });
    });
  }, [searchQuery, selectedSeverities, selectedStatuses, selectedScan, setFilters]);

  const quickStats = useMemo(() => {
    const total = deferredVulnerabilities.length;
    const criticalHigh = deferredVulnerabilities.filter(v => v.severity === 'critical' || v.severity === 'high').length;
    const open = deferredVulnerabilities.filter(v => v.status === 'Open').length;
    const closed = deferredVulnerabilities.filter(v => v.status === 'Closed').length;
    return { total, criticalHigh, open, closed };
  }, [deferredVulnerabilities]);

  return (
    <DashboardLayout>
      <div className="w-full 2xl:max-w-7xl mx-auto p-6">
        {/* Enhanced Header Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10 w-full mx-auto">
          <div className="space-y-2">
            <h2 className="text-2xl font-semibold tracking-tight flex items-center gap-3 text-foreground">
              <div className="p-2 rounded-xl bg-primary/10 border border-primary/20">
                <ShieldAlert className="h-6 w-6 text-primary" />
              </div>
              Findings
            </h2>
          </div>
          
          {/* Quick Stats */}
          <div className="flex items-center gap-4">
            <div className="text-center p-2 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-violet">{quickStats.total}</div>
              <div className="text-xs text-muted-foreground font-medium">Total</div>
            </div>
            <div className="text-center p-2 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-red-500">
                {quickStats.criticalHigh}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Critical/High</div>
            </div>
            <div className="text-center p-2 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-blue-500">
                {quickStats.open}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Open</div>
            </div>
            <div className="text-center p-2 bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-green-500">
                {quickStats.closed}
              </div>
              <div className="text-xs text-muted-foreground font-medium">Closed</div>
            </div>
          </div>
        </div>

        {/* Filter Toolbar */}
        <div style={{ animationDelay: '100ms' }}>
          <VulnerabilityFilterToolbar
            onSeverityChange={handleSeverityChange}
            selectedSeverities={selectedSeverities}
            onStatusChange={handleStatusChange}
            selectedStatuses={selectedStatuses}
            onScanChange={handleScanChange}
            selectedScan={selectedScan}
            scanNames={scanNames}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </div>

        {/* Main Dashboard */}
        <div className="animate-slide-up" style={{ animationDelay: '200ms' }}>
          <Suspense fallback={<FindingsSkeleton />}>
            <VulnerabilitiesDashboard
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              clearFilters={clearFilters}
            />
          </Suspense>
        </div>
      </div>
    </DashboardLayout>
  );
}
