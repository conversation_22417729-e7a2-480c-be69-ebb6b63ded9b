"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Play, Clock, Users, Star, Award, ArrowLeft, XCircle } from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"

interface Video {
  id: string
  title: string
  description: string
  duration: string
  instructor: string
  rating: number
  students: number
  category: string
  embedUrl: string
  thumbnail: string
  progress?: number
  completed?: boolean
}

const videos: Video[] = [
  {
    id: "1",
    title: "Security Awareness for Developers",
    description: "This course introduces developers to the fundamentals of security awareness, focusing on cultivating a security mindset, understanding common pitfalls, and developing secure habits.",
    duration: "2 hours",
    instructor: "<PERSON>",
    rating: 4.0,
    students: 1500,
    category: "Web Security",
    embedUrl: "https://app.mindsmith.ai/course/cmckrn7hj001rjp07ddnhuduw/learn",
    thumbnail: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop&crop=center",
    progress: 0,
    completed: false
  },

  {
    id: "3",
    title: "Web Application Security Fundamentals",
    description: "Learn the basics of web security, including OWASP Top 10, SQL injection prevention, and XSS protection techniques.",
    duration: "4 hours",
    instructor: "Michael Thompson",
    rating: 4.5,
    students: 1234,
    category: "Web Security",
    embedUrl: "https://app.mindsmith.ai/course/cmckrn7hj001rjp07ddnhuduw/learn",
    thumbnail: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=center",
    progress: 0,
    completed: false
  }
]

export default function CoursePage() {
  const params = useParams()
  const courseId = params?.id as string
  const [isLoading, setIsLoading] = useState(false)
  const [showCourse, setShowCourse] = useState(false)
  
  const course = videos.find(v => v.id === courseId)
  
  if (!course || !courseId) {
    return (
      <DashboardLayout>
        <div className="container mx-auto p-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Course Not Found</h1>
            <Link href="/learn">
              <Button>Back to Courses</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-6">
        <Link href="/learn">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courses
          </Button>
        </Link>

        {showCourse ? (
          <div className="fixed inset-0 bg-black z-50 flex items-center justify-center fullscreen-iframe-container">
            <div className="relative w-full h-full">
              <iframe
                src={course.embedUrl}
                title={course.title}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                allowFullScreen
                className="w-full h-full border-0"
              ></iframe>
              <div className="absolute top-4 right-4 z-10 flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={() => setShowCourse(false)}
                  variant="secondary"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Close Course
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
            <div className="xl:col-span-3">
              <Card className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="aspect-video relative bg-muted flex items-center justify-center overflow-hidden">
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="absolute inset-0 w-full h-full object-cover blur-sm"
                    />
                    <div className="absolute inset-0 bg-black/30" />
                    <Button
                      size="lg"
                      onClick={() => setShowCourse(true)}
                      className="flex items-center gap-2 relative z-10"
                    >
                      <Play className="h-5 w-5" />
                      Open Course
                    </Button>
                  </div>
                  
                  <div className="p-8">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h1 className="text-3xl font-bold mb-2">{course.title}</h1>
                      <p className="text-muted-foreground text-lg mb-4">{course.description}</p>
                    </div>
                    {course.completed && (
                      <Badge className="bg-green-500 hover:bg-green-600">
                        <Award className="h-3 w-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {course.duration}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {course.students.toLocaleString()} students
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      {course.rating}
                    </div>
                    <Badge variant="secondary">{course.category}</Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-8">
                    Instructor: <span className="font-medium text-foreground">{course.instructor}</span>
                  </p>
                  
                  {course.progress && course.progress > 0 && (
                    <div className="mb-8">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Progress</span>
                        <span className="text-sm text-muted-foreground">{course.progress}%</span>
                      </div>
                      <Progress value={course.progress} className="h-2" />
                    </div>
                  )}
                  
                  <div className="border-t pt-8">
                    <h2 className="text-xl font-semibold mb-4">About this course</h2>
                    <div className="prose prose-sm max-w-none text-muted-foreground">
                      <p className="mb-6">{course.description}</p>
                      <div className="space-y-4">
                        <h3 className="font-medium text-foreground text-base">What you'll learn:</h3>
                        <ul className="list-disc list-inside space-y-2 text-sm">
                          <li>Core security concepts and best practices</li>
                          <li>Common vulnerability patterns and prevention</li>
                          <li>Hands-on security testing techniques</li>
                          <li>Real-world application scenarios</li>
                          <li>Industry-standard security tools and methodologies</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              </Card>
            </div>

            <div className="xl:col-span-1">
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Course Details</h3>
                  <div className="space-y-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">{course.duration}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Category:</span>
                      <p className="font-medium">{course.category}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Instructor:</span>
                      <p className="font-medium">{course.instructor}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Students:</span>
                      <p className="font-medium">{course.students.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rating:</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium">{course.rating}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
