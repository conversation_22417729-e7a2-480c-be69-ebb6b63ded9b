"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Play, Clock, Users, Star, Search, Award, BookOpen } from "lucide-react"

interface Video {
  id: string
  title: string
  description: string
  duration: string
  instructor: string
  rating: number
  students: number
  category: string
  embedUrl: string
  thumbnail: string
  progress?: number
  completed?: boolean
  featured?: boolean
}

const videos: Video[] = [
  {
    id: "1",
    title: "Security Awareness for Developers",
    description: "This course introduces developers to the fundamentals of security awareness, focusing on cultivating a security mindset, understanding common pitfalls, and developing secure habits.",
    duration: "2 hours",
    instructor: "<PERSON>",
    rating: 4.0,
    students: 1500,
    category: "Web Security",
    embedUrl: "https://app.mindsmith.ai/course/cmckrn7hj001rjp07ddnhuduw/learn",
    thumbnail: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=300&fit=crop&crop=center",
    progress: 0,
    completed: false
  },

  {
    id: "3",
    title: "Web Application Security Fundamentals",
    description: "Learn the basics of web security, including OWASP Top 10, SQL injection prevention, and XSS protection techniques.",
    duration: "4 hours",
    instructor: "Michael Thompson",
    rating: 4.5,
    students: 1234,
    category: "Web Security",
    embedUrl: "https://app.mindsmith.ai/course/cmckrn7hj001rjp07ddnhuduw/learn",
    thumbnail: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=center",
    progress: 0,
    completed: false,
    featured: true
  }
]

export default function LearnPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>("All")
  const [searchQuery, setSearchQuery] = useState("")

  const categories = ["All", ...Array.from(new Set(videos.map(v => v.category)))]
  const filteredVideos = videos.filter(video => {
    const matchesCategory = selectedCategory === "All" || video.category === selectedCategory
    const matchesSearch = video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         video.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })
  
  const continueWatching = videos.filter(v => v.progress && v.progress > 0 && !v.completed)
  const completedCount = videos.filter(v => v.completed).length

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-12">
      <div className="flex flex-col space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">Developer Studio</h1>
            <p className="text-muted-foreground">
              Master cybersecurity concepts with our comprehensive learning platform
            </p>
            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Award className="h-4 w-4" />
                {completedCount} completed
              </div>
              <div className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                {videos.length} total courses
              </div>
            </div>
          </div>
          
          <div className="relative w-full md:w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search courses..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
            />
          </div>
        </div>

        {continueWatching.length > 0 && (
          <div className="space-y-6 pb-8 border-b">
            <h2 className="text-2xl font-bold">My Learning</h2>
            
            <div className="space-y-4 mt-6">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Play className="h-5 w-5" />
                Continue Learning
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {continueWatching.slice(0, 3).map((video) => (
                  <Card 
                    key={video.id}
                    className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] group"
                    onClick={() => window.location.href = `/learn/${video.id}`}
                  >
                    <CardContent className="p-4">
                      <div className="relative mb-3">
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-24 object-cover rounded transition-all duration-200 group-hover:brightness-110"
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded transition-all duration-200 group-hover:bg-black/40">
                          <Play className="h-6 w-6 text-white" />
                        </div>
                        <Progress value={video.progress} className="absolute bottom-1 left-1 right-1 h-1" />
                      </div>
                      <h4 className="font-medium text-sm line-clamp-2 mb-1">{video.title}</h4>
                      <p className="text-xs text-muted-foreground">{video.progress}% complete</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6 pb-8 border-b">
          <h2 className="text-2xl font-bold">Featured Courses</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {videos.filter(v => v.featured).slice(0, 2).map((video) => (
              <Card 
                key={video.id}
                className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] group overflow-hidden"
                onClick={() => window.location.href = `/learn/${video.id}`}
              >
                <div className="relative">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full h-48 object-cover transition-all duration-200 group-hover:brightness-110"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20 transition-all duration-200 group-hover:bg-black/40">
                    <Play className="h-10 w-10 text-white transition-all duration-200 group-hover:scale-125" />
                  </div>
                  <Badge className="absolute top-3 left-3 bg-primary">Featured</Badge>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {video.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">{video.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{video.rating}</span>
                      <span className="text-muted-foreground">({video.students} students)</span>
                    </div>
                    <Badge variant="secondary">{video.category}</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="space-y-6 pb-8 border-b">
          <h2 className="text-2xl font-bold">Browse by Category</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {categories.filter(c => c !== "All").map((category) => {
              const categoryCount = videos.filter(v => v.category === category).length
              return (
                <Card 
                  key={category}
                  className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] group p-6 text-center"
                  onClick={() => setSelectedCategory(category)}
                >
                  <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">{category}</h3>
                  <p className="text-sm text-muted-foreground">{categoryCount} courses</p>
                </Card>
              )
            })}
          </div>
        </div>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">All Courses</h2>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="transition-all duration-200 hover:scale-105 hover:shadow-md"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredVideos.map((video) => (
              <Card 
                key={video.id}
                className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] group overflow-hidden"
                onClick={() => window.location.href = `/learn/${video.id}`}
              >
                <div className="relative">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full h-40 object-cover transition-all duration-200 group-hover:brightness-110"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20 transition-all duration-200 group-hover:bg-black/40">
                    <Play className="h-8 w-8 text-white transition-all duration-200 group-hover:scale-125" />
                  </div>
                  {video.completed && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <Award className="h-3 w-3 text-white" />
                    </div>
                  )}
                  {video.progress && video.progress > 0 && (
                    <Progress value={video.progress} className="absolute bottom-0 left-0 right-0 h-1" />
                  )}
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                    {video.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {video.description}
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <Badge variant="secondary">{video.category}</Badge>
                    <span className="text-muted-foreground">{video.duration}</span>
                  </div>
                  <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      {video.rating}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {video.students}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
      </div>
    </DashboardLayout>
  )
}