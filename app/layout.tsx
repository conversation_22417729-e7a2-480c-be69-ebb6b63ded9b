import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Plus_Jakarta_Sans } from "next/font/google"
import "./globals.css"
import "../styles/chat-optimizations.css"
import "../styles/dashboard-optimizations.css"
import "../styles/pentest-performance.css"
import "../styles/findings-optimizations.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ScanCountProvider } from "@/context/ScanCountContext"
import { ScanProvider } from "@/context/ScanContext"
import { TeamMessagesProvider } from "@/context/TeamMessagesContext"
import { ConversationsProvider } from "@/context/ConversationsContext"
import { MessageQueueProvider } from "@/context/MessageQueueContext"
import { NotificationsProvider } from "@/context/NotificationsContext"
import { VulnerabilitiesProvider } from "@/context/VulnerabilitiesContext"
import { JsLoadedScript } from "./js-loaded"
import { PerformanceOptimizer } from "@/components/performance-optimizer"

const jakarta = Plus_Jakarta_Sans({
  subsets: ["latin"],
  weight: ["500", "600", "800"], // Medium, Semi-Bold, Extra Bold
  variable: "--font-jakarta",
  display: "swap", // Add font-display: swap to prevent render blocking
  preload: true,   // Ensure the font is preloaded
})

export const metadata: Metadata = {
  title: "DeepScan by GrowthGuard",
  description: "AI-powered penetration testing platform",
  applicationName: "DeepScan",
  authors: [{ name: "GrowthGuard" }],
  keywords: ["penetration testing", "security", "AI", "cybersecurity", "vulnerability scanning"],
  creator: "GrowthGuard",
  publisher: "GrowthGuard",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://app.getdeepscan.com'),
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: "/favicon.svg", type: "image/svg+xml" }
    ],
    shortcut: [
      { url: "/favicon.svg", type: "image/svg+xml" }
    ],
    apple: [
      { url: "/growthguard-logo.svg", type: "image/svg+xml" }
    ]
  },
  openGraph: {
    title: "DeepScan by GrowthGuard",
    description: "AI-powered penetration testing platform",
    url: "https://app.getdeepscan.com",
    siteName: "DeepScan by GrowthGuard",
    images: [
      {
        url: '/growthguard-logo.svg',
        width: 800,
        height: 600,
        alt: 'DeepScan by GrowthGuard Logo',
      }
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DeepScan by GrowthGuard',
    description: 'AI-powered penetration testing platform',
    creator: '@GrowthGuard',
    images: ['/growthguard-logo.svg'],
  },
  verification: {
    // Add verification tokens if you have them
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={jakarta.variable}>
      <head>
        {/* Preload critical assets */}
        <link rel="preload" href="/growthguard-logo.svg" as="image" type="image/svg+xml" />
        {/* Preconnect to Google Fonts to improve font loading performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={jakarta.className}>
        {/* Apply js-loaded class after page load */}
        <JsLoadedScript />
        {/* Add performance optimizer */}
        <PerformanceOptimizer />
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <div className="min-h-screen growthguard-gradient-bg dark:bg-[#111827]">
          <ScanProvider>
            <ScanCountProvider>
              <TeamMessagesProvider>
                <ConversationsProvider>
                  <MessageQueueProvider>
                    <NotificationsProvider>
                      <VulnerabilitiesProvider>
                        {children}
                      </VulnerabilitiesProvider>
                    </NotificationsProvider>
                  </MessageQueueProvider>
                </ConversationsProvider>
              </TeamMessagesProvider>
            </ScanCountProvider>
          </ScanProvider>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
