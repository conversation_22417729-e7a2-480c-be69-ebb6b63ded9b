"use client"

import { DashboardLayout } from "@/components/dashboard-layout"
import { Loader2, MessagesSquare } from "lucide-react"

export default function Loading() {
  return (
    <DashboardLayout>
      <div className="flex flex-col h-[calc(100vh-4rem)] animate-fadeIn">
        {/* Header - Skeleton */}
        <div className="flex items-center justify-between border-b px-6 py-4">
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <MessagesSquare className="h-5 w-5 text-primary" />
              <h3 className="text-2xl font-bold">Support</h3>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              <span className="inline-flex items-center">
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                Loading messages...
              </span>
            </p>
          </div>
        </div>

        {/* Content - Skeleton */}
        <div className="md:flex flex-1 overflow-hidden">
          {/* Messages List Skeleton */}
          <div className="w-full md:w-1/3 border-r flex flex-col">
            <div className="p-4 border-b">
              <div className="relative h-9 bg-muted/30 rounded-md animate-pulse"></div>
            </div>
            <div className="flex-1 p-2 space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="p-3 rounded-lg border animate-pulse">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-8 w-8 rounded-full bg-muted"></div>
                    <div className="h-4 w-32 bg-muted rounded"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 w-3/4 bg-muted rounded"></div>
                    <div className="h-3 w-1/2 bg-muted rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Message Detail Skeleton */}
          <div className="hidden md:flex flex-1 flex-col">
            <div className="p-6 animate-pulse">
              <div className="flex items-center gap-3 mb-6">
                <div className="h-10 w-10 rounded-full bg-muted"></div>
                <div>
                  <div className="h-4 w-32 bg-muted rounded mb-2"></div>
                  <div className="h-3 w-24 bg-muted rounded"></div>
                </div>
              </div>
              <div className="space-y-3 mb-8">
                <div className="h-4 w-3/4 bg-muted rounded"></div>
                <div className="h-4 w-full bg-muted rounded"></div>
                <div className="h-4 w-2/3 bg-muted rounded"></div>
              </div>
              <div className="h-px w-full bg-muted my-6"></div>
              <div className="flex items-center gap-3 mb-4">
                <div className="h-10 w-10 rounded-full bg-muted"></div>
                <div className="h-4 w-32 bg-muted rounded"></div>
              </div>
              <div className="space-y-3">
                <div className="h-4 w-full bg-muted rounded"></div>
                <div className="h-4 w-2/3 bg-muted rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
