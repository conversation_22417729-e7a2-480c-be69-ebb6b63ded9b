"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image"; // Import next/image
import { auth, sendSignInLink } from "../../lib/firebase";
import { signInWithEmailAndPassword, getIdTokenResult } from "firebase/auth";
import Cookies from 'js-cookie';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Turnstile } from '@marsidev/react-turnstile';
import { Checkbox } from "@/components/ui/checkbox";
import { Mail, Lock, TerminalSquare } from 'lucide-react';
import growthguardLogo from "../../public/growthguard-logo.svg";


const mapFirebaseError = (errorCode: string): string => {
    switch (errorCode) {
        case 'auth/user-not-found':
        case 'auth/wrong-password':
        case 'auth/invalid-credential':
            return 'Invalid email or password.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address.';
        default:
            return 'An authentication error occurred. Please try again.';
    }
}

export default function LoginPage() {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");
    const [loading, setLoading] = useState(false);
    const [isDevelopment, setIsDevelopment] = useState(false);
    const [bypassCaptcha, setBypassCaptcha] = useState(false);
    const [passwordlessEmail, setPasswordlessEmail] = useState("");
    const [passwordlessLoading, setPasswordlessLoading] = useState(false);
    const [passwordlessError, setPasswordlessError] = useState("");
    const [passwordlessSuccess, setPasswordlessSuccess] = useState("");
    const [passwordlessTurnstileToken, setPasswordlessTurnstileToken] = useState<string | null>(null);
    const passwordlessTurnstileRef = useRef<any>(null);
    const router = useRouter();

    useEffect(() => {
        const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
        setIsDevelopment(isDev);
    }, []);

    const handleAuth = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setLoading(true);
        setError("");

        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const idTokenResult = await getIdTokenResult(userCredential.user);
            const userRole = idTokenResult.claims.role as string || 'client';

            Cookies.set('user_role', userRole, { expires: 7, path: '/', sameSite: 'strict', secure: process.env.NODE_ENV === 'production' });

            if (userRole === 'admin') {
                router.push("/scans");
            } else if (userRole === 'manager') {
                router.push("/dashboard");
            } else {
                router.push("/");
            }
        } catch (err: any) {
            setError(mapFirebaseError(err.code));
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordlessSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setPasswordlessLoading(true);
        setPasswordlessError("");
        setPasswordlessSuccess("");

        if (!passwordlessEmail.trim()) {
            setPasswordlessError("Please enter your email address.");
            setPasswordlessLoading(false);
            return;
        }

        if (!passwordlessTurnstileToken && !(isDevelopment && bypassCaptcha)) {
            setPasswordlessError("Please complete the CAPTCHA verification.");
            setPasswordlessLoading(false);
            return;
        }

        try {
            const verifyResponse = await fetch('/api/verify-turnstile', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    token: passwordlessTurnstileToken,
                    bypassDev: isDevelopment && bypassCaptcha,
                }),
            });

            const verifyData = await verifyResponse.json();
            if (!verifyResponse.ok || !verifyData.success) {
                setPasswordlessError("CAPTCHA verification failed. Please try again.");
                setPasswordlessLoading(false);
                passwordlessTurnstileRef.current?.reset();
                setPasswordlessTurnstileToken(null);
                return;
            }

            // Check if user exists, or create them
            const userCheckResponse = await fetch('/api/users/check', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: passwordlessEmail, create: true }),
            });

            const userCheckData = await userCheckResponse.json();
            if (!userCheckResponse.ok || !userCheckData.exists) {
                setPasswordlessError(userCheckData.message || "Failed to verify or create your account. Please try again.");
                setPasswordlessLoading(false);
                return;
            }

            const protocol = window.location.protocol;
            const host = window.location.host;
            const redirectUrl = `${protocol}//${host}/auth/action-handler`;

            await sendSignInLink(passwordlessEmail, redirectUrl);

            setPasswordlessSuccess("Sign-in link sent successfully. Please check your email.");
        } catch (error: any) {
            console.error("Error in passwordless sign-in:", error);
            setPasswordlessError(error.message || "An unexpected error occurred. Please try again.");
        } finally {
            setPasswordlessLoading(false);
            passwordlessTurnstileRef.current?.reset();
            setPasswordlessTurnstileToken(null);
        }
    };


    return (
        <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2 xl:min-h-screen">
            <div className="flex items-center justify-center py-12">
                <div className="mx-auto grid w-[350px] gap-6 animate-slide-up-fade">
                    <div className="grid gap-2 text-center">
                        <div className="flex justify-center items-center gap-2 mb-4">
                            <Image src={growthguardLogo} alt="DeepScan Logo" width="33" height="40" />
                            <span className="text-2xl font-bold">DeepScan</span>
                        </div>
                        <h1 className="text-3xl font-bold">Welcome</h1>
                        <p className="text-balance text-muted-foreground">
                            Enter your email to get started
                        </p>
                    </div>
                    <form onSubmit={handlePasswordlessSignIn} className="grid gap-4">
                        {passwordlessSuccess && (
                            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 text-sm rounded-md" role="alert">
                                {passwordlessSuccess}
                            </div>
                        )}
                        <div className="grid gap-2">
                            <Label htmlFor="passwordlessEmail">Email</Label>
                            <div className="relative">
                                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input id="passwordlessEmail" type="email" placeholder="<EMAIL>" value={passwordlessEmail} onChange={(e) => setPasswordlessEmail(e.target.value)} required className="pl-10" disabled={passwordlessLoading} />
                            </div>
                        </div>
                        {passwordlessError && (
                            <div className="text-red-600 dark:text-red-400 text-sm text-center font-medium" role="alert">
                                {passwordlessError}
                            </div>
                        )}
                        {!(isDevelopment && bypassCaptcha) && (
                            <div className="flex justify-center">
                                <Turnstile siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || ""} onSuccess={setPasswordlessTurnstileToken} onError={() => setPasswordlessError("CAPTCHA verification failed.")} onExpire={() => setPasswordlessTurnstileToken(null)} ref={passwordlessTurnstileRef} />
                            </div>
                        )}
                        <Button type="submit" className="w-full" disabled={passwordlessLoading || (!(isDevelopment && bypassCaptcha) && !passwordlessTurnstileToken)}>
                            {passwordlessLoading ? "Sending..." : "Get Sign-in Link"}
                        </Button>
                    </form>

                    {isDevelopment && (
                        <>
                            <div className="relative my-4">
                                <div className="absolute inset-0 flex items-center"><span className="w-full border-t" /></div>
                                <div className="relative flex justify-center text-xs uppercase"><span className="bg-background px-2 text-muted-foreground">Dev Login</span></div>
                            </div>
                            <form onSubmit={handleAuth} className="grid gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="email">Dev Email</Label>
                                    <div className="relative">
                                        <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input id="email" type="email" placeholder="<EMAIL>" value={email} onChange={(e) => setEmail(e.target.value)} required className="pl-10" disabled={loading} />
                                    </div>
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="password">Password</Label>
                                    <div className="relative">
                                        <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input id="password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} required className="pl-10" disabled={loading} />
                                    </div>
                                </div>
                                {error && (
                                    <div className="text-red-600 dark:text-red-400 text-sm text-center font-medium" role="alert">
                                        {error}
                                    </div>
                                )}
                                <Button type="submit" className="w-full" variant="outline" disabled={loading}>
                                    {loading ? "Logging in..." : "Login with Password"}
                                </Button>
                            </form>
                            <div className="flex items-center space-x-2 mt-4">
                                <Checkbox
                                    id="bypassCaptcha"
                                    checked={bypassCaptcha}
                                    onCheckedChange={(checked) => setBypassCaptcha(!!checked)}
                                />
                                <Label htmlFor="bypassCaptcha">Bypass CAPTCHA in Dev Mode</Label>
                            </div>
                        </>
                    )}
                </div>
            </div>
            <div className="hidden bg-muted lg:block">
                <div className="flex flex-col items-center justify-center h-full text-center px-12 bg-gradient-to-br from-violet-100 to-blue-100 dark:from-[#1E293B] dark:to-black">
                    <TerminalSquare className="h-24 w-24 text-primary mb-6 animate-icon-pulse" />
                    <h2 className="text-4xl font-bold mb-4">Uncover Vulnerabilities, Faster</h2>
                    <p className="text-muted-foreground text-lg max-w-md">
                        The all-in-one platform for streamlined penetration testing and vulnerability management.
                    </p>
                </div>
            </div>
        </div>
    );
}
