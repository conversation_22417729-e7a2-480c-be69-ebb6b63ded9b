import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/firebase-admin";
import { ConversationService } from "@/lib/services/conversation-service";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Ensure params is properly awaited in Next.js App Router
  const resolvedParams = await Promise.resolve(params);
  const id = resolvedParams.id;

  // Get authorization token
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate conversation ID
  if (!id) {
    return NextResponse.json(
      { error: "Invalid conversation ID" },
      { status: 400 }
    );
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    const conversation = await ConversationService.getConversation(id, userId);

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ conversation });
  } catch (error: any) {
    console.error("Error in conversation API:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Ensure params is properly awaited in Next.js App Router
  const resolvedParams = await Promise.resolve(params);
  const id = resolvedParams.id;

  // Get authorization token
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate conversation ID
  if (!id) {
    return NextResponse.json(
      { error: "Invalid conversation ID" },
      { status: 400 }
    );
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    // Get request body
    const updates = await request.json();

    // Check if we're adding a message
    if (updates.message && typeof updates.message === "object") {
      console.log("Adding message to conversation:", { id, userId, message: updates.message });

      const message = updates.message;
      const success = await ConversationService.addMessage(id, userId, message);

      console.log("Message added successfully:", success);

      if (!success) {
        return NextResponse.json(
          { error: "Failed to add message to conversation" },
          { status: 404 }
        );
      }

      return NextResponse.json({ success: true });
    }

    // Otherwise, update the conversation
    const success = await ConversationService.updateConversation(id, userId, updates);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to update conversation" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in conversation API:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Ensure params is properly awaited in Next.js App Router
  const resolvedParams = await Promise.resolve(params);
  const id = resolvedParams.id;

  console.log("DELETE request received for conversation:", id);

  // Get authorization token
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.error("Unauthorized request - missing or invalid auth header");
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    console.error("Unauthorized request - no token extracted from header");
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate conversation ID
  if (!id) {
    console.error("Invalid conversation ID in request params");
    return NextResponse.json(
      { error: "Invalid conversation ID" },
      { status: 400 }
    );
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    console.log("Authenticated user:", userId, "attempting to delete conversation:", id);

    const success = await ConversationService.deleteConversation(id, userId);
    console.log("Delete operation result:", success);

    if (!success) {
      console.error("Failed to delete conversation:", id, "for user:", userId);
      return NextResponse.json(
        { error: "Failed to delete conversation" },
        { status: 404 }
      );
    }

    console.log("Successfully deleted conversation:", id);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in conversation delete API:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message}` },
      { status: 500 }
    );
  }
}
