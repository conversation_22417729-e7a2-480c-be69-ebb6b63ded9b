import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/firebase-admin";
import { db } from "@/lib/firebase-admin";
import crypto from "crypto";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Ensure params is properly awaited in Next.js App Router
  const resolvedParams = await Promise.resolve(params);
  const conversationId = resolvedParams.id;

  console.log("Status update request received for conversation:", conversationId);

  // Get authorization token
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate conversation ID
  if (!conversationId) {
    return NextResponse.json(
      { error: "Invalid conversation ID" },
      { status: 400 }
    );
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    // Get request body
    const { scanId, oldStatus, newStatus } = await request.json();
    if (!scanId || !newStatus) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the conversation
    const conversationRef = db.collection("conversations").doc(conversationId);
    const conversationDoc = await conversationRef.get();

    if (!conversationDoc.exists) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    const conversationData = conversationDoc.data();

    // Check for duplicate status update messages to prevent adding the same message multiple times
    // This is especially important when the page refreshes and components remount
    const existingMessages = conversationData?.messages || [];

    // Create the content we're looking for to detect duplicates
    const statusUpdateContent = `Pentest status updated from "${oldStatus || 'pending'}" to "${newStatus}".`;

    // Define the message type
    interface Message {
      id: string;
      role: string;
      content: string;
      timestamp: string;
    }

    // Look for recent duplicate messages (within the last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
    const recentDuplicateExists = existingMessages.some((msg: Message) =>
      msg.role === "system" &&
      msg.content === statusUpdateContent &&
      msg.timestamp > fiveMinutesAgo
    );

    // If a recent duplicate exists, don't add another message
    if (recentDuplicateExists) {
      console.log(`Preventing duplicate status update message for pentest ${scanId} in conversation ${conversationId}`);
      return NextResponse.json({
        success: true,
        message: "Duplicate status update prevented"
      });
    }

    // Create a system message about the status change
    const message: Message = {
      id: crypto.randomUUID(),
      role: "system",
      content: statusUpdateContent,
      timestamp: new Date().toISOString(),
    };

    // Add the message to the messages array
    const updatedMessages = [...existingMessages, message];

    await conversationRef.update({
      messages: updatedMessages,
      updatedAt: new Date().toISOString(),
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error adding status update message:", error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message}` },
      { status: 500 }
    );
  }
}
