"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { completeSignInWithEmailLink, isEmailSignInLink } from "@/lib/firebase";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Cookies from 'js-cookie';

export default function ActionHandler() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [needsEmail, setNeedsEmail] = useState(false);

  useEffect(() => {
    // Check if this is a sign-in link
    if (typeof window !== "undefined") {
      const isSignInLink = isEmailSignInLink(window.location.href);
      
      if (!isSignInLink) {
        setError("Invalid or expired sign-in link.");
        setLoading(false);
        return;
      }

      // Try to get the email from localStorage
      const savedEmail = localStorage.getItem("emailForSignIn");
      
      if (savedEmail) {
        setEmail(savedEmail);
        handleSignIn(savedEmail);
      } else {
        // We need the user to provide their email
        setNeedsEmail(true);
        setLoading(false);
      }
    }
  }, []);

  const handleSignIn = async (emailToUse: string) => {
    setLoading(true);
    setError("");
    
    try {
      const result = await completeSignInWithEmailLink(emailToUse);
      const idTokenResult = await result.user.getIdTokenResult(true); // Force refresh
      const customClaims = idTokenResult.claims;
      const userRole = (customClaims.role as string) || "client";

      Cookies.set("user_role", userRole, {
        expires: 7,
        path: "/",
        sameSite: "strict",
        secure: process.env.NODE_ENV === "production",
      });

      setSuccess("Sign-in successful! Redirecting...");

      setTimeout(() => {
        if (userRole === "admin") {
          router.push("/scans");
        } else if (userRole === "manager") {
          router.push("/dashboard");
        } else {
          router.push("/");
        }
      }, 1500);
    } catch (error: any) {
      console.error("Error completing sign-in:", error);
      let errorMessage = "Failed to complete sign-in. Please try again.";
      if (error.code === 'auth/invalid-action-code') {
        errorMessage = "The sign-in link is invalid or has expired. Please request a new one.";
      } else if (error.message.includes('does not match')) {
        errorMessage = "The email provided does not match the sign-in email address.";
      }
      setError(errorMessage);
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!email.trim()) {
      setError("Please enter your email address.");
      return;
    }
    handleSignIn(email);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-white to-[#F5EFFF] dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Complete Sign In</CardTitle>
          <CardDescription className="text-center">
            {needsEmail 
              ? "Please enter the email address you used to sign up." 
              : "Verifying your sign-in link..."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 mb-4 text-red-800 dark:text-red-300">
              {error}
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3 mb-4 text-green-800 dark:text-green-300">
              {success}
            </div>
          )}

          {needsEmail ? (
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={loading}
                  />
                </div>
                <Button type="submit" className="w-full bg-[#843DF5]" disabled={loading}>
                  {loading ? "Processing..." : "Complete Sign In"}
                </Button>
              </div>
            </form>
          ) : (
            <div className="flex justify-center">
              {loading && (
                <div className="flex flex-col items-center space-y-2">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#843DF5]"></div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Verifying your sign-in link...</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter>
          <div className="text-center text-sm text-gray-500 dark:text-gray-400 w-full">
            <a href="/login" className="text-[#843DF5] hover:underline">
              Return to login
            </a>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
