"use client";

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useScans } from "@/context/ScanContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import {
  Users,
  UserPlus,
  User,
  Shield,
  Activity,
  Loader2,
  Trash2,
  AlertCircle,
  X,
  RefreshCw
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/dashboard-layout";

const REFRESH_INTERVAL_MS = 60 * 1000; // 1 minute
const MEMBER_DATA_CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

interface TeamMember {
  id: string;
  email: string;
  displayName?: string;
  scanCount?: number; // This will be populated client-side
}

interface TeamData {
  id: string;
  managerId: string;
  managerEmail: string;
  teamName: string;
  teamMembers: string[]; // Array of user IDs (for backward compatibility)
  teamMemberEmails: string[]; // Array of emails (for backward compatibility)
  teamMemberUids?: Record<string, string>; // Map of email to UID (primary source of truth)
}

interface Scan {
  id: string;
  status: 'completed' | 'pending' | 'in-progress' | 're-test';
  userId?: string;
  userEmail?: string;
  name?: string;
  asset_type?: string;
  requestedAt: string | Date;
  criticalVulnerabilities: number;
  highVulnerabilities: number;
  mediumVulnerabilities: number;
  lowVulnerabilities: number;
}

export default function TeamDashboardPage() {
  const router = useRouter();
  const { user, role, loading: authLoading } = useAuth();
  const { scans, fetchData: fetchOrganizationScans } = useScans(); // scans is Scan[]
  const teamMemberScans = useMemo(() => {
    if (!scans) return {};
    return scans.reduce((acc, scan) => {
      const key = scan.userId || (scan.userEmail ? `email-${scan.userEmail}` : null);
      if (key) {
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(scan);
      }
      return acc;
    }, {} as Record<string, Scan[]>);
  }, [scans]);

  const [team, setTeam] = useState<TeamData | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isTeamLoading, setIsTeamLoading] = useState(false); // For team info specifically
  const [isMembersLoading, setIsMembersLoading] = useState(false); // For member list
  const [isScansLoading, setIsScansLoading] = useState(false); // For scan data refresh
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isDeletingMember, setIsDeletingMember] = useState(false);

  const [error, setError] = useState<string | null>(null);

  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const [isDeleteMemberModalOpen, setIsDeleteMemberModalOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null);

  const lastMembersFetchTimeRef = useRef(0);
  const isFetchingRef = useRef(false); // General fetch lock

  // Redirect if not a manager or auth still loading
  useEffect(() => {
    if (!authLoading && role && role !== "manager") {
      router.replace("/");
    }
  }, [authLoading, role, router]);

  const getMemberScanCount = useCallback((member: TeamMember, allScans: Scan[], teamScansData: Record<string, Scan[]>): number => {
    const uniqueScanIds = new Set<string>();
    const combinedScans: Scan[] = [];

    // Scans associated by member ID in teamScansData
    if (teamScansData[member.id]) {
      combinedScans.push(...teamScansData[member.id]);
    }
    // Scans associated by email key in teamScansData
    if (member.email && teamScansData[`email-${member.email}`]) {
      combinedScans.push(...teamScansData[`email-${member.email}`]);
    }
    // Scans from the global 'scans' array matching this member
    combinedScans.push(...allScans.filter(s => s.userId === member.id || (s.userEmail && s.userEmail === member.email)));

    const uniqueScans = combinedScans.filter(s => {
      if (!s.id || uniqueScanIds.has(s.id)) return false;
      uniqueScanIds.add(s.id);
      return true;
    });
    return uniqueScans.length;
  }, []);

  const processTeamMembersWithScans = useCallback((rawMembers: Omit<TeamMember, 'scanCount'>[]): TeamMember[] => {
    if (!rawMembers) return [];
    return rawMembers.map(member => ({
      ...member,
      scanCount: getMemberScanCount(member as TeamMember, scans, teamMemberScans),
    }));
  }, [getMemberScanCount, scans, teamMemberScans]);


  const fetchTeamAndMembers = useCallback(async (forceRefreshMembers = false) => {
    if (!user || isFetchingRef.current) return;

    isFetchingRef.current = true;
    setIsTeamLoading(true);
    if (forceRefreshMembers) setIsMembersLoading(true);
    setError(null);

    try {
      const idToken = await user.getIdToken();

      // Fetch team information
      const teamResponse = await fetch("/api/teams", {
        headers: { "Authorization": `Bearer ${idToken}` }
      });
      if (!teamResponse.ok) {
        const errData = await teamResponse.json().catch(() => ({}));
        throw new Error(`Failed to fetch team data: ${errData.error || teamResponse.statusText}`);
      }
      const teamDataResponse = await teamResponse.json();
      const currentTeam = teamDataResponse.teams && teamDataResponse.teams.length > 0 ? teamDataResponse.teams[0] : null;
      setTeam(currentTeam);

      // Fetch team members if needed
      const now = Date.now();
      if (forceRefreshMembers || now - lastMembersFetchTimeRef.current > MEMBER_DATA_CACHE_DURATION_MS || teamMembers.length === 0) {
        setIsMembersLoading(true); // Ensure loading state is true if we are fetching members
        const membersResponse = await fetch("/api/teams/members", {
          headers: { "Authorization": `Bearer ${idToken}` }
        });
        if (!membersResponse.ok) {
          const errData = await membersResponse.json().catch(() => ({}));
          throw new Error(`Failed to fetch team members: ${errData.error || membersResponse.statusText}`);
        }
        const membersData = await membersResponse.json();
        setTeamMembers(processTeamMembersWithScans(membersData.teamMemberDetails || []));
        lastMembersFetchTimeRef.current = now;
      } else {
        // If not fetching members, still update their scan counts with latest scan data
        setTeamMembers(prevMembers => processTeamMembersWithScans(prevMembers));
      }
    } catch (err: any) {
      console.error("Error in fetchTeamAndMembers:", err); // Kept for debugging
      setError(err.message);
    } finally {
      setIsTeamLoading(false);
      setIsMembersLoading(false); // Always reset members loading here
      isFetchingRef.current = false;
    }
  }, [user, processTeamMembersWithScans, teamMembers.length]); // Added teamMembers.length

  // Initial data load
  useEffect(() => {
    if (user && role === "manager" && !authLoading) {
      const loadInitialData = async () => {
        setIsInitialLoading(true);
        setIsScansLoading(true);
        try {
          await fetchOrganizationScans('organization', true); // Fetch scans first
        } catch (e) {
          console.error("Initial scan fetch failed:", e); // Kept
          setError("Failed to load initial scan data.");
        } finally {
          setIsScansLoading(false);
        }
        // fetchTeamAndMembers will be triggered by scans/teamMemberScans update if needed,
        // or called directly if those contexts don't trigger re-renders robustly.
        // For simplicity, we call it directly after scans are likely updated.
        await fetchTeamAndMembers(true); // Then fetch team and members
        setIsInitialLoading(false);
      };
      loadInitialData();

      const intervalId = setInterval(async () => {
        if (user && role === "manager") {
          await fetchOrganizationScans('organization', false); // Periodically refresh scans (non-blocking)
          fetchTeamAndMembers(false); // Periodically refresh team/members (non-blocking for members if cache is valid)
        }
      }, REFRESH_INTERVAL_MS);
      return () => clearInterval(intervalId);
    }
  }, [user, role, authLoading, fetchOrganizationScans]); // Removed fetchTeamAndMembers from here to avoid loop

  // Effect to update member scan counts when scan data changes
  useEffect(() => {
    if (teamMembers.length > 0 && (scans.length > 0 || Object.keys(teamMemberScans).length > 0) && !isMembersLoading && !isTeamLoading) {
       // Avoid updating if a fetch is in progress to prevent race conditions
      setTeamMembers(prevMembers => processTeamMembersWithScans(prevMembers));
    }
  }, [scans, teamMemberScans, processTeamMembersWithScans, teamMembers.length, isMembersLoading, isTeamLoading]);


  const handleAddMember = async () => {
    if (!newMemberEmail.trim() || !user) {
      toast({ title: "Error", description: "Please enter an email address.", variant: "destructive" });
      return;
    }
    setIsAddingMember(true);
    try {
      const idToken = await user.getIdToken();
      // Check if user exists
      const checkUserResponse = await fetch("/api/users/check", {
        method: "POST", headers: { "Content-Type": "application/json", "Authorization": `Bearer ${idToken}` },
        body: JSON.stringify({ email: newMemberEmail }),
      });
      if (!checkUserResponse.ok) throw new Error("Failed to verify user existence.");
      const userData = await checkUserResponse.json();
      if (!userData.exists) {
        toast({ title: "User not found", description: "This user doesn't exist. Only registered users can be added.", variant: "destructive" });
        setIsAddingMember(false);
        return;
      }

      let currentTeamId = team?.id;
      if (!currentTeamId) { // Create team if it doesn't exist
        const createTeamResponse = await fetch("/api/teams", {
          method: "POST", headers: { "Content-Type": "application/json", "Authorization": `Bearer ${idToken}` },
          body: JSON.stringify({ teamName: `${user.displayName || user.email}'s Team`, teamMemberEmails: [] }), // Add newMemberEmail here directly
        });
        if (!createTeamResponse.ok) throw new Error("Failed to create a new team.");
        const newTeamData = await createTeamResponse.json();
        setTeam(newTeamData.team);
        currentTeamId = newTeamData.team.id;
      }

      // Add member to existing or newly created team
      const addMemberResponse = await fetch("/api/teams", {
        method: "PUT", headers: { "Content-Type": "application/json", "Authorization": `Bearer ${idToken}` },
        body: JSON.stringify({ teamId: currentTeamId, teamMemberEmails: [...(team?.teamMemberEmails || []), newMemberEmail] }),
      });
      if (!addMemberResponse.ok) {
         const errorData = await addMemberResponse.json().catch(() => ({}));
         if (errorData.error?.includes("already a member")) {
             toast({ title: "Member already exists", description: `${newMemberEmail} is already in your team.`, variant: "default" });
         } else {
             throw new Error(errorData.error || "Failed to add team member.");
         }
      } else {
        toast({ title: "Team member added", description: `${newMemberEmail} has been added.` });
      }

      setNewMemberEmail("");
      setIsAddMemberModalOpen(false);
      await fetchTeamAndMembers(true); // Force refresh members list
    } catch (err: any) {
      toast({ title: "Error", description: err.message, variant: "destructive" });
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleDeleteMember = async () => {
    if (!memberToDelete || !team || !user) return;
    setIsDeletingMember(true);
    try {
      const idToken = await user.getIdToken();
      const updatedEmails = team.teamMemberEmails.filter(email => email !== memberToDelete.email);
      // Assuming teamMembers stores IDs, filter by ID if available, else by email as fallback
      const updatedMemberIds = team.teamMembers.filter(id => id !== memberToDelete.id);

      // Update teamMemberUids by removing the entry for this email
      const updatedMemberUids = { ...(team.teamMemberUids || {}) };
      if (memberToDelete.email && updatedMemberUids[memberToDelete.email]) {
        delete updatedMemberUids[memberToDelete.email];
      }

      const response = await fetch("/api/teams", {
        method: "PUT", headers: { "Content-Type": "application/json", "Authorization": `Bearer ${idToken}` },
        body: JSON.stringify({
          teamId: team.id,
          teamMemberEmails: updatedEmails,
          teamMembers: updatedMemberIds,
          teamMemberUids: updatedMemberUids
        }),
      });
      if (!response.ok) throw new Error("Failed to remove team member.");

      toast({ title: "Team member removed", description: `${memberToDelete.email} has been removed.` });
      setIsDeleteMemberModalOpen(false);
      setMemberToDelete(null);
      await fetchTeamAndMembers(true); // Force refresh members list
    } catch (err: any) {
      toast({ title: "Error", description: err.message, variant: "destructive" });
    } finally {
      setIsDeletingMember(false);
    }
  };

  const refreshAllData = useCallback(async () => {
    if (!user) return;
    setIsScansLoading(true);
    setIsTeamLoading(true); // Indicate overall refresh
    setIsMembersLoading(true);
    try {
      await fetchOrganizationScans('organization', true);
      await fetchTeamAndMembers(true);
      toast({ title: "Dashboard refreshed", description: "All team data has been updated." });
    } catch (err: any) {
      toast({ title: "Refresh failed", description: err.message || "Could not refresh data.", variant: "destructive" });
    } finally {
      setIsScansLoading(false);
      setIsTeamLoading(false);
      setIsMembersLoading(false);
    }
  }, [user, fetchOrganizationScans, fetchTeamAndMembers]);


  // Memoized statistics
  const teamStats = useMemo(() => {
    const s = scans || []; // Ensure scans is an array
    const totalScans = s.length;
    const completed = s.filter(scan => scan.status === "completed").length;
    return {
      totalScans,
      completedScans: completed,
      pendingScans: s.filter(scan => scan.status === "pending").length,
      inProgressScans: s.filter(scan => scan.status === "in-progress").length,
      retestScans: s.filter(scan => scan.status === "re-test").length,
      totalVulnerabilities: s.reduce((sum, scan) => sum + scan.criticalVulnerabilities + scan.highVulnerabilities + scan.mediumVulnerabilities + scan.lowVulnerabilities, 0),
      criticalVulnerabilities: s.reduce((sum, scan) => sum + scan.criticalVulnerabilities, 0),
      highVulnerabilities: s.reduce((sum, scan) => sum + scan.highVulnerabilities, 0),
      mediumVulnerabilities: s.reduce((sum, scan) => sum + scan.mediumVulnerabilities, 0),
      lowVulnerabilities: s.reduce((sum, scan) => sum + scan.lowVulnerabilities, 0),
      completionRate: totalScans > 0 ? Math.round((completed / totalScans) * 100) : 0,
    };
  }, [scans]);

  const sortedScansForDisplay = useMemo(() => {
    return [...(scans || [])] // Create a copy before sorting
      .sort((a, b) => new Date(b.requestedAt).getTime() - new Date(a.requestedAt).getTime())
      .slice(0, 10);
  }, [scans]);


  if (authLoading || (isInitialLoading && !error)) {
    return (
      <DashboardLayout>
        <div className="flex min-h-[calc(100vh-4rem)] items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-12 w-12 text-primary animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">
              {authLoading ? "Authenticating..." : "Loading team dashboard..."}
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && isInitialLoading) { // Only show full page error on initial load fail
    return (
      <DashboardLayout>
        <div className="container mx-auto py-10">
          <Card className="border-destructive bg-destructive/10">
            <CardHeader>
              <CardTitle className="text-destructive flex items-center gap-2">
                <AlertCircle /> Error Loading Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent><p>{error}</p></CardContent>
            <CardFooter>
              <Button variant="outline" onClick={() => window.location.reload()}>Try Again</Button>
            </CardFooter>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  const isLoadingOverall = isTeamLoading || isMembersLoading || isScansLoading;

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Users className="h-8 w-8 text-primary" /> Team Dashboard
            </h2>
            <p className="text-muted-foreground mt-1">Manage your team and view performance.</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={refreshAllData} disabled={isLoadingOverall}>
              {isLoadingOverall ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
              Refresh Dashboard
            </Button>
            <Button className="gap-2" onClick={() => setIsAddMemberModalOpen(true)}>
              <UserPlus className="h-4 w-4" /> Add Member
            </Button>
          </div>
        </div>

        {/* Add Member Modal */}
        {isAddMemberModalOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="fixed inset-0 bg-black/50" onClick={() => setIsAddMemberModalOpen(false)} />
            <Card className="relative z-50 w-full max-w-md">
              <CardHeader>
                <CardTitle>Add Team Member</CardTitle>
                <CardDescription>Enter email to add a new member. They must have an existing account.</CardDescription>
                <Button variant="ghost" size="icon" className="absolute right-4 top-4" onClick={() => setIsAddMemberModalOpen(false)}><X className="h-4 w-4" /></Button>
              </CardHeader>
              <CardContent className="space-y-4">
                <Label htmlFor="email-add">Email Address</Label>
                <Input id="email-add" type="email" placeholder="<EMAIL>" value={newMemberEmail} onChange={(e) => setNewMemberEmail(e.target.value)} />
              </CardContent>
              <CardFooter>
                <Button onClick={handleAddMember} disabled={!newMemberEmail.trim() || isAddingMember} className="w-full">
                  {isAddingMember ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null} Add Member
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}

        {/* Delete Member Modal */}
        {isDeleteMemberModalOpen && memberToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="fixed inset-0 bg-black/50" onClick={() => setIsDeleteMemberModalOpen(false)} />
            <Card className="relative z-50 w-full max-w-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2"><AlertCircle className="text-destructive" /> Confirm Removal</CardTitle>
                <CardDescription>Remove {memberToDelete.displayName || memberToDelete.email} from the team?</CardDescription>
                 <Button variant="ghost" size="icon" className="absolute right-4 top-4" onClick={() => setIsDeleteMemberModalOpen(false)}><X className="h-4 w-4" /></Button>
              </CardHeader>
              <CardContent>
                <p>This action cannot be undone. The member will lose access to team resources.</p>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsDeleteMemberModalOpen(false)}>Cancel</Button>
                <Button variant="destructive" onClick={handleDeleteMember} disabled={isDeletingMember}>
                  {isDeletingMember ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null} Remove Member
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}

        {/* Team Overview Stats */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2"><Users className="h-5 w-5 text-primary" /> Team Overview</CardTitle>
            <CardDescription>{team?.teamName || "Your Team"} • {isMembersLoading && !teamMembers.length ? <Loader2 className="inline h-4 w-4 animate-spin" /> : `${teamMembers.length} Members`}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { title: "Total Scans", value: teamStats.totalScans, details: `${teamStats.completedScans} completed • ${teamStats.pendingScans} pending` },
                { title: "Team Members", value: teamMembers.length, details: `${teamMembers.length} active members` },
                { title: "Total Vulnerabilities", value: teamStats.totalVulnerabilities, details: `${teamStats.criticalVulnerabilities} critical • ${teamStats.highVulnerabilities} high` },
                { title: "Completion Rate", value: `${teamStats.completionRate}%`, details: `${teamStats.completedScans} of ${teamStats.totalScans} completed` },
              ].map(stat => (
                <Card key={stat.title}>
                  <CardHeader className="pb-2">
                    <CardDescription>{stat.title}</CardDescription>
                    {(isScansLoading && (stat.title === "Total Scans" || stat.title === "Total Vulnerabilities" || stat.title === "Completion Rate")) || (isMembersLoading && stat.title === "Team Members") ?
                      <div className="h-8 w-16 bg-muted rounded animate-pulse mt-1"></div> :
                      <CardTitle className="text-2xl">{stat.value}</CardTitle>
                    }
                  </CardHeader>
                  <CardContent className="pt-0">
                    {(isScansLoading && (stat.title === "Total Scans" || stat.title === "Total Vulnerabilities" || stat.title === "Completion Rate")) || (isMembersLoading && stat.title === "Team Members") ?
                      <div className="h-4 w-full bg-muted rounded animate-pulse"></div> :
                      <div className="text-xs text-muted-foreground">{stat.details}</div>
                    }
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="members" className="mb-8">
          <TabsList className="mb-4">
            <TabsTrigger value="members" className="gap-2"><Users className="h-4 w-4" /> Team Members</TabsTrigger>
            <TabsTrigger value="performance" className="gap-2"><Activity className="h-4 w-4" /> Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="members">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div><CardTitle>Team Members List</CardTitle><CardDescription>Manage team members and their scan activity.</CardDescription></div>
                <Button variant="outline" size="sm" onClick={() => fetchTeamAndMembers(true)} disabled={isMembersLoading || isTeamLoading}>
                  {isMembersLoading || isTeamLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                  Refresh Members
                </Button>
              </CardHeader>
              <CardContent>
                {isMembersLoading && teamMembers.length === 0 ? ( // Show skeleton only if no members are yet displayed
                  Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                      <div className="col-span-5 flex items-center gap-3"><div className="h-8 w-8 rounded-full bg-muted animate-pulse"></div><div><div className="h-4 w-24 bg-muted animate-pulse mb-1"></div><div className="h-3 w-16 bg-muted animate-pulse"></div></div></div>
                      <div className="col-span-3"><div className="h-4 w-32 bg-muted animate-pulse"></div></div>
                      <div className="col-span-2 text-center"><div className="h-5 w-12 bg-muted animate-pulse mx-auto rounded-full"></div></div>
                      <div className="col-span-2 text-right"><div className="h-6 w-6 bg-muted animate-pulse ml-auto rounded"></div></div>
                    </div>
                  ))
                ) : teamMembers.length === 0 ? (
                  <div className="text-center py-8"><Users className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" /><h3 className="text-lg font-medium">No Team Members</h3><p className="text-muted-foreground">Add members to start collaborating.</p></div>
                ) : (
                  <div className="border rounded-md">
                    <div className="grid grid-cols-12 bg-muted/50 p-3 text-sm font-medium text-muted-foreground">
                      <div className="col-span-5">Member</div><div className="col-span-3">Email</div><div className="col-span-2 text-center">Scans</div><div className="col-span-2 text-right">Actions</div>
                    </div>
                    {teamMembers.map(member => (
                      <div key={member.id} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                        <div className="col-span-5 flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center"><User className="h-4 w-4 text-primary" /></div>
                          <div><div className="font-medium">{member.displayName || member.email.split('@')[0]}</div><div className="text-xs text-muted-foreground">Team Member</div></div>
                        </div>
                        <div className="col-span-3 truncate">{member.email}</div>
                        <div className="col-span-2 text-center">
                          <Badge variant={member.scanCount && member.scanCount > 0 ? "default" : "secondary"}>{member.scanCount || 0}</Badge>
                        </div>
                        <div className="col-span-2 flex justify-end">
                          <Button variant="ghost" size="icon" className="text-destructive hover:bg-destructive/10 h-8 w-8" onClick={() => { setMemberToDelete(member); setIsDeleteMemberModalOpen(true); }}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader><CardTitle>Team Performance Metrics</CardTitle><CardDescription>Overview of scan statuses and vulnerability distribution.</CardDescription></CardHeader>
              <CardContent className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium mb-4">Scan Status Distribution</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[
                      { label: "Pending", value: teamStats.pendingScans, color: "yellow" }, { label: "In Progress", value: teamStats.inProgressScans, color: "blue" },
                      { label: "Re-test", value: teamStats.retestScans, color: "purple" }, { label: "Completed", value: teamStats.completedScans, color: "green" },
                    ].map(s => (
                      <Card key={s.label} className={`bg-${s.color}-50 dark:bg-${s.color}-950/20 border-${s.color}-200 dark:border-${s.color}-900/50`}>
                        <CardHeader className="pb-2"><CardDescription>{s.label}</CardDescription>
                          {isScansLoading ? <div className="h-7 w-12 bg-muted rounded animate-pulse mt-1"></div> : <CardTitle>{s.value}</CardTitle>}
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Vulnerability Distribution</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                     {[
                      { label: "Critical", value: teamStats.criticalVulnerabilities, color: "red" }, { label: "High", value: teamStats.highVulnerabilities, color: "orange" },
                      { label: "Medium", value: teamStats.mediumVulnerabilities, color: "amber" }, { label: "Low", value: teamStats.lowVulnerabilities, color: "sky" /* using sky for low */ },
                    ].map(v => (
                      <Card key={v.label} className={`bg-${v.color}-50 dark:bg-${v.color}-950/20 border-${v.color}-200 dark:border-${v.color}-900/50`}>
                        <CardHeader className="pb-2"><CardDescription>{v.label}</CardDescription>
                          {isScansLoading ? <div className="h-7 w-12 bg-muted rounded animate-pulse mt-1"></div> : <CardTitle>{v.value}</CardTitle>}
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Member Performance</h3>
                  {isMembersLoading && teamMembers.length === 0 ? ( /* Skeleton for member performance */
                     Array.from({ length: 2 }).map((_, i) => (
                        <div key={i} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                          <div className="col-span-4 flex items-center gap-3"><div className="h-8 w-8 rounded-full bg-muted animate-pulse"></div><div><div className="h-4 w-24 bg-muted animate-pulse mb-1"></div><div className="h-3 w-32 bg-muted animate-pulse"></div></div></div>
                          {[1,2,3,4].map(j => <div key={j} className="col-span-2 text-center"><div className="h-4 w-8 bg-muted animate-pulse mx-auto"></div></div>)}
                        </div>
                     ))
                  ) : teamMembers.length === 0 ? (
                    <p className="text-muted-foreground text-center py-4">No team members to display performance for.</p>
                  ) : (
                    <div className="border rounded-md">
                      <div className="grid grid-cols-12 bg-muted/50 p-3 text-sm font-medium text-muted-foreground">
                        <div className="col-span-4">Member</div><div className="col-span-2 text-center">Scans</div><div className="col-span-2 text-center">Completed</div><div className="col-span-2 text-center">In Progress</div><div className="col-span-2 text-center">Vulnerabilities</div>
                      </div>
                      {teamMembers.map(member => {
                        const memberScans = (scans || []).filter(s => s.userId === member.id || (s.userEmail && s.userEmail === member.email));
                        const completed = memberScans.filter(s => s.status === 'completed').length;
                        const inProgress = memberScans.filter(s => s.status === 'in-progress').length;
                        const vulnerabilities = memberScans.reduce((sum, s) => sum + s.criticalVulnerabilities + s.highVulnerabilities + s.mediumVulnerabilities + s.lowVulnerabilities, 0);
                        return (
                          <div key={member.id} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                            <div className="col-span-4 flex items-center gap-3">
                              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center"><User className="h-4 w-4 text-primary" /></div>
                              <div><div className="font-medium">{member.displayName || member.email.split('@')[0]}</div><div className="text-xs text-muted-foreground truncate">{member.email}</div></div>
                            </div>
                            <div className="col-span-2 text-center">{isScansLoading ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : (member.scanCount || 0)}</div>
                            <div className="col-span-2 text-center">{isScansLoading ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : completed}</div>
                            <div className="col-span-2 text-center">{isScansLoading ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : inProgress}</div>
                            <div className="col-span-2 text-center">{isScansLoading ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : vulnerabilities}</div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div><CardTitle>Recent Team Scans</CardTitle><CardDescription>Latest 10 scans initiated by your team.</CardDescription></div>
             <Button variant="outline" size="sm" onClick={() => fetchOrganizationScans('organization', true)} disabled={isScansLoading}>
                {isScansLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                Refresh Scans
            </Button>
          </CardHeader>
          <CardContent>
            {isScansLoading && sortedScansForDisplay.length === 0 ? ( // Skeleton for recent scans
              Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                  <div className="col-span-3"><div className="h-4 w-32 bg-muted animate-pulse"></div></div>
                  <div className="col-span-2"><div className="h-5 w-20 bg-muted animate-pulse rounded-full"></div></div>
                  <div className="col-span-2"><div className="h-4 w-24 bg-muted animate-pulse"></div></div>
                  <div className="col-span-2"><div className="h-4 w-16 bg-muted animate-pulse"></div></div>
                  <div className="col-span-3"><div className="h-4 w-28 bg-muted animate-pulse"></div></div>
                </div>
              ))
            ) : sortedScansForDisplay.length === 0 ? (
              <div className="text-center py-8"><Shield className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" /><h3 className="text-lg font-medium">No Scans Found</h3><p className="text-muted-foreground">Your team has not performed any scans yet.</p></div>
            ) : (
              <div className="border rounded-md">
                <div className="grid grid-cols-12 bg-muted/50 p-3 text-sm font-medium text-muted-foreground">
                  <div className="col-span-3">Scan Target/Name</div><div className="col-span-2">Status</div><div className="col-span-2">Member</div><div className="col-span-2">Requested</div><div className="col-span-3">Vulnerabilities</div>
                </div>
                {sortedScansForDisplay.map(scan => {
                  const member = teamMembers.find(m => m.id === scan.userId || m.email === scan.userEmail);
                  const scanName = scan.name || scan.asset_type?.replace(/_/g, ' ') || "Untitled Scan";
                  return (
                    <div key={scan.id} className="grid grid-cols-12 p-3 text-sm border-t items-center">
                      <div className="col-span-3 font-medium truncate" title={scanName}>{scanName}</div>
                      <div className="col-span-2">
                        <Badge variant={
                          scan.status === 'completed' ? 'default' :
                          scan.status === 'pending' ? 'outline' :
                          scan.status === 'in-progress' ? 'secondary' :
                          scan.status === 're-test' ? 'secondary' : 'outline'
                        } className={
                          scan.status === 'completed' ? 'bg-green-500 hover:bg-green-600' :
                          scan.status === 'pending' ? 'bg-yellow-500 hover:bg-yellow-600 text-white' :
                          scan.status === 'in-progress' ? 'bg-blue-500 hover:bg-blue-600' :
                          scan.status === 're-test' ? 'bg-purple-500 hover:bg-purple-600' : ''
                        }>{scan.status.charAt(0).toUpperCase() + scan.status.slice(1)}</Badge>
                      </div>
                      <div className="col-span-2 truncate">{member?.displayName || member?.email?.split('@')[0] || scan.userEmail?.split('@')[0] || "Unknown"}</div>
                      <div className="col-span-2">{new Date(scan.requestedAt).toLocaleDateString()}</div>
                      <div className="col-span-3 flex flex-wrap gap-1">
                        {scan.criticalVulnerabilities > 0 && <Badge variant="destructive" className="text-xs">{scan.criticalVulnerabilities}C</Badge>}
                        {scan.highVulnerabilities > 0 && <Badge variant="default" className="text-xs bg-orange-500 hover:bg-orange-600">{scan.highVulnerabilities}H</Badge>}
                        {scan.mediumVulnerabilities > 0 && <Badge variant="default" className="text-xs bg-amber-500 hover:bg-amber-600">{scan.mediumVulnerabilities}M</Badge>}
                        {scan.lowVulnerabilities > 0 && <Badge variant="secondary" className="text-xs bg-sky-500 hover:bg-sky-600">{scan.lowVulnerabilities}L</Badge>}
                        {scan.criticalVulnerabilities + scan.highVulnerabilities + scan.mediumVulnerabilities + scan.lowVulnerabilities === 0 && <span className="text-xs text-muted-foreground">None</span>}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {scans.length > 10 ? `Showing 10 of ${scans.length} scans.` : `Showing all ${scans.length} scans.`}
            </p>
            <Button size="sm" onClick={() => router.push("/scans")}>View All Scans</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
}