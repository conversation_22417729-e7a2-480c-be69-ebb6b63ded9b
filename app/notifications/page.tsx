"use client"

import React from 'react';
import { DashboardLayout } from '@/components/dashboard-layout';
import { NotificationsPanel } from '@/components/notifications-panel';
import { Bell } from 'lucide-react';

export default function NotificationsPage() {
  return (
    <DashboardLayout>
      <div className="container mx-auto py-6 px-4 md:px-6 dark:bg-[#111827]">
        <div className="mb-8">
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Bell className="h-6 w-6 text-primary" />
            Notifications
          </h2>
          <p className="text-muted-foreground text-sm mt-1">
            View and manage your notifications
          </p>
        </div>
        <NotificationsPanel onClose={() => {}} />
      </div>
    </DashboardLayout>
  );
}
