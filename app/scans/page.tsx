import { DashboardLayout } from "@/components/dashboard-layout"
import { ScansList } from "@/components/scans-list"
import { PerformanceOptimizer } from "@/components/performance-optimizer"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

const ScansSkeleton = () => (
  <div className="px-6 py-6 space-y-6">
    <div className="flex justify-between items-center">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-10 w-32" />
    </div>
    <div className="flex gap-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <Skeleton key={i} className="h-10 w-24" />
      ))}
    </div>
    <div className="space-y-4">
      {Array.from({ length: 6 }).map((_, i) => (
        <Skeleton key={i} className="h-48 w-full" />
      ))}
    </div>
  </div>
);

export default function ScansPage() {
  return (
    <DashboardLayout>
      <PerformanceOptimizer />
      <Suspense fallback={<ScansSkeleton />}>
        <ScansList />
      </Suspense>
    </DashboardLayout>
  )
}
