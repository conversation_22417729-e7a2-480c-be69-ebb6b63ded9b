"use client";

import { useEffect } from 'react';

// Create a proper React component instead of a function
export function JsLoadedScript() {
  useEffect(() => {
    // Use requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        document.documentElement.classList.add('js-loaded');
      });
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      setTimeout(() => {
        document.documentElement.classList.add('js-loaded');
      }, 1000); // Delay animations by 1 second
    }
  }, []);

  // This component doesn't render anything
  return null;
}
