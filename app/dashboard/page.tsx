"use client";

import { useState, Suspense, useDeferredValue } from "react";
import { DashboardLayout } from "@/components/dashboard-layout"
import { AnalyticsDashboard } from "@/components/analytics-dashboard"
import { TeamDataPreloader } from "@/components/team-data-preloader"
import { useAuth } from "@/hooks/useAuth"
import { VulnerabilitiesProvider } from "@/context/VulnerabilitiesContext"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

const DashboardSkeleton = () => (
  <div className="space-y-6">
    <div className="flex justify-between items-center">
      <Skeleton className="h-8 w-64" />
      <div className="flex gap-4">
        <Skeleton className="h-9 w-32" />
        <Skeleton className="h-9 w-32" />
      </div>
    </div>
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Skeleton key={i} className="h-32" />
      ))}
    </div>
    <div className="grid gap-6 md:grid-cols-2">
      <Skeleton className="h-80" />
      <Skeleton className="h-80" />
    </div>
  </div>
);

export default function DashboardPage() {
  const { role } = useAuth();
  const [activeView, setActiveView] = useState<"personal" | "organization">("personal");
  const deferredActiveView = useDeferredValue(activeView);

  return (
    <DashboardLayout>
      {/* Preload team data in the background for managers */}
      {role === "manager" && <TeamDataPreloader />}
      
      <VulnerabilitiesProvider>
        <div className="mt-4">
          <Tabs value={activeView} onValueChange={(value) => setActiveView(value as "personal" | "organization")}>
            <TabsContent value="personal" className="space-y-6">
              <Suspense fallback={<DashboardSkeleton />}>
                <AnalyticsDashboard initialViewMode="personal" />
              </Suspense>
            </TabsContent>
            
            <TabsContent value="organization" className="space-y-6">
              <Suspense fallback={<DashboardSkeleton />}>
                <AnalyticsDashboard initialViewMode="organization" />
              </Suspense>
            </TabsContent>
          </Tabs>
        </div>
      </VulnerabilitiesProvider>
    </DashboardLayout>
  )
}
