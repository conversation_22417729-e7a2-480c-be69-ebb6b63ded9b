import type { NextApiRequest, NextApiResponse } from 'next';
import { db } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';

type RateLimitResponse = {
  success: boolean;
  message: string;
  remainingAttempts?: number;
  resetTime?: string;
};

// Rate limit configuration
const MAX_ATTEMPTS = 3; // Maximum number of attempts
const WINDOW_MINUTES = 15; // Time window in minutes
const COLLECTION_NAME = 'email-rate-limits';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<RateLimitResponse>
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: `Method ${req.method} Not Allowed`,
    });
  }

  // Get the email from the request body
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required',
    });
  }

  // Get client IP address
  const clientIp = 
    (req.headers['x-forwarded-for'] as string)?.split(',')[0].trim() || 
    req.socket.remoteAddress || 
    'unknown';

  try {
    // Create a composite key using email and IP
    const key = `${email.toLowerCase()}_${clientIp}`;
    
    // Get the current timestamp
    const now = admin.firestore.Timestamp.now();
    
    // Calculate the timestamp for the start of the window
    const windowStart = new admin.firestore.Timestamp(
      now.seconds - WINDOW_MINUTES * 60,
      now.nanoseconds
    );

    // Reference to the rate limit document
    const rateLimitRef = db.collection(COLLECTION_NAME).doc(key);
    
    // Get the current rate limit document
    const rateLimitDoc = await rateLimitRef.get();
    
    if (rateLimitDoc.exists) {
      const data = rateLimitDoc.data();
      
      if (!data) {
        // If data is undefined (shouldn't happen), create a new record
        await rateLimitRef.set({
          email,
          ip: clientIp,
          attempts: 1,
          firstAttempt: now,
          lastAttempt: now,
        });
        
        return res.status(200).json({
          success: true,
          message: 'Rate limit check passed',
          remainingAttempts: MAX_ATTEMPTS - 1,
        });
      }
      
      // Check if the first attempt is outside the window
      if (data.firstAttempt.seconds < windowStart.seconds) {
        // Reset the counter if outside the window
        await rateLimitRef.set({
          email,
          ip: clientIp,
          attempts: 1,
          firstAttempt: now,
          lastAttempt: now,
        });
        
        return res.status(200).json({
          success: true,
          message: 'Rate limit check passed',
          remainingAttempts: MAX_ATTEMPTS - 1,
        });
      }
      
      // Check if the user has exceeded the maximum attempts
      if (data.attempts >= MAX_ATTEMPTS) {
        // Calculate time until reset
        const resetTimeSeconds = data.firstAttempt.seconds + WINDOW_MINUTES * 60;
        const resetDate = new Date(resetTimeSeconds * 1000);
        
        return res.status(429).json({
          success: false,
          message: `Too many attempts. Please try again later.`,
          remainingAttempts: 0,
          resetTime: resetDate.toISOString(),
        });
      }
      
      // Increment the attempts counter
      await rateLimitRef.update({
        attempts: admin.firestore.FieldValue.increment(1),
        lastAttempt: now,
      });
      
      return res.status(200).json({
        success: true,
        message: 'Rate limit check passed',
        remainingAttempts: MAX_ATTEMPTS - (data.attempts + 1),
      });
    } else {
      // Create a new rate limit document
      await rateLimitRef.set({
        email,
        ip: clientIp,
        attempts: 1,
        firstAttempt: now,
        lastAttempt: now,
      });
      
      return res.status(200).json({
        success: true,
        message: 'Rate limit check passed',
        remainingAttempts: MAX_ATTEMPTS - 1,
      });
    }
  } catch (error) {
    console.error('Error checking rate limit:', error);
    
    // In case of error, allow the request to proceed
    return res.status(500).json({
      success: true, // Still return success to not block legitimate users due to server errors
      message: 'Error checking rate limit, proceeding anyway',
    });
  }
}
