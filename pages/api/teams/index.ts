import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";

interface TeamData {
  managerId: string;
  managerEmail: string;
  teamName: string;
  teamMembers: string[]; // Array of user IDs (for backward compatibility)
  teamMemberEmails: string[]; // Array of user emails (for backward compatibility)
  teamMemberUids?: Record<string, string>; // Map of email to UID (primary source of truth)
}

// Extended interface that includes the document ID
interface TeamDataWithId extends TeamData {
  id: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<
    | { team?: TeamDataWithId; teams?: TeamDataWithId[]; message?: string }
    | { error: string }
  >
) {
  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  const userId = decodedToken.uid;
  const userEmail = decodedToken.email || "";

  // Get user's custom claims to check role
  const userRecord = await auth.getUser(userId);
  const customClaims = userRecord.customClaims || {};

  // Check if user is a manager or admin based on custom claims
  const isManager = customClaims.role === 'manager';
  const isAdmin = customClaims.role === 'admin';

  if (!isManager && !isAdmin) {
    return res.status(403).json({ error: "Forbidden: Only managers and admins can access team data" });
  }

  // Handle GET request to fetch teams
  if (req.method === "GET") {
    try {
      // Use a more generic type that can handle both CollectionReference and Query
      let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData> = db.collection("teams");

      // If user is a manager, only show their team
      if (isManager) {
        query = query.where("managerId", "==", userId);
      }
      // If admin, show all teams (no filter)

      const snapshot = await query.get();
      const teams = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data() as TeamData
      })) as TeamDataWithId[];

      return res.status(200).json({ teams });
    } catch (error: any) {
      console.error("Error fetching teams:", error);
      return res.status(500).json({ error: "Failed to fetch teams" });
    }
  }

  // Handle POST request to create a new team
  if (req.method === "POST") {
    try {
      console.log("Creating new team for user:", userId, userEmail);
      const { teamName, teamMembers, teamMemberEmails } = req.body;

      if (!teamName) {
        console.log("Team name is missing in request");
        return res.status(400).json({ error: "Team name is required" });
      }

      console.log("Team creation request:", { teamName, teamMembers, teamMemberEmails });

      // Filter out emails that don't exist in Firebase Auth
      const validatedEmails: string[] = [];
      const validatedUids: string[] = [];
      const teamMemberUids: Record<string, string> = {};

      // Check each email to see if it exists in Firebase Auth
      if (teamMemberEmails && teamMemberEmails.length > 0) {
        for (const email of teamMemberEmails) {
          try {
            // Try to get the user by email from Firebase Auth
            const userRecord = await auth.getUserByEmail(email);

            // If we get here, the user exists, so add to validated emails and UIDs
            validatedEmails.push(email);
            validatedUids.push(userRecord.uid);
            teamMemberUids[email] = userRecord.uid;
            console.log(`Validated user with email ${email} exists in Firebase Auth, uid: ${userRecord.uid}`);
          } catch (error) {
            // If we get an error, the user doesn't exist in Firebase Auth
            console.log(`User with email ${email} not found in Firebase Auth, skipping`);
          }
        }

        console.log(`Filtered team member emails from ${teamMemberEmails.length} to ${validatedEmails.length} valid emails`);
      }

      // Create a new team document
      const teamData: TeamData = {
        managerId: userId,
        managerEmail: userEmail,
        teamName,
        teamMembers: validatedUids, // Use the validated UIDs
        teamMemberEmails: validatedEmails,
        teamMemberUids: teamMemberUids
      };

      const docRef = await db.collection("teams").add(teamData);
      console.log("Team created with ID:", docRef.id);

      const createdTeam: TeamDataWithId = {
        id: docRef.id,
        ...teamData
      };

      console.log("Returning created team:", createdTeam);

      return res.status(201).json({
        team: createdTeam,
        message: "Team created successfully"
      });
    } catch (error: any) {
      console.error("Error creating team:", error);
      return res.status(500).json({ error: "Failed to create team" });
    }
  }

  // Handle PUT request to update a team
  if (req.method === "PUT") {
    try {
      console.log("Updating team, request body:", req.body);
      const { teamId, teamName, teamMembers, teamMemberEmails } = req.body;

      if (!teamId) {
        console.log("Team ID is missing in request");
        return res.status(400).json({ error: "Team ID is required" });
      }

      // Get the team document
      const teamRef = db.collection("teams").doc(teamId);
      const teamDoc = await teamRef.get();

      if (!teamDoc.exists) {
        console.log("Team not found with ID:", teamId);

        // If the team doesn't exist, create it instead of returning an error
        if (isManager) {
          console.log("Creating a new team for manager");
          // Process emails to get UIDs
          const validatedEmails: string[] = [];
          const validatedUids: string[] = [];
          const teamMemberUids: Record<string, string> = {};

          if (teamMemberEmails && teamMemberEmails.length > 0) {
            for (const email of teamMemberEmails) {
              try {
                const userRecord = await auth.getUserByEmail(email);
                validatedEmails.push(email);
                validatedUids.push(userRecord.uid);
                teamMemberUids[email] = userRecord.uid;
              } catch (error) {
                console.log(`User with email ${email} not found in Firebase Auth, skipping`);
              }
            }
          }

          const newTeamData: TeamData = {
            managerId: userId,
            managerEmail: userEmail,
            teamName: teamName || "My Team",
            teamMembers: validatedUids,
            teamMemberEmails: validatedEmails,
            teamMemberUids: teamMemberUids
          };

          await teamRef.set(newTeamData);
          console.log("New team created with ID:", teamId);

          const createdTeam: TeamDataWithId = {
            id: teamId,
            ...newTeamData
          };

          return res.status(201).json({
            message: "Team created successfully",
            team: createdTeam
          });
        } else {
          return res.status(404).json({ error: "Team not found" });
        }
      }

      // Check if the user is the manager of this team or an admin
      const teamData = teamDoc.data() as TeamData;
      if (!isAdmin && teamData.managerId !== userId) {
        return res.status(403).json({ error: "Forbidden: You can only update your own team" });
      }

      // Update the team document
      const updates: Partial<TeamData> = {};
      if (teamName) updates.teamName = teamName;

      // Handle team members update with special care for removal operations
      if (teamMembers !== undefined) {
        // Ensure we're not accidentally setting an empty array when we meant to keep the existing data
        if (teamMembers.length === 0 && teamData.teamMembers.length > 0 && !teamMemberEmails) {
          console.log("Warning: Attempted to set teamMembers to empty array without updating emails. Keeping existing data.");
        } else {
          updates.teamMembers = teamMembers;
          console.log("Updating team members from", teamData.teamMembers, "to", teamMembers);
        }
      }

      // Handle team member emails update with special care for removal operations
      if (teamMemberEmails !== undefined) {
        // Ensure we're not accidentally setting an empty array when we meant to keep the existing data
        if (teamMemberEmails.length === 0 && teamData.teamMemberEmails.length > 0 && !teamMembers) {
          console.log("Warning: Attempted to set teamMemberEmails to empty array without updating members. Keeping existing data.");
        } else {
          // Filter out emails that don't exist in Firebase Auth
          const validatedEmails: string[] = [];
          const validatedUids: string[] = [];
          const teamMemberUids = teamData.teamMemberUids || {};

          // Check each email to see if it exists in Firebase Auth
          for (const email of teamMemberEmails) {
            try {
              // Try to get the user by email from Firebase Auth
              const userRecord = await auth.getUserByEmail(email);

              // If we get here, the user exists, so add to validated emails and UIDs
              validatedEmails.push(email);
              validatedUids.push(userRecord.uid);
              teamMemberUids[email] = userRecord.uid;
              console.log(`Validated user with email ${email} exists in Firebase Auth, uid: ${userRecord.uid}`);
            } catch (error) {
              // If we get an error, the user doesn't exist in Firebase Auth
              console.log(`User with email ${email} not found in Firebase Auth, skipping`);
            }
          }

          console.log(`Filtered team member emails from ${teamMemberEmails.length} to ${validatedEmails.length} valid emails`);

          // Only update with emails that exist in Firebase Auth
          updates.teamMemberEmails = validatedEmails;
          updates.teamMembers = validatedUids;
          updates.teamMemberUids = teamMemberUids;
          console.log("Updating team member emails from", teamData.teamMemberEmails, "to", validatedEmails);
          console.log("Updating team members from", teamData.teamMembers, "to", validatedUids);
          console.log("Updating teamMemberUids to", teamMemberUids);
        }
      }

      console.log("Updating team with data:", updates);

      await teamRef.update(updates);

      // Clear the team members cache when team members are updated
      if (teamMembers !== undefined || teamMemberEmails !== undefined) {
        try {
          // Import the memory cache
          const { memoryCache } = require("@/lib/cache");

          // Clear the team members cache for this team
          const teamCacheKey = `team_members:${teamId}`;
          console.log(`Clearing team members cache for key: ${teamCacheKey}`);
          memoryCache.delete(teamCacheKey);

          // Also clear any other team-related caches
          memoryCache.clearByPrefix(`team:${teamId}`);
          console.log("Team cache cleared successfully");
        } catch (cacheError) {
          console.error("Error clearing team cache:", cacheError);
          // Continue even if cache clearing fails
        }
      }

      // Fetch the updated team to confirm changes
      const updatedTeamDoc = await teamRef.get();
      const updatedTeamData = updatedTeamDoc.data() as TeamData;
      console.log("Team updated successfully. New team data:", updatedTeamData);

      const updatedTeam: TeamDataWithId = {
        id: teamId,
        ...updatedTeamData
      };

      return res.status(200).json({
        message: "Team updated successfully",
        team: updatedTeam
      });
    } catch (error: any) {
      console.error("Error updating team:", error);
      return res.status(500).json({ error: "Failed to update team" });
    }
  }

  // Handle DELETE request to delete a team
  if (req.method === "DELETE") {
    try {
      const { teamId } = req.query;

      if (!teamId || typeof teamId !== "string") {
        return res.status(400).json({ error: "Team ID is required" });
      }

      // Get the team document
      const teamRef = db.collection("teams").doc(teamId);
      const teamDoc = await teamRef.get();

      if (!teamDoc.exists) {
        return res.status(404).json({ error: "Team not found" });
      }

      // Check if the user is the manager of this team or an admin
      const teamData = teamDoc.data() as TeamData;
      if (!isAdmin && teamData.managerId !== userId) {
        return res.status(403).json({ error: "Forbidden: You can only delete your own team" });
      }

      // Delete the team document
      await teamRef.delete();

      return res.status(200).json({
        message: "Team deleted successfully"
      });
    } catch (error: any) {
      console.error("Error deleting team:", error);
      return res.status(500).json({ error: "Failed to delete team" });
    }
  }

  // If the request method is not supported
  res.setHeader("Allow", ["GET", "POST", "PUT", "DELETE"]);
  return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
}
