import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import { memoryCache } from "@/lib/cache";

interface TeamMember {
  id: string;
  email: string;
  displayName?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<
    | { teamMembers: string[]; teamMemberDetails?: TeamMember[] }
    | { error: string }
  >
) {
  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  const userId = decodedToken.uid;

  // Get user's custom claims to check role
  const userRecord = await auth.getUser(userId);
  const customClaims = userRecord.customClaims || {};

  // Check if user is a manager or admin based on custom claims
  const isManager = customClaims.role === 'manager';
  const isAdmin = customClaims.role === 'admin';

  if (!isManager && !isAdmin) {
    return res.status(403).json({ error: "Forbidden: Only managers and admins can access team member data" });
  }

  // Handle GET request to fetch team members
  if (req.method === "GET") {
    try {
      console.log("Fetching team members for manager:", userId);

      // Find the team where this user is the manager
      const teamRef = db.collection("teams").where("managerId", "==", userId);
      const teamSnapshot = await teamRef.get();

      if (teamSnapshot.empty) {
        // No team found for this manager
        console.log("No team found for manager:", userId);
        return res.status(200).json({
          teamMembers: [],
          teamMemberDetails: []
        });
      }

      // Get the first team document (assuming a manager has one team)
      const teamDoc = teamSnapshot.docs[0];
      const teamData = teamDoc.data();
      const teamMembers = teamData.teamMembers || [];
      const teamMemberEmails = teamData.teamMemberEmails || [];
      const teamMemberUids = teamData.teamMemberUids || {};

      console.log("Found team:", teamDoc.id);
      console.log("Team members IDs:", teamMembers);
      console.log("Team member emails:", teamMemberEmails);
      console.log("Team member UIDs mapping:", teamMemberUids);

      // Optionally fetch more details about each team member
      const teamMemberDetails: TeamMember[] = [];

      // Create a map of member IDs to emails for easier lookup
      const memberEmailMap = new Map<string, string>();

      // First, try to find the correct email for each member ID by looking up in Firebase Auth
      for (const memberId of teamMembers) {
        try {
          const userRecord = await auth.getUser(memberId);
          if (userRecord.email) {
            memberEmailMap.set(memberId, userRecord.email);
            console.log(`Mapped member ID ${memberId} to email ${userRecord.email} from Firebase Auth`);
          }
        } catch (error) {
          console.log(`Error looking up user for member ID ${memberId}:`, error);
        }
      }

      // For any member IDs that don't have emails yet, try to match with teamMemberEmails
      // This is a best-effort approach and may not be accurate if arrays aren't aligned
      let emailIndex = 0;
      for (const memberId of teamMembers) {
        if (!memberEmailMap.has(memberId) && emailIndex < teamMemberEmails.length) {
          memberEmailMap.set(memberId, teamMemberEmails[emailIndex]);
          console.log(`Mapped member ID ${memberId} to email ${teamMemberEmails[emailIndex]} from team data`);
          emailIndex++;
        }
      }

      // Process members with IDs using the email map
      for (const memberId of teamMembers) {
        const memberEmail = memberEmailMap.get(memberId) || "unknown";
        console.log(`Processing team member with ID: ${memberId}, email: ${memberEmail}`);

        try {
          // Try to get user details from Firebase Auth
          const userRecord = await auth.getUser(memberId);
          console.log(`Found user record for ${memberId}:`, {
            email: userRecord.email,
            displayName: userRecord.displayName
          });

          teamMemberDetails.push({
            id: memberId,
            email: userRecord.email || memberEmail,
            displayName: userRecord.displayName
          });
        } catch (error) {
          console.log(`Error fetching user record for ${memberId}:`, error);
          // If user not found in Auth, just use the basic info we have
          teamMemberDetails.push({
            id: memberId,
            email: memberEmail
          });
        }
      }

      // Process members from teamMemberUids mapping if available
      if (Object.keys(teamMemberUids).length > 0) {
        console.log("Processing members from teamMemberUids mapping");

        // Create a set of processed UIDs to avoid duplicates
        const processedUids = new Set(teamMemberDetails.map(member => member.id));

        for (const [email, uid] of Object.entries(teamMemberUids) as [string, string][]) {
          // Skip if we've already processed this user
          if (processedUids.has(uid)) {
            console.log(`User ${uid} (${email}) already in team members list, skipping`);
            continue;
          }

          console.log(`Processing team member from teamMemberUids: ${email} -> ${uid}`);

          try {
            // Try to get user details from Firebase Auth
            const userRecord = await auth.getUser(uid);
            console.log(`Found user record for ${uid} (${email}):`, {
              email: userRecord.email,
              displayName: userRecord.displayName
            });

            teamMemberDetails.push({
              id: uid,
              email: email,
              displayName: userRecord.displayName
            });
            processedUids.add(uid);
          } catch (error) {
            console.log(`Error fetching user record for ${uid}:`, error);
            // Skip users that don't exist in Firebase Auth anymore
            console.log(`User ${uid} (${email}) not found in Firebase Auth, skipping`);

            // Flag this team for cleanup
            try {
              // Add to a cleanup queue
              const cleanupRef = db.collection("team-cleanup-queue").doc();
              await cleanupRef.set({
                teamId: teamDoc.id,
                email: email,
                uid: uid,
                timestamp: new Date().toISOString(),
                processed: false
              });
              console.log(`Added team ${teamDoc.id} to cleanup queue for email ${email}`);
            } catch (cleanupError) {
              console.log(`Error adding to cleanup queue: ${cleanupError}`);
            }
          }
        }
      }

      // Now process all emails to make sure we don't miss any team members
      // Create a set of emails we've already processed
      const processedEmails = new Set(teamMemberDetails.map(member => member.email));

      // Create a cache key for this team's members to avoid repeated lookups
      const teamCacheKey = `team_members:${teamDoc.id}`;

      // Add cache control headers to improve caching behavior
      res.setHeader('Cache-Control', 'private, max-age=300');
      res.setHeader('ETag', `"${teamDoc.id}-members"`);

      // Check if the client sent an If-None-Match header
      const ifNoneMatch = req.headers['if-none-match'];

      // Check if we have cached team member details
      const cachedMembers = memoryCache.get<TeamMember[]>(teamCacheKey);
      if (cachedMembers && cachedMembers.length > 0) {
        // If the client has the same ETag, return 304 Not Modified
        if (ifNoneMatch && ifNoneMatch === `"${teamDoc.id}-members"`) {
          console.log(`Client has latest team member data for team ${teamDoc.id}, returning 304`);
          return res.status(304).end();
        }

        console.log(`Using cached team member details for team ${teamDoc.id}`);
        return res.status(200).json({
          teamMembers,
          teamMemberDetails: cachedMembers
        });
      }

      console.log("Processing all team member emails");
      console.log("Already processed emails:", Array.from(processedEmails));

      // Process emails in batches to avoid too many concurrent Firebase Auth calls
      const batchSize = 5;
      for (let i = 0; i < teamMemberEmails.length; i += batchSize) {
        const emailBatch = teamMemberEmails.slice(i, i + batchSize);

        // Process each email in the batch
        await Promise.all(emailBatch.map(async (memberEmail: string) => {
          // Skip emails we've already processed - but don't log this to reduce noise
          if (processedEmails.has(memberEmail)) {
            // Removed the console.log to reduce log noise on refreshes
            return;
          }

          console.log(`Processing team member with email: ${memberEmail}`);

          // Try to find a user with this email in Firebase Auth
          try {
            const userRecord = await auth.getUserByEmail(memberEmail);
            console.log(`Found user record for email ${memberEmail}:`, {
              uid: userRecord.uid,
              displayName: userRecord.displayName
            });

            // Check if this user is already in the list
            const alreadyAdded = teamMemberDetails.some(member => member.id === userRecord.uid);

            if (!alreadyAdded) {
              teamMemberDetails.push({
                id: userRecord.uid,
                email: memberEmail,
                displayName: userRecord.displayName
              });
              processedEmails.add(memberEmail);
              console.log(`Added team member with ID ${userRecord.uid} and email ${memberEmail}`);
            } else {
              console.log(`User ${userRecord.uid} already in team members list, skipping`);
            }
          } catch (error) {
            console.log(`Error finding user by email ${memberEmail}:`, error);
            console.log(`User with email ${memberEmail} not found in Firebase Auth, skipping`);

            // Skip users that don't exist in Firebase Auth
            // We no longer generate stable IDs for non-existent users
            console.log(`Skipping team member with email ${memberEmail} - user does not exist in Firebase Auth`);

            // Don't add this user to the team
            // Just add to processedEmails to avoid processing again
            processedEmails.add(memberEmail);
          }
        }));
      }

      // Cache the team member details for 5 minutes
      memoryCache.set(teamCacheKey, teamMemberDetails, 300000);

      console.log(`Final team member details count: ${teamMemberDetails.length}`);
      console.log("Team member details:", teamMemberDetails.map(m => ({ id: m.id, email: m.email })));

      return res.status(200).json({
        teamMembers,
        teamMemberDetails
      });
    } catch (error: any) {
      console.error("Error fetching team members:", error);
      return res.status(500).json({ error: "Failed to fetch team members" });
    }
  }

  // If the request method is not supported
  res.setHeader("Allow", ["GET"]);
  return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
}
