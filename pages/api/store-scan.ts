import type { NextApiRequest, NextApiResponse } from "next";
import { db } from "@/lib/firebase-admin";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  const { fields } = req.body;
  if (!fields) {
    return res.status(400).json({ error: "Missing fields" });
  }
  try {

    // Create a custom ID using the current date and time (ISO string, safe for Firestore)
    const now = new Date();
    const customId = now.toISOString().replace(/[:.]/g, "-"); // e.g., 2025-04-28T02-03-00-123Z
    await db.collection("scans").doc(customId).set(fields);
    return res.status(200).json({ success: true, id: customId });
  } catch (error: any) {
    return res.status(500).json({ error: error.message });
  }
}
