import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import { Vulnerability } from "@/types/vulnerability-types";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];

  try {
    const decodedToken = await auth.verifyIdToken(token);
    const { uid, name, role } = decodedToken; // Destructure role
    const { vulnerabilityIds, note, status } = req.body; // Accept status

    if (!vulnerabilityIds || !Array.isArray(vulnerabilityIds) || vulnerabilityIds.length === 0) {
      return res.status(400).json({ error: "Missing or invalid vulnerability IDs" });
    }

    if (!status) {
      return res.status(400).json({ error: "Missing status" });
    }

    // Validate status based on user role
    if (role !== "admin" && status !== "Pending Retest") {
      return res.status(403).json({ error: "Forbidden: Only admins can set status to Open or Closed" });
    }

    if (status === "Pending Retest" && !note) {
      return res.status(400).json({ error: "Note is required for Pending Retest status" });
    }

    const batch = db.batch();

    vulnerabilityIds.forEach((id: string) => {
      const vulnerabilityRef = db.collection("vulnerabilities").doc(id);
      const updateData: any = {
        status: status as Vulnerability['status'],
        updatedAt: new Date().toISOString(),
      };

      if (status === 'Closed') {
        updateData.closedAt = new Date().toISOString();
      } else if (status === 'Open') {
        // If status is set to Open, clear closedAt if it exists
        updateData.closedAt = null; 
      }

      batch.update(vulnerabilityRef, updateData);

      if (note) {
        const noteRef = vulnerabilityRef.collection("notes").doc();
        batch.set(noteRef, {
          text: note,
          author: name || "Anonymous",
          authorId: uid,
          createdAt: new Date().toISOString(),
        });
      }
    });

    await batch.commit();

    // Add notification for vulnerability status changes (for admin actions)
    if (role === "admin" && vulnerabilityIds.length > 0) {
      // Get the first vulnerability to use for notification details
      const firstVulnRef = db.collection("vulnerabilities").doc(vulnerabilityIds[0]);
      const firstVulnDoc = await firstVulnRef.get();
      
      if (firstVulnDoc.exists) {
        const vulnData = firstVulnDoc.data();
        const notificationData = {
          type: 'system',
          title: 'Vulnerabilities Updated',
          message: `${vulnerabilityIds.length} vulnerability${vulnerabilityIds.length > 1 ? 's' : ''} ${vulnerabilityIds.length > 1 ? 'have' : 'has'} been marked as ${status}`,
          timestamp: new Date().toISOString(),
          userId: uid,
          vulnerabilityCount: vulnerabilityIds.length,
          newStatus: status
        };

        // Store notification in Firestore for real-time updates
        await db.collection('notifications').add(notificationData);
      }
    }

    res.status(200).json({ message: `Vulnerabilities updated to ${status} successfully` });
  } catch (error: any) {
    console.error("Error updating vulnerabilities:", error);
    res.status(500).json({ error: error.message || "Internal server error" });
  }
}