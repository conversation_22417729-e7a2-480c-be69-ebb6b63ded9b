import type { NextApiRequest, NextApiResponse } from "next";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp, getApps, cert } from "firebase-admin/app";
import OpenA<PERSON> from "openai";
import { VulnerabilityDetailGenerationRequest, VulnerabilityDetailGenerationResponse } from "@/types/vulnerability-types";

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    // First try to use the structured environment variables
    if (process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY) {

      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
        }),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });

      console.log("Firebase Admin initialized with structured environment variables");
    }
    // Fallback to service account key JSON
    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(
        process.env.FIREBASE_SERVICE_ACCOUNT_KEY
      );

      // Validate the service account object
      if (!serviceAccount.project_id) {
        throw new Error("Service account object must contain a string 'project_id' property");
      }

      initializeApp({
        credential: cert(serviceAccount),
      });

      console.log("Firebase Admin initialized with service account JSON");
    }
    else {
      throw new Error("No Firebase credentials found in environment variables");
    }
  } catch (error) {
    console.error("Error initializing Firebase Admin:", error);
    // Continue execution - the error will be handled in the API handler
  }
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Check if the request method is POST
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get the authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Extract the token
  const token = authHeader.split("Bearer ")[1];

  try {
    // Verify the token
    await getAuth().verifyIdToken(token);

    // Get request body
    const {
      vulnerabilityId,
      name,
      severity,
      description,
      affectedComponent,
      target,
    } = req.body as VulnerabilityDetailGenerationRequest & { vulnerabilityId: string };

    // Validate required fields
    if (!vulnerabilityId || !name || !severity || !description || !affectedComponent) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Create a prompt for OpenAI
    const prompt = `
You are a cybersecurity expert specializing in vulnerability assessment and remediation.
Provide a detailed analysis of the following vulnerability:

Vulnerability Name: ${name}
Severity: ${severity}
Description: ${description}
Affected Component: ${affectedComponent}
${target ? `Target: ${target}` : ""}

Please provide the following information in a structured JSON format:
1. A detailed explanation of the vulnerability (detailedExplanation)
2. Step-by-step remediation instructions (remediationSteps as an array)
3. The potential impact of this vulnerability (impact)
4. If applicable, a potential CVE ID that might match this vulnerability (cveId)

Example format:
{
  "detailedExplanation": "This vulnerability occurs when...",
  "remediationSteps": ["Update the component to version X.Y.Z", "Implement input validation"],
  "impact": "This vulnerability could allow attackers to...",
  "cveId": "CVE-2023-12345"
}

If you cannot determine a specific CVE ID, omit the cveId field.
`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-4.1-nano",
      messages: [
        {
          role: "system",
          content: "You are a cybersecurity expert specializing in vulnerability assessment and remediation.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    // Get the response
    const rawResponse = completion.choices[0].message.content || "";

    // Parse the JSON response
    let detailsResponse: VulnerabilityDetailGenerationResponse;
    try {
      detailsResponse = JSON.parse(rawResponse);

      // Ensure the response has the expected structure
      if (!detailsResponse.detailedExplanation) {
        detailsResponse.detailedExplanation = "No detailed explanation available.";
      }
      if (!Array.isArray(detailsResponse.remediationSteps)) {
        detailsResponse.remediationSteps = ["No specific remediation steps available."];
      }
      if (!detailsResponse.impact) {
        detailsResponse.impact = "Impact assessment not available.";
      }
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      console.log("Raw response:", rawResponse);

      // Fallback to a basic response
      detailsResponse = {
        detailedExplanation: "Unable to generate a detailed explanation. Please try again later.",
        remediationSteps: ["No specific remediation steps available."],
        impact: "Impact assessment not available.",
      };
    }

    // Update the vulnerability in Firestore with the generated details
    const db = getFirestore();
    await db.collection("vulnerabilities").doc(vulnerabilityId).update({
      detailedExplanation: detailsResponse.detailedExplanation,
      remediationSteps: detailsResponse.remediationSteps,
      impact: detailsResponse.impact,
      cveId: detailsResponse.cveId || null,
      updatedAt: new Date().toISOString(),
    });

    // Return the generated details
    return res.status(200).json(detailsResponse);
  } catch (error: any) {
    console.error("Error generating vulnerability details:", error);
    return res.status(500).json({ error: error.message || "Internal server error" });
  }
}
