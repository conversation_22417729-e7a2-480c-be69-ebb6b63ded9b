import type { NextApiRequest, NextApiResponse } from 'next';
import { Resend } from 'resend';
import { auth, db } from '@/lib/firebase-admin';
import { getAdminEmails } from '@/lib/email-utils';

const resend = new Resend(process.env.RESEND_API_KEY);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    console.log('Server: Attempting to verify ID token...');
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    console.log('Server: Token verified. User ID:', userId);

    // Extract vulnerability ID from URL parameters
    const { id: vulnerabilityId } = req.query;
    // Extract admin's note from request body
    const { adminNote } = req.body;

    // Validate incoming data
    if (!vulnerabilityId || typeof vulnerabilityId !== 'string') {
      return res.status(400).json({ error: 'Vulnerability ID is required and must be a string.' });
    }
    if (!adminNote || typeof adminNote !== 'string' || adminNote.trim() === '') {
      return res.status(400).json({ error: 'Admin note is required and cannot be empty.' });
    }

    console.log('Server: Fetching admin emails...');
    // Fetch admin emails
    const adminEmails = await getAdminEmails();
    console.log('Server: Admin emails fetched:', adminEmails);
    if (adminEmails.length === 0) {
      console.warn('No admin emails found for retest request notification.');
      // Still return success as the request was processed, but no notification sent
      return res.status(200).json({ success: true, message: 'Retest request processed, but no admin emails found for notification.' });
    }

    console.log('Server: Fetching vulnerability details...');
    // Fetch vulnerability details from Firebase
    const vulnerabilityRef = db.collection('vulnerabilities').doc(vulnerabilityId);
    const vulnerabilityDoc = await vulnerabilityRef.get();
    console.log('Server: Vulnerability doc exists:', vulnerabilityDoc.exists);

    if (!vulnerabilityDoc.exists) {
      return res.status(404).json({ error: 'Vulnerability not found.' });
    }

    const vulnerabilityData = vulnerabilityDoc.data();
    const vulnerabilityTitle = vulnerabilityData?.title || 'Unknown Vulnerability';
    const scanId = vulnerabilityData?.scanId || 'N/A';

    // Update the vulnerability's status to 'retest'
    console.log(`Server: Updating vulnerability ${vulnerabilityId} status to 'retest'.`);
    await vulnerabilityRef.update({
      status: 'sent-for-retest',
      retestNote: adminNote, // Store the admin note with the vulnerability
      lastRetestRequestedAt: new Date().toISOString(), // Track last retest request time
      lastRetestRequestedBy: userId, // Track who requested the last retest
    });
    console.log(`Server: Vulnerability ${vulnerabilityId} updated to 'sent-for-retest' status.`);

    // Update the associated scan's status to 're-test' and aggregate notes
    if (scanId && scanId !== 'N/A') {
      const scanRef = db.collection('scans').doc(scanId);
      const scanDoc = await scanRef.get();
      if (scanDoc.exists) {
        const scanData = scanDoc.data();
        const existingNotes = scanData?.retestNotes || [];
        const newNote = {
          vulnerabilityId,
          vulnerabilityTitle,
          note: adminNote,
          requestedBy: userId,
          requestedAt: new Date().toISOString(),
        };
        const newNotes = [...existingNotes, newNote];

        await scanRef.update({
          status: 're-test',
          retestNotes: newNotes,
        });
        console.log(`Server: Scan ${scanId} updated to 're-test' status with aggregated notes.`);
      } else {
        console.warn(`Server: Scan ${scanId} not found for vulnerability ${vulnerabilityId}.`);
      }
    } else {
      console.warn('Server: Scan ID not found for vulnerability, skipping scan status update.');
    }

    // Construct email subject and body
    const subject = `Retest Request for Vulnerability: ${vulnerabilityTitle} (ID: ${vulnerabilityId})`;
    const emailBody = `
      <p>A retest has been requested for the following vulnerability:</p>
      <div class="details">
        <p><strong>Vulnerability ID:</strong> ${vulnerabilityId}</p>
        <p><strong>Title:</strong> ${vulnerabilityTitle}</p>
        <p><strong>Scan ID:</strong> ${scanId}</p>
        <p><strong>Requested By:</strong> ${userId}</p>
        <p><strong>Admin Note:</strong></p>
        <p style="white-space: pre-wrap; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${adminNote}</p>
        <p><strong>Request Time:</strong> ${new Date().toLocaleString()}</p>
      </div>
      <p>Please review the vulnerability and initiate the retest process.</p>
      <p>You can view the vulnerability details by clicking the button below:</p>
    `;

    // Create email HTML content (reusing structure from send-email.ts)
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #333;
              background-color: #f9f9f9;
              margin: 0;
              padding: 0;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #ffffff;
              border-radius: 8px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            .header {
              text-align: center;
              padding-bottom: 20px;
              border-bottom: 1px solid #eee;
            }
            h1 {
              color: #333;
              font-size: 24px;
              margin-bottom: 20px;
            }
            .content {
              padding: 20px 0;
            }
            .details {
              background-color: #f5f5f5;
              padding: 15px;
              border-radius: 4px;
              margin: 20px 0;
            }
            .details p {
              margin: 8px 0;
            }
            .button {
              display: inline-block;
              background-color: #1976d2;
              color: white;
              text-decoration: none;
              padding: 10px 20px;
              border-radius: 4px;
              margin-top: 20px;
              font-weight: 500;
            }
            .footer {
              text-align: center;
              padding-top: 20px;
              border-top: 1px solid #eee;
              font-size: 12px;
              color: #777;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>${subject}</h1>
            </div>
            <div class="content">
              ${emailBody}
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/vulnerabilities/${vulnerabilityId}" class="button">View Vulnerability Details</a>
            </div>
            <div class="footer">
              <p>This is an automated message from DeepScan. Please do not reply to this email.</p>
              <p>&copy; ${new Date().getFullYear()} DeepScan. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    // In testing <NAME_EMAIL>, we can only send to the account owner's email
    // So we'll <NAME_EMAIL> as the recipient
    let filteredRecipients: string[] = ['<EMAIL>'];

    console.log('Original admin recipients (would have been):', adminEmails);
    console.log('<NAME_EMAIL> as recipient due to Resend testing limitations');

    // Send email to filtered recipients
    const emailPromises = filteredRecipients.map(async (email: string) => {
      return resend.emails.send({
        from: 'DeepScan <<EMAIL>>',
        to: email,
        subject: subject,
        html: html,
        replyTo: '<EMAIL>'
      });
    });

    console.log('Server: Sending emails...');
    // Wait for all emails to be sent
    const results = await Promise.all(emailPromises);
    console.log('Server: Email sending results:', results);

    console.log('Server: Logging retest request to database...');
    // Log the retest request in the database
    await db.collection('retest-requests').add({
      vulnerabilityId,
      adminNote,
      requestedBy: userId,
      requestedAt: new Date().toISOString(),
      notificationSentTo: filteredRecipients,
      success: true,
      emailResults: results.map(r => ({ id: r.data?.id ?? null, error: r.error?.message })) // Ensure 'id' is not undefined
    });

    // Add notification for retest request
    const notificationData = {
      type: 'system',
      title: 'Retest Request Submitted',
      message: `Retest requested for vulnerability: ${vulnerabilityTitle}`,
      timestamp: new Date().toISOString(),
      userId: userId,
      vulnerabilityId: vulnerabilityId,
      vulnerabilityTitle: vulnerabilityTitle,
      scanId: scanId
    };

    // Store notification in Firestore for real-time updates
    await db.collection('notifications').add(notificationData);

    return res.status(200).json({
      success: true,
      message: `Retest request for vulnerability ${vulnerabilityId} submitted and notification sent to ${filteredRecipients.length} admin(s).`
    });
  } catch (error: any) {
    console.error('Server: Error processing retest request:', error);

    // Log failed retest request attempt
    try {
      await db.collection('retest-requests').add({
        vulnerabilityId: req.query.id || 'N/A',
        adminNote: req.body.adminNote || 'N/A',
        requestedBy: (await auth.verifyIdToken(token).catch(() => null))?.uid || 'N/A',
        requestedAt: new Date().toISOString(),
        error: error.message || 'Unknown error',
        success: false
      });
    } catch (logError) {
      console.error('Error logging retest request failure:', logError);
    }

    return res.status(500).json({
      error: `Failed to process retest request: ${error.message}`
    });
  }
}