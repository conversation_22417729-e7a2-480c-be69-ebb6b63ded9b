import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];

  try {
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    const { id } = req.query;
    if (!id || typeof id !== "string") {
      return res.status(400).json({ error: "Missing or invalid vulnerability ID" });
    }

    const { text } = req.body;
    if (!text || typeof text !== "string") {
      return res.status(400).json({ error: "Missing note text" });
    }

    const vulnRef = db.collection("vulnerabilities").doc(id);
    const vulnDoc = await vulnRef.get();

    if (!vulnDoc.exists) {
      return res.status(404).json({ error: "Vulnerability not found" });
    }

    // Authorization check: Ensure user has access to the vulnerability
    if (decodedToken.role !== "admin") {
      const scanDoc = await db.collection("scans").doc(vulnDoc.data()?.scanId).get();
      if (!scanDoc.exists || scanDoc.data()?.userId !== userId) {
        return res.status(403).json({ error: "Forbidden" });
      }
    }

    const noteRef = vulnRef.collection("notes").doc();
    await noteRef.set({
      text,
      author: decodedToken.name || "Anonymous",
      authorId: userId,
      createdAt: new Date().toISOString(),
    });

    return res.status(201).json({ message: "Note added successfully" });
  } catch (error: any) {
    console.error("Error adding note:", error);
    return res.status(500).json({ error: error.message || "Internal server error" });
  }
}