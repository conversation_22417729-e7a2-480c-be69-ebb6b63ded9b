import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];

  try {
    const decodedToken = await auth.verifyIdToken(token);
    const { uid, name } = decodedToken;
    const { vulnerabilityIds, note } = req.body;

    if (!vulnerabilityIds || !Array.isArray(vulnerabilityIds) || vulnerabilityIds.length === 0) {
      return res.status(400).json({ error: "Missing or invalid vulnerability IDs" });
    }

    if (!note) {
      return res.status(400).json({ error: "Missing note" });
    }

    const batch = db.batch();

    vulnerabilityIds.forEach((id: string) => {
      const vulnerabilityRef = db.collection("vulnerabilities").doc(id);
      batch.update(vulnerabilityRef, {
        status: "Pending Retest",
        updatedAt: new Date().toISOString(),
      });

      const noteRef = vulnerabilityRef.collection("notes").doc();
      batch.set(noteRef, {
        text: note,
        author: name || "Anonymous",
        authorId: uid,
        createdAt: new Date().toISOString(),
      });
    });

    await batch.commit();

    res.status(200).json({ message: "Vulnerabilities submitted for retest successfully" });
  } catch (error: any) {
    console.error("Error submitting vulnerabilities for retest:", error);
    res.status(500).json({ error: error.message || "Internal server error" });
  }
}