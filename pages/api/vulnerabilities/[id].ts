import type { NextApiRequest, NextApiResponse } from "next";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp, getApps, cert } from "firebase-admin/app";
import { Vulnerability } from "@/types/vulnerability-types";

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    // First try to use the structured environment variables
    if (process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY) {

      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
        }),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });

      console.log("Firebase Admin initialized with structured environment variables");
    }
    // Fallback to service account key JSON
    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(
        process.env.FIREBASE_SERVICE_ACCOUNT_KEY
      );

      // Validate the service account object
      if (!serviceAccount.project_id) {
        throw new Error("Service account object must contain a string 'project_id' property");
      }

      initializeApp({
        credential: cert(serviceAccount),
      });

      console.log("Firebase Admin initialized with service account JSON");
    }
    else {
      throw new Error("No Firebase credentials found in environment variables");
    }
  } catch (error) {
    console.error("Error initializing Firebase Admin:", error);
    // Continue execution - the error will be handled in the API handler
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Get the vulnerability ID from the request
  const { id } = req.query;

  if (!id || typeof id !== "string") {
    return res.status(400).json({ error: "Missing or invalid vulnerability ID" });
  }

  // Get the authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Extract the token
  const token = authHeader.split("Bearer ")[1];

  try {
    // Verify the token
    const decodedToken = await getAuth().verifyIdToken(token);
    const userId = decodedToken.uid;

    // Get the Firestore instance
    const db = getFirestore();

    // Get the vulnerability document
    const vulnDoc = await db.collection("vulnerabilities").doc(id).get();

    // Check if the vulnerability exists
    if (!vulnDoc.exists) {
      return res.status(404).json({ error: "Vulnerability not found" });
    }

    // Get the vulnerability data
    const vulnerability = {
      id: vulnDoc.id,
      ...vulnDoc.data(),
    } as Vulnerability;

    // If the user is not an admin, check if they have access to this vulnerability
    if (decodedToken.role !== "admin") {
      // Get the scan document to check if the user has access
      const scanDoc = await db.collection("scans").doc(vulnerability.scanId).get();

      if (!scanDoc.exists) {
        return res.status(404).json({ error: "Associated scan not found" });
      }

      const scanData = scanDoc.data();

      // Check if the user has access to this scan
      if (scanData?.userId !== userId) {
        return res.status(403).json({ error: "Forbidden" });
      }
    }
 
     if (req.method === "PUT") {
      const { status, note } = req.body;
 
       if (!status) {
        return res.status(400).json({ error: "Missing status" });
      }
 
      const updateData: any = {
        status,
        updatedAt: new Date().toISOString(),
      };
 
 

      const batch = db.batch();
      const vulnerabilityRef = db.collection("vulnerabilities").doc(id);

      batch.update(vulnerabilityRef, updateData);

      if (note) {
        const noteRef = vulnerabilityRef.collection("notes").doc();
        batch.set(noteRef, {
          text: note,
          author: decodedToken.name || "Anonymous",
          authorId: userId,
          createdAt: new Date().toISOString(),
        });
      }

      await batch.commit();
 
       return res.status(200).json({ message: "Vulnerability updated successfully" });
    }

     // Fetch notes for the vulnerability
     const notesSnapshot = await vulnDoc.ref.collection("notes").orderBy("createdAt", "asc").get();
     const notes = notesSnapshot.docs.map((doc: any) => ({ // Explicitly type doc as any for now, will refine with VulnerabilityNote type later
       id: doc.id,
       ...doc.data()
     }));

     // Add notes to the vulnerability object
     const vulnerabilityWithNotes = {
       ...vulnerability,
       notes: notes
     };

     // Return the vulnerability with notes
     return res.status(200).json({ vulnerability: vulnerabilityWithNotes });
   } catch (error: any) {
     console.error("Error fetching vulnerability:", error);
     return res.status(500).json({ error: error.message || "Internal server error" });
  }
}
