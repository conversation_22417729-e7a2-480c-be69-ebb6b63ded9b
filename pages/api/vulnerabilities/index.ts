import type { NextApiRequest, NextApiResponse } from "next";
import { Vulnerability, VulnerabilityNote } from "@/types/vulnerability-types";

// Import Firebase Admin from the centralized implementation
import { db, auth } from "@/lib/firebase-admin";
import { getUserOrganization, getOrganizationScanIds, getOrganizationMembers } from "@/lib/organization-utils";
import {
  logOrganizationAccess,
  validateOrganizationAccess,
  checkOrganizationRateLimit
} from "@/lib/organization-security";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Check if the request method is GET
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get the authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Extract the token
  const token = authHeader.split("Bearer ")[1];

  try {
    // Verify the token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    const userEmail = decodedToken.email || "";

    // Get query parameters for filtering
    const { severity, scanId, search, includeOrganization } = req.query;

    // Create a base query to get vulnerabilities
    let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData> = db.collection("vulnerabilities");

    // If the user is not an admin, filter by userId or organization
    if (decodedToken.role !== "admin") {
      let scanIds: string[] = [];
      
      // Check if organization-wide access is requested
      if (includeOrganization === "true") {
        try {
          console.log(`Fetching organization-wide vulnerabilities for user: ${userEmail}`);
          
          // Validate organization access
          const accessValidation = await validateOrganizationAccess(userId, userEmail);
          
          if (!accessValidation.isValid) {
            console.log(`Organization vulnerability access denied: ${accessValidation.errorMessage}`);
            await logOrganizationAccess(
              userId,
              userEmail,
              'VIEW_ORGANIZATION_VULNERABILITIES',
              'vulnerabilities',
              undefined,
              accessValidation.userOrganization || undefined,
              false,
              accessValidation.errorMessage
            );
            // Fall back to user's own scans
            const scansSnapshot = await db
              .collection("scans")
              .where("userId", "==", userId)
              .get();
            scanIds = scansSnapshot.docs.map((doc) => doc.id);
          } else {
            const userOrganization = accessValidation.userOrganization!;
            
            // Temporarily disable rate limiting for development
            // TODO: Re-enable with proper limits in production
            console.log(`Skipping rate limiting for development - user: ${userId}`);
            
            console.log(`User belongs to organization: ${userOrganization}`);
            
            // Get organization members first, then their scan IDs
            const orgMembers = await getOrganizationMembers(userOrganization);
            console.log(`Found ${orgMembers.length} organization members`);
            
            const memberUserIds = orgMembers.map(member => member.userId);
            console.log(`Member user IDs: ${memberUserIds.join(', ')}`);
            
            const orgScanIds = await getOrganizationScanIds(memberUserIds);
            scanIds = orgScanIds;
            
            console.log(`Found ${scanIds.length} scans for organization vulnerabilities`);
            
            // Log successful access
            await logOrganizationAccess(
              userId,
              userEmail,
              'VIEW_ORGANIZATION_VULNERABILITIES',
              'vulnerabilities',
              undefined,
              userOrganization,
              true
            );
          }
        } catch (orgError) {
          console.error("Error fetching organization vulnerability data:", orgError);
          await logOrganizationAccess(
            userId,
            userEmail,
            'VIEW_ORGANIZATION_VULNERABILITIES',
            'vulnerabilities',
            undefined,
            undefined,
            false,
            `Error: ${orgError}`
          );
          // Fall back to user's own scans
          const scansSnapshot = await db
            .collection("scans")
            .where("userId", "==", userId)
            .get();
          scanIds = scansSnapshot.docs.map((doc) => doc.id);
        }
      } else {
        // Default behavior: get user's own scans
        const scansSnapshot = await db
          .collection("scans")
          .where("userId", "==", userId)
          .get();
        scanIds = scansSnapshot.docs.map((doc) => doc.id);
      }

      // If no scans found, return empty array
      if (scanIds.length === 0) {
        return res.status(200).json({ vulnerabilities: [] });
      }

      // Filter vulnerabilities by scan IDs in batches (Firestore 'in' limit is 10)
      if (scanIds.length <= 10) {
        query = query.where("scanId", "in", scanIds);
      } else {
        // Handle large number of scan IDs by querying in batches
        const batchSize = 10;
        const allVulnerabilities: any[] = [];
        
        for (let i = 0; i < scanIds.length; i += batchSize) {
          const batch = scanIds.slice(i, i + batchSize);
          const batchQuery = db.collection("vulnerabilities").where("scanId", "in", batch);
          
          // Apply additional filters to batch query
          let filteredBatchQuery = batchQuery;
          if (severity) {
            filteredBatchQuery = filteredBatchQuery.where("severity", "==", severity);
          }
          if (scanId) {
            filteredBatchQuery = filteredBatchQuery.where("scanId", "==", scanId);
          }
          
          const batchSnapshot = await filteredBatchQuery.get();
          batchSnapshot.docs.forEach(doc => {
            allVulnerabilities.push({
              id: doc.id,
              ...doc.data()
            });
          });
        }
        
        // Apply search filter and fetch notes
        let vulnerabilities: Vulnerability[] = allVulnerabilities as Vulnerability[];
        
        // Fetch notes for each vulnerability
        const vulnerabilitiesWithNotes = await Promise.all(
          vulnerabilities.map(async (vuln) => {
            const notesSnapshot = await db
              .collection("vulnerabilities")
              .doc(vuln.id)
              .collection("notes")
              .orderBy("createdAt", "asc")
              .get();

            const notes = notesSnapshot.docs.map((noteDoc) => ({
              id: noteDoc.id,
              ...noteDoc.data(),
            })) as VulnerabilityNote[];

            return { ...vuln, notes };
          })
        );

        vulnerabilities = vulnerabilitiesWithNotes;

        // Apply search filter if provided
        if (search && typeof search === "string") {
          const searchLower = search.toLowerCase();
          vulnerabilities = vulnerabilities.filter(
            (vuln) =>
              vuln.name.toLowerCase().includes(searchLower) ||
              vuln.description.toLowerCase().includes(searchLower) ||
              vuln.affectedComponent.toLowerCase().includes(searchLower)
          );
        }

        // Return the vulnerabilities for batch processing
        return res.status(200).json({ vulnerabilities });
      }
    }

    // Apply additional filters if provided
    if (severity) {
      query = query.where("severity", "==", severity);
    }

    if (scanId) {
      query = query.where("scanId", "==", scanId);
    }

    // Execute the query
    const snapshot = await query.get();

    // Transform the data
    let vulnerabilities: Vulnerability[] = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Vulnerability[];

    // Fetch notes for each vulnerability
    const vulnerabilitiesWithNotes = await Promise.all(
      vulnerabilities.map(async (vuln) => {
        const notesSnapshot = await db
          .collection("vulnerabilities")
          .doc(vuln.id)
          .collection("notes")
          .orderBy("createdAt", "asc")
          .get();

        const notes = notesSnapshot.docs.map((noteDoc) => ({
          id: noteDoc.id,
          ...noteDoc.data(),
        })) as VulnerabilityNote[]; // Explicitly cast to VulnerabilityNote[]

        return { ...vuln, notes };
      })
    );

    vulnerabilities = vulnerabilitiesWithNotes;

    // Apply search filter if provided (client-side filtering since Firestore doesn't support text search)
    if (search && typeof search === "string") {
      const searchLower = search.toLowerCase();
      vulnerabilities = vulnerabilities.filter(
        (vuln) =>
          vuln.name.toLowerCase().includes(searchLower) ||
          vuln.description.toLowerCase().includes(searchLower) ||
          vuln.affectedComponent.toLowerCase().includes(searchLower)
      );
    }

    // Return the vulnerabilities
    return res.status(200).json({ vulnerabilities });
  } catch (error: any) {
    console.error("Error fetching vulnerabilities:", error);
    return res.status(500).json({ error: error.message || "Internal server error" });
  }
}
