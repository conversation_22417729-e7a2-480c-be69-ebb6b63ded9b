import type { NextApiRequest, NextApiResponse } from "next";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { initializeApp, getApps, cert } from "firebase-admin/app";
import <PERSON><PERSON><PERSON> from "openai";
import axios from "axios";
import pdfParse from "pdf-parse";

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    // First try to use the structured environment variables
    if (process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY) {

      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
        }),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });

      console.log("Firebase Admin initialized with structured environment variables");
    }
    // Fallback to service account key JSON
    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(
        process.env.FIREBASE_SERVICE_ACCOUNT_KEY
      );

      // Validate the service account object
      if (!serviceAccount.project_id) {
        throw new Error("Service account object must contain a string 'project_id' property");
      }

      initializeApp({
        credential: cert(serviceAccount),
      });

      console.log("Firebase Admin initialized with service account JSON");
    }
    else {
      throw new Error("No Firebase credentials found in environment variables");
    }
  } catch (error) {
    console.error("Error initializing Firebase Admin:", error);
    // Continue execution - the error will be handled in the API handler
  }
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Check if the request method is POST
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get the authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Extract the token
  const token = authHeader.split("Bearer ")[1];

  try {
    // Verify the token
    const decodedToken = await getAuth().verifyIdToken(token);

    // Only allow admins to manually extract vulnerabilities
    if (decodedToken.role !== "admin") {
      return res.status(403).json({ error: "Forbidden" });
    }

    // Get request body
    const { scanId, reportUrl } = req.body;

    // Validate required fields
    if (!scanId || !reportUrl) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Get the Firestore instance
    const db = getFirestore();

    // Get the scan document
    const scanDoc = await db.collection("scans").doc(scanId).get();
    if (!scanDoc.exists) {
      return res.status(404).json({ error: "Scan not found" });
    }

    const scanData = scanDoc.data();
    if (!scanData) {
      return res.status(404).json({ error: "Scan data not found" });
    }

    // Download the report
    const reportResponse = await axios.get(reportUrl, {
      responseType: "arraybuffer",
    });
    const reportBuffer = Buffer.from(reportResponse.data);

    // Parse the PDF
    const pdfData = await pdfParse(reportBuffer);
    const reportText = pdfData.text;

    // Look for sections that might contain tables with vulnerability information
    let keyFindingsSection = "";
    const keyFindingsMatch = reportText.match(
      /(?:Key Findings|Vulnerabilities|Security Issues|Findings)[\s\S]*?(?=\n\n\n|\n\n[A-Z]|$)/i
    );
    if (keyFindingsMatch) {
      keyFindingsSection = keyFindingsMatch[0];
      console.log(
        "Found potential Key Findings section:",
        keyFindingsSection.substring(0, 500) + "..."
      );
    }

    // Create a prompt for OpenAI to extract vulnerabilities
    const prompt = `
Extract all individual vulnerabilities from the following security report.
Focus on the "Key Findings" or "Vulnerabilities" section if available.
The vulnerabilities are often presented in a table. Pay close attention to column headers such as "ID", "Vulnerability", "Affected Endpoint", "Role", "Severity", and "CVSS" to correctly associate the data.

Report Text:
${keyFindingsSection || reportText.substring(0, 10000)}

For each vulnerability, extract the following information:
1. Name/title of the vulnerability
2. Severity level (critical, high, medium, low)
3. Brief description
4. Affected component or system
5. CVSS score (as a number, e.g., 9.8)
6. Role (e.g., Automation, Analyst, Anyone)

Return the results as a JSON array of vulnerability objects. Each object should have:
- name: The name or title of the vulnerability
- severity: The severity level (critical, high, medium, low)
- description: A brief description of the vulnerability
- affectedComponent: The affected component or system
- cvssScore: The CVSS score (e.g., 9.8). If not found, use null.
- role: The role associated with the vulnerability (e.g., "Automation", "Analyst", "Anyone"). If not found, use null.

Example format:
{
  "vulnerabilities": [
    {
      "name": "SQL Injection in Login Form",
      "severity": "critical",
      "description": "The login form is vulnerable to SQL injection attacks, allowing attackers to bypass authentication.",
      "affectedComponent": "Authentication System",
      "cvssScore": 9.8,
      "role": "Anyone"
    },
    {
      "name": "Outdated SSL Certificate",
      "severity": "medium",
      "description": "The SSL certificate is outdated and using deprecated encryption algorithms.",
      "affectedComponent": "Web Server",
      "cvssScore": 5.4,
      "role": "Automation"
    }
  ]
}

If no vulnerabilities can be extracted, return an empty array.
`;

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-4.1-nano",
      messages: [
        {
          role: "system",
          content: "You are a cybersecurity expert specializing in vulnerability assessment.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    // Get the response
    const rawResponse = completion.choices[0].message.content || "";

    // Parse the JSON response
    let extractedVulnerabilities: any[] = [];
    try {
      const parsedResponse = JSON.parse(rawResponse);
      extractedVulnerabilities = parsedResponse.vulnerabilities || [];
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      console.log("Raw response:", rawResponse);
      return res.status(500).json({ error: "Failed to parse." });
    }

    // If no vulnerabilities were extracted, return an empty array
    if (extractedVulnerabilities.length === 0) {
      return res.status(200).json({ vulnerabilities: [] });
    }

    // Save the vulnerabilities to Firestore
    const batch = db.batch();
    const savedVulnerabilities = [];

    for (const vuln of extractedVulnerabilities) {
      // Validate the vulnerability data
      if (!vuln.name || !vuln.severity || !vuln.description || !vuln.affectedComponent) {
        continue;
      }

      // Normalize severity
      let normalizedSeverity = vuln.severity.toLowerCase();
      if (!["critical", "high", "medium", "low"].includes(normalizedSeverity)) {
        // Map other severity terms to our standard levels
        if (normalizedSeverity.includes("crit")) normalizedSeverity = "critical";
        else if (normalizedSeverity.includes("high")) normalizedSeverity = "high";
        else if (normalizedSeverity.includes("med")) normalizedSeverity = "medium";
        else if (normalizedSeverity.includes("low")) normalizedSeverity = "low";
        else normalizedSeverity = "medium"; // Default
      }

      // Create a new vulnerability document
      const vulnRef = db.collection("vulnerabilities").doc();
      const vulnData = {
        name: vuln.name,
        severity: normalizedSeverity,
        description: vuln.description,
        affectedComponent: vuln.affectedComponent,
        scanId: scanId,
        scanName: scanData.conversationTitle || scanData.name || "Unknown Scan",
        target: scanData.target || "",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: "open",
        cvssScore: typeof vuln.cvssScore === 'number' ? vuln.cvssScore : null,
        role: typeof vuln.role === 'string' ? vuln.role : null,
      };

      batch.set(vulnRef, vulnData);
      savedVulnerabilities.push({
        id: vulnRef.id,
        ...vulnData,
      });
    }

    // Commit the batch
    await batch.commit();

    // Return the saved vulnerabilities
    return res.status(200).json({ vulnerabilities: savedVulnerabilities });
  } catch (error: any) {
    console.error("Error extracting vulnerabilities:", error);
    return res.status(500).json({ error: error.message || "Internal server error" });
  }
}
