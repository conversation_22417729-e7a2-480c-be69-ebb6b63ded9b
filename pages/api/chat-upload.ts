import type { NextApiRequest, NextApiResponse } from "next";
import formidable, { Fields, Files, File } from "formidable";
import fs from "fs";
import path from "path";
import { auth, getBucket } from "@/lib/firebase-admin";
import { getApps } from "firebase-admin/app";
import {
  MAX_FILE_SIZE,
  validateFileSize,
  validateFileExtension,
  getContentType,
  formatFileSize,
  getFileErrorMessage,
  generateUniqueFileName
} from "@/lib/file-utils";

// Disable Next.js body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<
    | { message: string; fileUrl?: string }
    | { error: string }
    | { bucketValue?: string; appsLength?: number }
  >
) {
  // Add a check for a query parameter to return environment info
  if (req.query.checkBucket === "true") {
    return res.status(200).json({
      bucketValue: process.env.FIREBASE_STORAGE_BUCKET,
      appsLength: getApps().length,
    });
  }

  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST", "GET"]); // Allow GET for checking
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Authentication failed. Please log in again." });
  }

  const userId = decodedToken.uid;

  // Configure formidable with file size limit
  const form = formidable({
    maxFileSize: MAX_FILE_SIZE, // 10MB limit
  });

  form.parse(req, async (err: Error | null, _fields: Fields, files: Files) => {
    // Handle formidable errors (including file size limit)
    if (err) {
      console.error("Error parsing form data:", err);
      // Check if it's a file size error
      if (err.message && err.message.includes("maxFileSize")) {
        return res.status(413).json({
          error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
        });
      }
      return res.status(500).json({ error: "Error parsing form data" });
    }

    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    // Validate file exists
    if (!file || !file.filepath || !file.originalFilename) {
      return res.status(400).json({ error: "Invalid file uploaded" });
    }

    // Validate file extension
    if (!validateFileExtension(file.originalFilename)) {
      return res.status(400).json({
        error: "Invalid file type. Only PDF and Word documents are allowed."
      });
    }

    // Double-check file size (in case formidable config is bypassed)
    if (!validateFileSize(file.size)) {
      return res.status(413).json({
        error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
      });
    }

    try {
      // Get bucket from centralized module
      const bucket = getBucket();

      // Generate a unique filename that preserves the original name
      const fileName = generateUniqueFileName(file.originalFilename);
      const filePath = `chat-uploads/${userId}/${fileName}`;

      // Get content type based on file extension
      const contentType = getContentType(file.originalFilename);

      // Create write stream with proper content type
      const fileUploadStream = bucket.file(filePath).createWriteStream({
        metadata: {
          contentType: contentType,
        },
        resumable: false, // For small files, non-resumable uploads are faster
      });

      // Set up error handling for the stream
      fileUploadStream.on('error', (error) => {
        console.error("Stream error during file upload:", error);
        throw error;
      });

      // Use streaming instead of reading entire file into memory
      const readStream = fs.createReadStream(file.filepath);

      // Handle read stream errors
      readStream.on('error', (error) => {
        console.error("Error reading file:", error);
        throw error;
      });

      // Pipe the file to Firebase Storage
      await new Promise<void>((resolve, reject) => {
        readStream.pipe(fileUploadStream)
          .on('finish', () => resolve())
          .on('error', reject);
      });

      // Get the download URL (use a more reasonable expiration)
      const [url] = await bucket.file(filePath).getSignedUrl({
        action: "read",
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
      });

      // Return success response with URL
      res.status(200).json({
        message: "File uploaded successfully",
        fileUrl: url,
      });
    } catch (error: any) {
      console.error("Error uploading file:", error);

      // Use our helper to get a user-friendly error message
      const errorMessage = getFileErrorMessage(error);
      res.status(500).json({ error: errorMessage });
    } finally {
      // Clean up the temporary file created by formidable
      fs.unlink(file.filepath, (unlinkErr) => {
        if (unlinkErr)
          console.error("Error deleting temporary file:", unlinkErr);
      });
    }
  });
}
