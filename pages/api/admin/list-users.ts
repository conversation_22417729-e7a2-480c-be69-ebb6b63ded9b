import type { NextApiRequest, NextApiResponse } from 'next';
import { auth, db } from '@/lib/firebase-admin';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const requestingUserId = decodedToken.uid;
    const requestingUserEmail = decodedToken.email || '';

    // Check if the requesting user is an admin
    const requestingUserRecord = await auth.getUser(requestingUserId);
    const customClaims = requestingUserRecord.customClaims || {};

    // Only allow admins to list users
    const isAdmin = customClaims.role === 'admin';

    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden: Only admins can list users' });
    }

    // Get pagination parameters
    const pageSize = parseInt(req.query.pageSize as string) || 100;
    const pageToken = req.query.pageToken as string | undefined;

    // List users from Firebase Auth
    const listUsersResult = await auth.listUsers(pageSize, pageToken);

    // Enhance user data with role information from custom claims
    const users = await Promise.all(
      listUsersResult.users.map(async (user) => {
        // Get role from custom claims
        const customClaims = user.customClaims || {};
        let role = customClaims.role || 'client';

        // Get additional user data from Firestore if available
        let userData = {};
        try {
          const userDoc = await db.collection('users').doc(user.uid).get();
          if (userDoc.exists) {
            userData = userDoc.data() || {};
          }
        } catch (error) {
          console.error(`Error fetching Firestore data for user ${user.uid}:`, error);
        }

        return {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          role,
          disabled: user.disabled,
          lastSignInTime: user.metadata.lastSignInTime,
          creationTime: user.metadata.creationTime,
          ...userData
        };
      })
    );

    return res.status(200).json({
      users,
      pageToken: listUsersResult.pageToken
    });
  } catch (error: any) {
    console.error('Error in list-users API:', error);
    return res.status(500).json({
      error: `Internal server error: ${error.message}`
    });
  }
}
