import type { NextApiRequest, NextApiResponse } from 'next';
import { auth, db } from '@/lib/firebase-admin';
import { extractOrganizationFromEmail } from '@/lib/user-utils';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    const userEmail = decodedToken.email || '';

    // For new users, we'll set a default role of 'client'
    // This endpoint is called right after user creation
    const role = 'client';

    // Set custom claims for the user
    await auth.setCustomUserClaims(userId, { role });

    // Extract organization from email
    const organization = extractOrganizationFromEmail(userEmail);

    // Also store the role and organization in Firestore for easier querying
    await db.collection('users').doc(userId).set({
      role,
      email: userEmail,
      organization,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }, { merge: true });

    return res.status(200).json({
      success: true,
      message: `Role for new user ${userEmail} set to ${role}`
    });
  } catch (error: any) {
    console.error('Error in set-initial-role API:', error);
    return res.status(500).json({
      error: `Internal server error: ${error.message}`
    });
  }
}
