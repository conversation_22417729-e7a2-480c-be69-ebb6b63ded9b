import type { NextApiRequest, NextApiResponse } from 'next';
import { auth, db } from '@/lib/firebase-admin';
import { extractOrganizationFromEmail } from '@/lib/user-utils';

type RoleData = {
  userId: string;
  role: 'admin' | 'manager' | 'client';
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const requestingUserId = decodedToken.uid;
    const requestingUserEmail = decodedToken.email || '';

    // Check if the requesting user is an admin
    const requestingUserRecord = await auth.getUser(requestingUserId);
    const customClaims = requestingUserRecord.customClaims || {};

    // Only allow admins to manage roles
    const isAdmin = customClaims.role === 'admin';

    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden: Only admins can manage roles' });
    }

    // Get request body
    const { userId, role } = req.body as RoleData;

    // Validate required fields
    if (!userId || !role) {
      return res.status(400).json({ error: 'Missing required fields: userId and role' });
    }

    // Validate role value
    if (!['admin', 'manager', 'client'].includes(role)) {
      return res.status(400).json({ error: 'Invalid role. Must be admin, manager, or client' });
    }

    // Get the target user
    const userRecord = await auth.getUser(userId);
    const userEmail = userRecord.email || '';

    // Set custom claims for the user
    await auth.setCustomUserClaims(userId, { role });

    // Extract organization from email if it exists
    const organization = extractOrganizationFromEmail(userEmail);

    // Also store the role and organization in Firestore for easier querying
    await db.collection('users').doc(userId).set({
      role,
      email: userEmail,
      organization,
      updatedAt: new Date().toISOString(),
      updatedBy: requestingUserId
    }, { merge: true });

    return res.status(200).json({
      success: true,
      message: `Role for user ${userRecord.email} updated to ${role}`
    });
  } catch (error: any) {
    console.error('Error in manage-roles API:', error);
    return res.status(500).json({
      error: `Internal server error: ${error.message}`
    });
  }
}
