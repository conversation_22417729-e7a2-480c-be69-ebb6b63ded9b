import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import admin from "firebase-admin";
import { FieldPath } from "firebase-admin/firestore";
import { ConversationType } from "@/lib/models/conversation";
import { getUserOrganization } from "@/lib/organization-utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    const idToken = authHeader.split(" ")[1];

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(idToken);
    } catch (error) {
      console.error("Error verifying Firebase ID token:", error);
      return res.status(401).json({ error: "Unauthorized" });
    }

    const uid = decodedToken.uid;
    const userRecord = await auth.getUser(uid);
    const userRole = userRecord.customClaims?.role;

    if (userRole !== "admin") {
      return res.status(403).json({ error: "Forbidden" });
    }

    const conversationsSnapshot = await db.collection("conversations").orderBy("updatedAt", "desc").get();

    if (conversationsSnapshot.empty) {
      return res.status(200).json({ conversations: {} });
    }

    const userIds = [...new Set(conversationsSnapshot.docs.map(doc => doc.data().userId).filter(Boolean))];
    
    const userRecords: { [key: string]: any } = {};
    if (userIds.length > 0) {
      const userResults = await auth.getUsers(userIds.map(uid => ({ uid })));
      for (const user of userResults.users) {
        userRecords[user.uid] = user;
      }
    }

    const conversations = await Promise.all(conversationsSnapshot.docs.map(async (doc) => {
      const conversationData = doc.data();
      const user = userRecords[conversationData.userId];

      if (!user || !user.email) {
        console.warn(`Skipping conversation ${doc.id}: user ${conversationData.userId} not found or has no email.`);
        return null;
      }

      try {
        const organization = await getUserOrganization(user.email);
        return {
          id: doc.id,
          ...conversationData,
          user: {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName,
          },
          organization,
        };
      } catch (error) {
        console.error(`Error fetching organization for user ${user.uid} in conversation ${doc.id}`, error);
        return null;
      }
    }));

    const validConversations = conversations.filter((conv): conv is ConversationType & { user: any, organization: string | null } => conv !== null);

    // Collect all unique associatedScanIds from all valid conversations
    const allAssociatedScanIds = [...new Set(validConversations.flatMap(conv => conv.associatedScanIds || []))];

    const associatedScans: { [key: string]: any } = {};
    if (allAssociatedScanIds.length > 0) {
      // Firestore 'in' query limit is 10, so batch the scan fetches
      const batchSize = 10;
      for (let i = 0; i < allAssociatedScanIds.length; i += batchSize) {
        const batch = allAssociatedScanIds.slice(i, i + batchSize);
        const scansSnapshot = await db.collection("scans").where(FieldPath.documentId(), 'in', batch).get();
        scansSnapshot.forEach(doc => {
          associatedScans[doc.id] = { id: doc.id, ...doc.data() };
        });
      }
    }

    // Enrich conversations with full scan objects
    const enrichedConversations = validConversations.map((conversation) => {
      const conv = conversation as ConversationType & { user: any, organization: string | null };
      const enrichedAssociatedScans = (conv.associatedScanIds || [])
        .map((scanId: string) => associatedScans[scanId])
        .filter(Boolean); // Filter out any scans that weren't found

      return {
        ...conv,
        associatedScans: enrichedAssociatedScans, // Add full scan objects
      };
    });

    const groupedConversations = enrichedConversations.reduce((acc, conversation) => {
      if (conversation) {
        const { organization } = conversation;
        if (organization && typeof organization === 'string') {
          if (!acc[organization]) {
            acc[organization] = [];
          }
          acc[organization].push(conversation);
        }
      }
      return acc;
    }, {} as Record<string, any[]>);

    return res.status(200).json({ conversations: groupedConversations });
  } catch (error: any) {
    console.error("Error fetching conversations:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
}