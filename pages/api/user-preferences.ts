import type { NextApiRequest, NextApiResponse } from 'next';
import { auth } from '@/lib/firebase-admin';
import { 
  saveUserNotificationPreferences, 
  getUserNotificationPreferences,
  NotificationPreferences 
} from '@/lib/user-preferences';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    // Handle GET request - retrieve user preferences
    if (req.method === 'GET') {
      const preferences = await getUserNotificationPreferences(userId);
      return res.status(200).json(preferences);
    }
    
    // Handle PUT request - update user preferences
    if (req.method === 'PUT') {
      const { 
        emailNotifications, 
        scanCompletedNotifications, 
        messageNotifications 
      } = req.body as NotificationPreferences;

      // Validate required fields
      if (
        typeof emailNotifications !== 'boolean' ||
        typeof scanCompletedNotifications !== 'boolean' ||
        typeof messageNotifications !== 'boolean'
      ) {
        return res.status(400).json({ 
          error: 'Invalid preferences format. All fields must be boolean values.' 
        });
      }

      // Save preferences
      const success = await saveUserNotificationPreferences(userId, {
        emailNotifications,
        scanCompletedNotifications,
        messageNotifications
      });

      if (success) {
        return res.status(200).json({ 
          message: 'Preferences saved successfully',
          preferences: {
            emailNotifications,
            scanCompletedNotifications,
            messageNotifications
          }
        });
      } else {
        return res.status(500).json({ error: 'Failed to save preferences' });
      }
    }

    // Handle unsupported methods
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error handling user preferences:', error);
    return res.status(500).json({ 
      error: `Internal server error: ${error.message}` 
    });
  }
}
