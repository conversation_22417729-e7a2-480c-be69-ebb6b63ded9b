import type { NextApiRequest, NextApiResponse } from "next";
import { auth, db } from "@/lib/firebase-admin";
const Pusher = require('pusher');

const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; message: string } | { error: string }>
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    const userEmail = decodedToken.email || "unknown";

    // Get the message from the request body
    const { message, originalMessageId } = req.body;
    if (!message || typeof message !== "string") {
      return res.status(400).json({ error: "Missing or invalid message" });
    }

    if (!originalMessageId || typeof originalMessageId !== "string") {
      return res.status(400).json({ error: "Missing or invalid original message ID" });
    }

    // Check if the original message exists and belongs to this user
    const originalMessageRef = db.collection("team-messages").doc(originalMessageId);
    const originalMessageDoc = await originalMessageRef.get();

    if (!originalMessageDoc.exists) {
      return res.status(404).json({ error: "Original message not found" });
    }

    const originalMessageData = originalMessageDoc.data();
    if (originalMessageData?.userId !== userId) {
      return res.status(403).json({ error: "You don't have permission to reply to this message" });
    }

    // Create a new document in the "team-messages" collection
    const timestamp = new Date().toISOString();
    const messageData = {
      userId,
      userEmail,
      message,
      timestamp,
      status: "unread",
      isReplyTo: originalMessageId, // Reference to the original message
    };

    const docRef = await db.collection("team-messages").add(messageData);

    // Create the complete message object for Pusher notification
    const newMessage = {
      id: docRef.id,
      ...messageData
    };

    // Emit the message using Pusher to admin channel
    try {
      await pusher.trigger('admin-messages', 'new-message', newMessage);
      console.log(`New reply ${docRef.id} created for user ${userId} in response to message ${originalMessageId}`);
    } catch (error) {
      console.error('Error emitting new reply event:', error);
    }

    // Return success
    return res.status(200).json({
      success: true,
      message: "Reply sent to team successfully",
    });
  } catch (error: any) {
    console.error("Error sending reply to team:", error);
    return res.status(500).json({
      error: `Internal server error: ${error.message}`,
    });
  }
}
