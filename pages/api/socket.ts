// This file is server-side only
import { NextApiRequest } from 'next';
import socket<PERSON>and<PERSON>, { NextApiResponseWithSocket } from '@/lib/socket-server';

// Export the socket handler
export default function handler(req: NextApiRequest, res: NextApiResponseWithSocket) {
  return socketHandler(req, res);
}

// Disable body parsing for WebSocket connections
export const config = {
  api: {
    bodyParser: false,
  },
};
