import type { NextApiRequest, NextApiResponse } from "next";
import { auth, db } from "@/lib/firebase-admin";
const Pusher = require('pusher');

const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true
});

interface TeamMessage {
  id: string;
  userId: string;
  userEmail: string;
  message: string;
  timestamp: string;
  status: "read" | "unread";
  response?: string;
  responseTimestamp?: string;
}

// Rate limiting map to track requests per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 50; // 50 requests per window

// Rate limiting function
const checkRateLimit = (userId: string): boolean => {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new rate limit entry
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false; // Rate limit exceeded
  }

  userLimit.count++;
  return true;
};

// Input validation and sanitization
const validateAndSanitizeInput = (input: any): { isValid: boolean; sanitized?: any; error?: string } => {
  if (typeof input !== 'object' || input === null) {
    return { isValid: false, error: 'Invalid input format' };
  }

  const sanitized: any = {};

  // Validate messageId if present
  if (input.messageId !== undefined) {
    if (typeof input.messageId !== 'string' || input.messageId.length === 0 || input.messageId.length > 100) {
      return { isValid: false, error: 'Invalid message ID' };
    }
    sanitized.messageId = input.messageId.trim();
  }

  // Validate status if present
  if (input.status !== undefined) {
    if (!['read', 'unread'].includes(input.status)) {
      return { isValid: false, error: 'Invalid status value' };
    }
    sanitized.status = input.status;
  }

  // Validate response if present
  if (input.response !== undefined) {
    if (typeof input.response !== 'string') {
      return { isValid: false, error: 'Response must be a string' };
    }
    // Sanitize response content - remove potentially dangerous content
    const cleanResponse = input.response
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
    
    if (cleanResponse.length === 0 || cleanResponse.length > 5000) {
      return { isValid: false, error: 'Response length must be between 1 and 5000 characters' };
    }
    sanitized.response = cleanResponse;
  }

  return { isValid: true, sanitized };
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<
    | { messages: TeamMessage[]; pagination?: { page: number; limit: number; total: number; hasMore: boolean } }
    | { message: string }
    | { error: string }
  >
) {
  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userEmail = decodedToken.email || "";
    const userId = decodedToken.uid;

    // Check rate limit
    if (!checkRateLimit(userId)) {
      return res.status(429).json({ 
        error: "Rate limit exceeded. Please wait before making more requests." 
      });
    }

    // Check if the user is an admin using custom claims
    const customClaims = decodedToken;
    if (customClaims.role !== 'admin') {
      return res.status(403).json({ error: "Forbidden: Admin access required" });
    }

    // Handle GET request to fetch messages with pagination
    if (req.method === "GET") {
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // Max 100 items per page
      const offset = (page - 1) * limit;

      // Get total count for pagination
      const countSnapshot = await db.collection("team-messages").count().get();
      const total = countSnapshot.data().count;

      // Fetch messages with pagination
      const snapshot = await db
        .collection("team-messages")
        .orderBy("timestamp", "desc")
        .limit(limit)
        .offset(offset)
        .get();

      const messages: TeamMessage[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...(doc.data() as Omit<TeamMessage, "id">),
      }));

      const hasMore = offset + messages.length < total;

      return res.status(200).json({ 
        messages,
        pagination: {
          page,
          limit,
          total,
          hasMore
        }
      });
    }

    // Handle PUT request to update message status or add response
    if (req.method === "PUT") {
      const validation = validateAndSanitizeInput(req.body);
      if (!validation.isValid) {
        return res.status(400).json({ error: validation.error || "Invalid input" });
      }

      const { messageId, status, response } = validation.sanitized!;

      if (!messageId) {
        return res.status(400).json({ error: "Missing message ID" });
      }

      const messageRef = db.collection("team-messages").doc(messageId);
      const messageDoc = await messageRef.get();

      if (!messageDoc.exists) {
        return res.status(404).json({ error: "Message not found" });
      }

      const updates: Record<string, any> = {};

      if (status) {
        updates.status = status;

        // Emit message read event if status is changed to read
        if (status === "read") {
          // Get the message data to access userId
          const messageData = messageDoc.data() as Omit<TeamMessage, "id">;

          // Emit the message read event using Pusher
          try {
            await pusher.trigger(`user-${messageData.userId}`, 'message-read', {
              messageId,
              userId: messageData.userId
            });
            console.log(`Message ${messageId} marked as read for user ${messageData.userId}`);
          } catch (error) {
            console.error('Error emitting message read event:', error);
          }
        }
      }

      if (response) {
        updates.response = response;
        updates.responseTimestamp = new Date().toISOString();

        // Get the message data to access userId
        const messageData = messageDoc.data() as Omit<TeamMessage, "id">;

        // Emit the message response event using Pusher
        try {
          await pusher.trigger(`user-${messageData.userId}`, 'message-response', {
            messageId,
            response,
            responseTimestamp: updates.responseTimestamp,
            userId: messageData.userId
          });
          console.log(`Response added to message ${messageId} for user ${messageData.userId}`);
        } catch (error) {
          console.error('Error emitting message response event:', error);
        }
      }

      await messageRef.update(updates);

      return res.status(200).json({ message: "Message updated successfully" });
    }

    // Handle DELETE request to delete a message
    if (req.method === "DELETE") {
      const { messageId } = req.query;

      if (!messageId || typeof messageId !== "string") {
        return res.status(400).json({ error: "Missing message ID" });
      }

      // Validate messageId format
      if (messageId.length === 0 || messageId.length > 100) {
        return res.status(400).json({ error: "Invalid message ID format" });
      }

      const messageRef = db.collection("team-messages").doc(messageId);
      const messageDoc = await messageRef.get();

      if (!messageDoc.exists) {
        return res.status(404).json({ error: "Message not found" });
      }

      // Delete the message
      await messageRef.delete();
      console.log(`Message ${messageId} deleted by admin ${userEmail}`);

      return res.status(200).json({ message: "Message deleted successfully" });
    }

    // Handle unsupported methods
    return res.status(405).json({ error: "Method not allowed" });
  } catch (error: any) {
    console.error("Error handling team messages:", error);
    
    // Don't expose internal error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    return res.status(500).json({
      error: isDevelopment 
        ? `Internal server error: ${error.message}`
        : "Internal server error"
    });
  }
}
