import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import { 
  getUserOrganization, 
  getOrganizationMembers, 
  getOrganizationInfo,
  updateUserOrganization,
  isUserInOrganization
} from "@/lib/organization-utils";
import {
  logOrganizationAccess,
  validateOrganizationAccess,
  checkOrganizationRateLimit,
  sanitizeOrganizationData
} from "@/lib/organization-security";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  
  const idToken = authHeader.split(" ")[1];
  
  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }
  
  const userId = decodedToken.uid;
  const userEmail = decodedToken.email || "";
  
  if (req.method === "GET") {
    try {
      const { action } = req.query;
      
      console.log(`[DEBUG] Organizations API: GET request from user ${userEmail}, action: ${action}`);
      
      if (action === "info") {
        console.log(`[DEBUG] Organizations API: Getting organization info for user ${userEmail}`);
        // Validate organization access
        const accessValidation = await validateOrganizationAccess(userId, userEmail);
        
        console.log(`[DEBUG] Organizations API: Access validation result:`, accessValidation);
        
        if (!accessValidation.isValid) {
          await logOrganizationAccess(
            userId,
            userEmail,
            'VIEW_ORGANIZATION_INFO',
            'organization',
            undefined,
            accessValidation.userOrganization || undefined,
            false,
            accessValidation.errorMessage
          );
          return res.status(403).json({ error: accessValidation.errorMessage });
        }
        
        // Check rate limiting
        const rateLimit = await checkOrganizationRateLimit(userId, 'VIEW_ORGANIZATION_INFO');
        
        if (!rateLimit.isAllowed) {
          await logOrganizationAccess(
            userId,
            userEmail,
            'VIEW_ORGANIZATION_INFO',
            'organization',
            undefined,
            accessValidation.userOrganization || undefined,
            false,
            'Rate limit exceeded'
          );
          return res.status(429).json({
            error: "Rate limit exceeded",
            resetTime: rateLimit.resetTime
          });
        }
        
        const userOrganization = accessValidation.userOrganization!;
        const organizationInfo = await getOrganizationInfo(userOrganization);
        
        if (!organizationInfo) {
          await logOrganizationAccess(
            userId,
            userEmail,
            'VIEW_ORGANIZATION_INFO',
            'organization',
            undefined,
            userOrganization,
            false,
            'Organization information not found'
          );
          return res.status(404).json({ error: "Organization information not found" });
        }
        
        // Sanitize organization data before sending
        const sanitizedInfo = sanitizeOrganizationData(organizationInfo);
        
        await logOrganizationAccess(
          userId,
          userEmail,
          'VIEW_ORGANIZATION_INFO',
          'organization',
          undefined,
          userOrganization,
          true
        );
        
        return res.status(200).json({ organization: sanitizedInfo });
        
      } else if (action === "members") {
        // Get organization members
        const userOrganization = await getUserOrganization(userEmail);
        
        if (!userOrganization) {
          return res.status(404).json({ error: "User organization not found" });
        }
        
        const members = await getOrganizationMembers(userOrganization);
        
        return res.status(200).json({ 
          organization: userOrganization,
          members 
        });
        
      } else {
        // Default: get user's organization
        const userOrganization = await getUserOrganization(userEmail);
        
        return res.status(200).json({ 
          organization: userOrganization,
          userEmail 
        });
      }
      
    } catch (error: any) {
      console.error("Error fetching organization data:", error);
      return res.status(500).json({ 
        error: "Failed to fetch organization data",
        details: error.message 
      });
    }
    
  } else if (req.method === "POST") {
    try {
      const { action, organizationName } = req.body;
      
      if (action === "update") {
        // Update user's organization
        if (!organizationName) {
          return res.status(400).json({ error: "Organization name is required" });
        }
        
        await updateUserOrganization(userId, organizationName);
        
        return res.status(200).json({ 
          message: "Organization updated successfully",
          organization: organizationName 
        });
        
      } else if (action === "verify") {
        // Verify if user belongs to organization
        if (!organizationName) {
          return res.status(400).json({ error: "Organization name is required" });
        }
        
        const isMember = await isUserInOrganization(userEmail, organizationName);
        
        return res.status(200).json({ 
          isMember,
          organization: organizationName,
          userEmail 
        });
        
      } else {
        return res.status(400).json({ error: "Invalid action" });
      }
      
    } catch (error: any) {
      console.error("Error processing organization request:", error);
      return res.status(500).json({ 
        error: "Failed to process organization request",
        details: error.message 
      });
    }
    
  } else if (req.method === "PUT") {
    try {
      // Auto-detect and set user's organization based on email
      const userOrganization = await getUserOrganization(userEmail);
      
      if (!userOrganization) {
        return res.status(400).json({ error: "Could not determine organization from email" });
      }
      
      // Update user's organization in Firestore
      await updateUserOrganization(userId, userOrganization);
      
      return res.status(200).json({ 
        message: "Organization auto-detected and updated",
        organization: userOrganization 
      });
      
    } catch (error: any) {
      console.error("Error auto-detecting organization:", error);
      return res.status(500).json({ 
        error: "Failed to auto-detect organization",
        details: error.message 
      });
    }
    
  } else {
    res.setHeader("Allow", ["GET", "POST", "PUT"]);
    res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}