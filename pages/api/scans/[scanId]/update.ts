import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import { memoryCache } from "@/lib/cache";
import { NextApiResponseWithSocket, getSocketIO, emitScanUpdated } from "@/lib/socket-server";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponseWithSocket
) {
  if (req.method !== "PUT") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Get scan ID from URL
  const { scanId } = req.query;
  if (!scanId || typeof scanId !== "string") {
    return res.status(400).json({ error: "Invalid scan ID" });
  }

  try {
    // Verify the token
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    const userRole = decodedToken.role || "client";

    // Get the scan from Firestore
    const scanRef = db.collection("scans").doc(scanId);
    const scanDoc = await scanRef.get();

    if (!scanDoc.exists) {
      return res.status(404).json({ error: "Scan not found" });
    }

    const scanData = scanDoc.data();

    // Check if the user has permission to update this scan
    if (userRole !== "admin" && scanData?.userId !== userId) {
      return res.status(403).json({ error: "Unauthorized to update this scan" });
    }

    // Get the update data from the request body
    const updateData = req.body;

    const oldStatus = scanData?.status; // Get the old status before update

    // Update the scan
    await scanRef.update(updateData);

    // Send email notification if status changed
    if (updateData.status && updateData.status !== oldStatus) {
      try {
        const emailResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/send-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            scanId,
            recipients: ['<EMAIL>'],
            oldStatus,
            newStatus: updateData.status,
            scanDetails: {
              target: scanData?.target || 'Unknown',
              asset_type: scanData?.asset_type || 'unknown'
            }
          })
        });
        
        if (emailResponse.ok) {
          console.log(`✅ Email notification sent for scan ${scanId} status change: ${oldStatus} → ${updateData.status}`);
        } else {
          console.error(`❌ Failed to send email notification:`, await emailResponse.text());
        }
      } catch (emailError) {
        console.error('Error sending email notification:', emailError);
      }
    }

    // If scan status changes to 'completed', update all 'sent-for-retest' vulnerabilities to 'closed'
    if (updateData.status === 'completed' && oldStatus !== 'completed') {
      console.log(`Server: Scan ${scanId} completed. Attempting to close retested vulnerabilities.`);
      const vulnerabilitiesRef = db.collection('vulnerabilities');
      const retestedVulnerabilitiesSnapshot = await vulnerabilitiesRef
        .where('scanId', '==', scanId)
        .where('status', '==', 'sent-for-retest')
        .get();

      if (!retestedVulnerabilitiesSnapshot.empty) {
        const batch = db.batch();
        retestedVulnerabilitiesSnapshot.docs.forEach(doc => {
          batch.update(doc.ref, { status: 'closed' });
        });
        await batch.commit();
        console.log(`Server: Updated ${retestedVulnerabilitiesSnapshot.size} retested vulnerabilities to 'closed'.`);
      } else {
        console.warn(`Server: No retested vulnerabilities found for scan ${scanId} to close.`);
      }
    }

    // Clear any cached data for this user
    memoryCache.clearByPrefix(`scans:${userId}`);

    // If this is a team scan, also clear team cache
    if (scanData?.userId && scanData.userId !== userId) {
      memoryCache.clearByPrefix(`scans:${scanData.userId}`);
    }

    // Clear cache for this specific scan
    memoryCache.delete(`scan:${scanId}:${userId}`);

    // Get the updated scan data
    const updatedScanDoc = await scanRef.get();
    const updatedScanData = {
      id: scanId,
      ...updatedScanDoc.data()
    };

    // Emit WebSocket event for scan update
    try {
      const io = getSocketIO(res);
      if (io) {
        emitScanUpdated(io, updatedScanData);
      }
    } catch (socketError) {
      // Continue without WebSocket functionality
    }

    return res.status(200).json({
      message: "Scan updated successfully",
      updatedFields: Object.keys(updateData),
      scan: updatedScanData
    });
  } catch (error: any) {
    return res.status(500).json({
      error: "Internal server error",
      details: error.message
    });
  }
}
