import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";
import { memoryCache } from "@/lib/cache";
import { NextApiResponseWithSocket, getSocketIO, emitScanUpdated, emitScanDeleted } from "@/lib/socket-server";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponseWithSocket
) {
  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  const userId = decodedToken.uid;
  const userRole = decodedToken.role || "client";
  const scanId = req.query.scanId as string;

  if (!scanId) {
    return res.status(400).json({ error: "Scan ID is required" });
  }

  if (req.method === "GET") {
    try {
      // Check cache first
      const cacheKey = `scan:${scanId}:${userId}`;
      const cachedScan = memoryCache.get(cacheKey);

      if (cachedScan) {
        console.log(`Using cached data for scan ${scanId}`);
        return res.status(200).json(cachedScan);
      }

      console.log(`Fetching fresh data for scan ${scanId}`);

      // Get the scan from Firestore
      const scanDoc = await db.collection("scans").doc(scanId).get();

      if (!scanDoc.exists) {
        return res.status(404).json({ error: "Scan not found" });
      }

      const scanData = scanDoc.data();

      // Check if the user has access to this scan
      // For client users, they should only see their own scans
      // For admin users, they can see all scans
      if (userRole !== "admin" && scanData?.userId !== userId) {
        return res.status(403).json({ error: "Unauthorized to access this scan" });
      }

      // Ensure the scan has a status field
      const scanWithStatus = {
        id: scanDoc.id,
        ...scanData,
        status: scanData?.status || "pending" // Default to pending if no status
      };

      // Cache the result for 5 minutes
      memoryCache.set(cacheKey, scanWithStatus, 300000);

      // Return the scan data
      return res.status(200).json(scanWithStatus);
    } catch (error) {
      console.error("Error fetching scan:", error);
      return res.status(500).json({ error: "Failed to fetch scan" });
    }
  } else if (req.method === "DELETE") {
    try {
      // Get the scan from Firestore
      const scanRef = db.collection("scans").doc(scanId);
      const scanDoc = await scanRef.get();

      if (!scanDoc.exists) {
        return res.status(404).json({ error: "Scan not found" });
      }

      const scanData = scanDoc.data();

      // Check if the user has permission to delete this scan
      if (userRole !== "admin" && scanData?.userId !== userId) {
        return res.status(403).json({ error: "Unauthorized to delete this scan" });
      }

      // Check if there are files associated with this scan
      if (scanData?.files && Array.isArray(scanData.files) && scanData.files.length > 0) {
        console.log(`Scan ${scanId} has ${scanData.files.length} associated files that will be deleted`);

        // Import the Firebase storage bucket
        const { getBucket } = require("@/lib/firebase-admin");
        const bucket = getBucket();

        // Delete each file from storage
        for (const file of scanData.files) {
          if (file.url && file.url.includes('scan-results')) {
            try {
              // Extract the path from the URL
              const urlPath = file.url.split('scan-results/')[1];
              if (urlPath) {
                const fullPath = `scan-results/${urlPath}`;
                await bucket.file(fullPath).delete();
                console.log(`Deleted file: ${fullPath}`);
              }
            } catch (deleteErr) {
              console.error(`Error deleting file ${file.url}:`, deleteErr);
              // Continue with other files even if one fails
            }
          }
        }
      } else {
        console.log(`Scan ${scanId} has no associated files to delete`);
      }

      // Delete associated vulnerabilities
      console.log(`Checking for vulnerabilities associated with scan ${scanId}`);
      let vulnerabilitiesDeleted = 0;

      try {
        // Query for all vulnerabilities with this scanId
        const vulnerabilitiesQuery = db.collection("vulnerabilities").where("scanId", "==", scanId);
        const vulnerabilitiesSnapshot = await vulnerabilitiesQuery.get();

        if (!vulnerabilitiesSnapshot.empty) {
          console.log(`Found ${vulnerabilitiesSnapshot.size} vulnerabilities to delete`);

          // Create a batch to delete all vulnerabilities
          const batch = db.batch();
          vulnerabilitiesSnapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
          });

          // Commit the batch
          await batch.commit();
          vulnerabilitiesDeleted = vulnerabilitiesSnapshot.size;
          console.log(`Successfully deleted ${vulnerabilitiesDeleted} vulnerabilities`);
        } else {
          console.log("No vulnerabilities found to delete");
        }
      } catch (vulnDeleteError) {
        console.error("Error deleting vulnerabilities:", vulnDeleteError);
        // Continue with scan deletion even if vulnerability deletion fails
      }

      // Delete the scan document
      await scanRef.delete();

      // Clear cache for this scan and user's scan list
      memoryCache.delete(`scan:${scanId}:${userId}`);
      memoryCache.clearByPrefix(`scans:${userId}`);

      // If this is a team scan, also clear team cache
      if (scanData && scanData.userId !== userId) {
        memoryCache.clearByPrefix(`scans:${scanData.userId}`);
      }

      // Prepare scan data for the WebSocket event
      const deletedScanData = {
        id: scanId,
        userId: scanData?.userId || userId, // Default to current user if scanData is undefined
        managerId: scanData?.managerId,
      };

      // Emit WebSocket event for scan deletion
      const io = getSocketIO(res);
      if (io) {
        emitScanDeleted(io, deletedScanData);
      }

      return res.status(200).json({
        message: "Scan deleted successfully",
        scanId,
        filesDeleted: scanData?.files?.length || 0,
        vulnerabilitiesDeleted
      });
    } catch (error) {
      console.error("Error deleting scan:", error);
      return res.status(500).json({ error: "Failed to delete scan" });
    }
  } else {
    res.setHeader("Allow", ["GET", "DELETE"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}
