import type { NextApiRequest, NextApiResponse } from "next";
import { db, getBucket, auth } from "@/lib/firebase-admin";
import firebase from "firebase-admin";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; vulnerabilitiesDeleted?: number; warning?: string } | { error: string }>
) {
  if (req.method !== "DELETE") {
    res.setHeader("Allow", ["DELETE"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Get the user's custom claims to check role
  const userRecord = await auth.getUser(decodedToken.uid);
  const customClaims = userRecord.customClaims || {};
  const userEmail = decodedToken.email || "";

  console.log("User email from token:", userEmail);
  console.log("User custom claims:", customClaims);

  // Check if user has admin role in custom claims
  const isAdmin = customClaims.role === 'admin';

  // Get the scan document to check ownership
  const { scanId } = req.query;

  if (!scanId || typeof scanId !== "string") {
    return res.status(400).json({ error: "Missing required parameter: scanId" });
  }

  // Get the scan document
  const scanRef = db.collection("scans").doc(scanId);
  const scanDoc = await scanRef.get();

  if (!scanDoc.exists) {
    return res.status(404).json({ error: "Scan not found" });
  }

  const scanData = scanDoc.data();

  // Check if the user is the owner of the scan
  const isOwner = scanData?.userId === decodedToken.uid;

  // Allow both admin users and the scan owner to delete reports
  if (!isAdmin && !isOwner) {
    console.log("Access denied: User is not an admin or the scan owner");
    return res.status(403).json({ error: "You don't have permission to delete this report" });
  }

  console.log(`Access granted: User is ${isAdmin ? 'an admin' : 'the scan owner'}`);

  try {
    // We already have the scanRef, scanDoc, and scanData from above

    // Check if there are files to delete
    if (!scanData?.files || !Array.isArray(scanData.files) || scanData.files.length === 0) {
      return res.status(404).json({ error: "No report files found for this scan" });
    }

    // Get the storage bucket
    const bucket = getBucket();

    // Delete each file from storage
    for (const file of scanData.files) {
      if (file.url) {
        try {
          console.log(`Attempting to delete file with URL: ${file.url}`);

          // Extract the file path from the URL
          let filePath;

          // Method 1: Try to extract from Firebase Storage URL format
          if (file.url.includes('scan-results')) {
            const urlPath = file.url.split('scan-results/')[1];
            if (urlPath) {
              filePath = `scan-results/${urlPath}`;
              console.log(`Extracted path using method 1: ${filePath}`);
            }
          }

          // Method 2: Try to extract from signed URL format
          if (!filePath && file.url.includes('o/scan-results')) {
            const match = file.url.match(/o\/(scan-results%2F[^?]+)/);
            if (match && match[1]) {
              filePath = decodeURIComponent(match[1]);
              console.log(`Extracted path using method 2: ${filePath}`);
            }
          }

          // Method 3: Construct path from scanId and filename
          if (!filePath && file.originalName) {
            filePath = `scan-results/${scanId}/${file.originalName}`;
            console.log(`Constructed path using method 3: ${filePath}`);
          }

          // If we have a file path, try to delete the file
          if (filePath) {
            console.log(`Attempting to delete file at path: ${filePath}`);
            try {
              await bucket.file(filePath).delete();
              console.log(`Successfully deleted file: ${filePath}`);
            } catch (specificDeleteErr) {
              console.error(`Failed to delete specific file path: ${filePath}`, specificDeleteErr);

              // If the specific file deletion failed, try listing files in the scan directory
              // and delete any that match the pattern
              console.log(`Trying alternative method: listing files in scan-results/${scanId}/`);
              try {
                const [files] = await bucket.getFiles({
                  prefix: `scan-results/${scanId}/`
                });

                console.log(`Found ${files.length} files in the directory:`, files.map(f => f.name));

                // Delete all files in the directory
                for (const storageFile of files) {
                  try {
                    console.log(`Deleting file: ${storageFile.name}`);
                    await storageFile.delete();
                    console.log(`Successfully deleted: ${storageFile.name}`);
                  } catch (fileDeleteErr) {
                    console.error(`Failed to delete file ${storageFile.name}:`, fileDeleteErr);
                  }
                }
              } catch (listErr) {
                console.error(`Failed to list files in directory:`, listErr);
              }
            }
          } else {
            // If we couldn't determine the file path, try listing all files in the scan directory
            console.log(`Could not determine file path for URL: ${file.url}`);
            console.log(`Trying alternative method: listing files in scan-results/${scanId}/`);

            try {
              const [files] = await bucket.getFiles({
                prefix: `scan-results/${scanId}/`
              });

              console.log(`Found ${files.length} files in the directory:`, files.map(f => f.name));

              // Delete all files in the directory
              for (const storageFile of files) {
                try {
                  console.log(`Deleting file: ${storageFile.name}`);
                  await storageFile.delete();
                  console.log(`Successfully deleted: ${storageFile.name}`);
                } catch (fileDeleteErr) {
                  console.error(`Failed to delete file ${storageFile.name}:`, fileDeleteErr);
                }
              }
            } catch (listErr) {
              console.error(`Failed to list files in directory:`, listErr);
            }
          }
        } catch (deleteErr) {
          console.error(`Error in main deletion process for ${file.url}:`, deleteErr);
          // Continue with other files even if one fails
        }
      }
    }

    // Update the scan document to remove file references, AI summary, and reset vulnerability counts
    await scanRef.update({
      files: [],
      resultFileUrl: firebase.firestore.FieldValue.delete(),
      aiSummary: firebase.firestore.FieldValue.delete(),
      // Reset all vulnerability counts to 0
      criticalVulnerabilities: 0,
      highVulnerabilities: 0,
      mediumVulnerabilities: 0,
      lowVulnerabilities: 0,
      // Add timestamp for the vulnerability update
      vulnerabilitiesUpdatedAt: new Date().toISOString()
    });

    // Delete all vulnerabilities associated with this scan
    console.log(`Deleting vulnerabilities associated with scan ${scanId}`);
    try {
      // Query for all vulnerabilities with this scanId
      const vulnerabilitiesQuery = db.collection("vulnerabilities").where("scanId", "==", scanId);
      const vulnerabilitiesSnapshot = await vulnerabilitiesQuery.get();

      if (!vulnerabilitiesSnapshot.empty) {
        console.log(`Found ${vulnerabilitiesSnapshot.size} vulnerabilities to delete`);

        // Create a batch to delete all vulnerabilities
        const batch = db.batch();
        vulnerabilitiesSnapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });

        // Commit the batch
        await batch.commit();
        console.log(`Successfully deleted ${vulnerabilitiesSnapshot.size} vulnerabilities`);

        return res.status(200).json({
          success: true,
          vulnerabilitiesDeleted: vulnerabilitiesSnapshot.size
        });
      } else {
        console.log("No vulnerabilities found to delete");
        return res.status(200).json({ success: true, vulnerabilitiesDeleted: 0 });
      }
    } catch (vulnDeleteError) {
      console.error("Error deleting vulnerabilities:", vulnDeleteError);
      // Still return success since the report was deleted successfully
      return res.status(200).json({
        success: true,
        warning: "Report deleted but failed to delete associated vulnerabilities"
      });
    }
  } catch (error: any) {
    console.error("Error deleting report:", error);
    return res.status(500).json({ error: error.message || "Failed to delete report" });
  }
}
