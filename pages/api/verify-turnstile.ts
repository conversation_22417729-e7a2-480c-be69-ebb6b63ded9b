import type { NextApiRequest, NextApiResponse } from "next";

type TurnstileVerifyResponse = {
  success: boolean;
  "error-codes"?: string[];
  challenge_ts?: string;
  hostname?: string;
  action?: string;
  cdata?: string;
};

type ApiResponse = {
  success: boolean;
  message?: string;
  errors?: string[];
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Only allow POST requests
  if (req.method !== "POST") {
    return res.status(405).json({
      success: false,
      message: `Method ${req.method} Not Allowed`
    });
  }

  // Get the token from the request body
  const { token, bypassDev } = req.body;

  // Check if we're in development mode and bypass was requested
  const isDevelopment = process.env.NODE_ENV === 'development';
  if (isDevelopment && bypassDev === true) {
    console.log("Development mode: Bypassing CAPTCHA verification");
    return res.status(200).json({
      success: true,
      message: "CAPTCHA verification bypassed in development mode",
    });
  }

  if (!token) {
    return res.status(400).json({
      success: false,
      message: "Missing token"
    });
  }

  try {
    // Verify the token with Cloudflare Turnstile
    const formData = new URLSearchParams();
    formData.append("secret", process.env.TURNSTILE_SECRET_KEY || ""); // Your Turnstile secret key
    formData.append("response", token);

    // Add the user's IP address if available
    if (req.headers["x-forwarded-for"]) {
      formData.append("remoteip", req.headers["x-forwarded-for"] as string);
    }

    // Make the verification request to Cloudflare
    const verifyResponse = await fetch(
      "https://challenges.cloudflare.com/turnstile/v0/siteverify",
      {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    // Parse the response
    const data: TurnstileVerifyResponse = await verifyResponse.json();

    // Return the verification result
    if (data.success) {
      return res.status(200).json({
        success: true,
        message: "CAPTCHA verification successful",
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "CAPTCHA verification failed",
        errors: data["error-codes"],
      });
    }
  } catch (error) {
    console.error("Error verifying CAPTCHA:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error during CAPTCHA verification",
    });
  }
}
