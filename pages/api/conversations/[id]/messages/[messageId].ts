import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow PUT method for updating message content
  if (req.method !== "PUT") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Validate the user's token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    const idToken = authHeader.split(" ")[1];

    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(idToken);
    } catch (error) {
      console.error("Error verifying Firebase ID token:", error);
      return res.status(401).json({ error: "Unauthorized" });
    }

    const uid = decodedToken.uid;

    // Get the conversation ID and message ID from the URL
    const conversationId = req.query.id as string;
    const messageId = req.query.messageId as string;

    // Get the new content from the request body
    const { content } = req.body;
    if (typeof content !== "string") {
      return res.status(400).json({ error: "Missing or invalid content" });
    }

    // Get the conversation document
    const conversationRef = db.collection("conversations").doc(conversationId);
    const conversationDoc = await conversationRef.get();

    // Check if the conversation exists
    if (!conversationDoc.exists) {
      return res.status(404).json({ error: "Conversation not found" });
    }

    // Check if the user owns the conversation
    const conversationData = conversationDoc.data();
    if (conversationData?.userId !== uid) {
      return res.status(403).json({ error: "Forbidden: You don't have access to this conversation" });
    }

    // Find the message in the conversation
    const messages = conversationData?.messages || [];
    const messageIndex = messages.findIndex((msg: any) => msg.id === messageId);

    if (messageIndex === -1) {
      return res.status(404).json({ error: "Message not found" });
    }

    // Update the message content
    messages[messageIndex].content = content;

    // Update the conversation document
    await conversationRef.update({
      messages,
      updatedAt: new Date().toISOString(),
    });

    return res.status(200).json({ success: true });
  } catch (error: any) {
    console.error("Error updating message:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
}
