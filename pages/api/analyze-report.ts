import { NextApiRequest, NextApiResponse } from 'next';
import { getAuth } from 'firebase-admin/auth';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import axios from 'axios';
import OpenAI from 'openai';
import pdfParse from 'pdf-parse';

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    // First try to use the structured environment variables
    if (process.env.FIREBASE_PROJECT_ID &&
        process.env.FIREBASE_CLIENT_EMAIL &&
        process.env.FIREBASE_PRIVATE_KEY) {

      initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
        }),
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
      });

      console.log("Firebase Admin initialized with structured environment variables");
    }
    // Fallback to service account key JSON
    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(
        process.env.FIREBASE_SERVICE_ACCOUNT_KEY
      );

      // Validate the service account object
      if (!serviceAccount.project_id) {
        throw new Error("Service account object must contain a string 'project_id' property");
      }

      initializeApp({
        credential: cert(serviceAccount),
      });

      console.log("Firebase Admin initialized with service account JSON");
    }
    else {
      throw new Error("No Firebase credentials found in environment variables");
    }
  } catch (error) {
    console.error("Error initializing Firebase Admin:", error);
    // Continue execution - the error will be handled in the API handler
  }
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.split('Bearer ')[1];
    try {
      await getAuth().verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying token:', error);
      return res.status(401).json({ error: 'Invalid authentication token' });
    }

    // Get request data
    const { reportUrl, scanId, target, vulnerabilities } = req.body;

    if (!reportUrl) {
      return res.status(400).json({ error: 'Report URL is required' });
    }

    // Download the report file
    let reportBuffer;
    try {
      const response = await axios.get(reportUrl, {
        responseType: 'arraybuffer',
      });
      reportBuffer = response.data;
    } catch (error) {
      console.error('Error downloading report:', error);
      return res.status(500).json({ error: 'Failed to download report file' });
    }

    // Parse the PDF to extract text
    let reportText;
    try {
      console.log("PDF Buffer received, size:", reportBuffer.length, "bytes");

      const pdfData = await pdfParse(reportBuffer);
      reportText = pdfData.text;

      console.log("PDF Info:", {
        pageCount: pdfData.numpages,
        info: pdfData.info,
        metadata: pdfData.metadata,
        textLength: reportText.length
      });

      // Look for sections that might contain tables with vulnerability information
      // These are often labeled as "Key Findings", "Vulnerabilities", etc.
      let keyFindingsSection = "";
      const keyFindingsMatch = reportText.match(/(?:Key Findings|Vulnerabilities|Security Issues|Findings)[\s\S]*?(?=\n\n\n|\n\n[A-Z]|$)/i);
      if (keyFindingsMatch) {
        keyFindingsSection = keyFindingsMatch[0];
        console.log("Found potential Key Findings section:", keyFindingsSection.substring(0, 500) + "...");
      }

      // If the text is too long, truncate it to fit within OpenAI's token limits
      // But make sure to preserve the Key Findings section if found
      const words = reportText.split(/\s+/);
      if (words.length > 6000) {
        // If we found a key findings section, make sure it's included in the truncated text
        if (keyFindingsSection) {
          // First, remove the key findings section from the original text to avoid duplication
          const textWithoutKeyFindings = reportText.replace(keyFindingsSection, "");
          const wordsWithoutKeyFindings = textWithoutKeyFindings.split(/\s+/);

          // Calculate how many words we can include (leaving room for the key findings)
          const keyFindingsWords = keyFindingsSection.split(/\s+/).length;
          const maxWords = Math.min(6000 - keyFindingsWords, wordsWithoutKeyFindings.length);

          // Create the truncated text with the key findings section at the beginning
          reportText = keyFindingsSection + "\n\n" +
                      wordsWithoutKeyFindings.slice(0, maxWords).join(' ') +
                      '... [content truncated due to length]';
        } else {
          // No key findings section found, just truncate normally
          reportText = words.slice(0, 6000).join(' ') + '... [content truncated due to length]';
        }
        console.log("Text truncated from", words.length, "words to approximately 6000 words");
      }

      console.log(`Extracted ${words.length} words from PDF`);
    } catch (error) {
      console.error('Error parsing PDF:', error);
      return res.status(500).json({ error: 'Failed to parse PDF content' });
    }

    // Create a prompt for the AI with the extracted text
    const prompt = `
      Please analyze this security scan report for ${target} and provide a concise summary.

      Here is the extracted text from the report:

      ${reportText}

      MOST IMPORTANT TASK: Look for a "Key Findings" table or section in the report. This table typically lists vulnerabilities with their severity levels (Critical, High, Medium, Low). Count the exact number of vulnerabilities for each severity level from this table.

      Structure your response in JSON format with the following sections:
      1. "summary": A brief overview (2-3 sentences)
      2. "vulnerabilityCounts": Count of vulnerabilities by severity level EXACTLY as they appear in the Key Findings table
      3. "keyFindings": List of the most important security findings (array of objects with "finding" and "severity" properties)
      4. "potentialImpacts": List of potential impacts of these vulnerabilities (array of strings)
      5. "recommendations": List of recommended actions (array of objects with "recommendation" and "priority" properties)

      Example format:
      {
        "summary": "This scan identified 5 critical vulnerabilities in the system...",
        "vulnerabilityCounts": {
          "critical": 2,
          "high": 3,
          "medium": 5,
          "low": 8
        },
        "keyFindings": [
          {"finding": "Insecure authentication mechanism", "severity": "critical"},
          {"finding": "Unpatched software components", "severity": "high"}
        ],
        "potentialImpacts": ["Unauthorized data access", "System compromise"],
        "recommendations": [
          {"recommendation": "Update all software components", "priority": "high"},
          {"recommendation": "Implement multi-factor authentication", "priority": "medium"}
        ]
      }

      For severity, use one of: "critical", "high", "medium", or "low".
      For priority, use one of: "immediate", "high", "medium", or "low".

      CRITICAL INSTRUCTION: For the vulnerabilityCounts, do NOT estimate or guess. Count the EXACT number of findings for each severity level from the Key Findings table in the report. If you can identify a table with columns for ID, Vulnerability, and Severity, use that table to count the vulnerabilities by severity level.

      Ensure your response is valid JSON. Do not include any HTML tags or markdown formatting.
    `;

    // Get AI analysis
    let aiResponse;
    try {
      // Log what we're sending to OpenAI (first 500 chars for brevity)
      console.log("Sending to OpenAI - Report URL:", reportUrl);
      console.log("Extracted text sample (first 500 chars):", reportText.substring(0, 500));
      console.log("Prompt sample (first 500 chars):", prompt.substring(0, 500));

      const completion = await openai.chat.completions.create({
        model: "gpt-4.1-nano",
        messages: [
          {
            role: "system",
            content: "You are a cybersecurity expert specializing in vulnerability assessment. Your primary task is to accurately extract vulnerability counts from security reports. Look for tables labeled 'Key Findings' or similar that list vulnerabilities with severity levels. Count the exact number of vulnerabilities for each severity level (Critical, High, Medium, Low) from these tables. Provide clear, concise summaries in structured JSON format."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more deterministic results
        response_format: { type: "json_object" } // Ensure we get valid JSON
      });

      // Get the raw response from OpenAI
      const rawResponse = completion.choices[0].message.content || '';

      // Helper functions to guess severity and priority
      const guessSeverity = (text: string): 'critical' | 'high' | 'medium' | 'low' => {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('critical') || lowerText.includes('severe') || lowerText.includes('urgent')) {
          return 'critical';
        } else if (lowerText.includes('high') || lowerText.includes('important')) {
          return 'high';
        } else if (lowerText.includes('medium') || lowerText.includes('moderate')) {
          return 'medium';
        } else {
          return 'low';
        }
      };

      const guessPriority = (text: string): 'immediate' | 'high' | 'medium' | 'low' => {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('immediate') || lowerText.includes('urgent') || lowerText.includes('critical')) {
          return 'immediate';
        } else if (lowerText.includes('high') || lowerText.includes('important')) {
          return 'high';
        } else if (lowerText.includes('medium') || lowerText.includes('moderate')) {
          return 'medium';
        } else {
          return 'low';
        }
      };

      try {
        // Parse the JSON response
        const parsedResponse = JSON.parse(rawResponse);

        // Validate that the response has the expected structure
        if (!parsedResponse.summary ||
            !parsedResponse.vulnerabilityCounts ||
            !Array.isArray(parsedResponse.keyFindings) ||
            !Array.isArray(parsedResponse.potentialImpacts) ||
            !Array.isArray(parsedResponse.recommendations)) {
          console.warn("OpenAI response missing expected fields:", parsedResponse);
          // If missing fields, create a fallback structure
          aiResponse = {
            summary: parsedResponse.summary || "Analysis complete. See details below.",
            vulnerabilityCounts: parsedResponse.vulnerabilityCounts || {
              critical: 0,
              high: 0,
              medium: 0,
              low: 0
            },
            keyFindings: [],
            potentialImpacts: Array.isArray(parsedResponse.potentialImpacts) ? parsedResponse.potentialImpacts : [],
            recommendations: []
          };

          // Convert simple string arrays to object arrays if needed
          if (Array.isArray(parsedResponse.keyFindings)) {
            if (parsedResponse.keyFindings.length > 0 && typeof parsedResponse.keyFindings[0] === 'string') {
              aiResponse.keyFindings = parsedResponse.keyFindings.map((finding: string) => ({
                finding,
                severity: guessSeverity(finding)
              }));
            } else {
              aiResponse.keyFindings = parsedResponse.keyFindings;
            }
          }

          if (Array.isArray(parsedResponse.recommendations)) {
            if (parsedResponse.recommendations.length > 0 && typeof parsedResponse.recommendations[0] === 'string') {
              aiResponse.recommendations = parsedResponse.recommendations.map((recommendation: string) => ({
                recommendation,
                priority: guessPriority(recommendation)
              }));
            } else {
              aiResponse.recommendations = parsedResponse.recommendations;
            }
          }
        } else {
          // Use the parsed response as is, but ensure proper structure
          aiResponse = {
            summary: parsedResponse.summary,
            vulnerabilityCounts: parsedResponse.vulnerabilityCounts || {
              critical: 0,
              high: 0,
              medium: 0,
              low: 0
            },
            keyFindings: parsedResponse.keyFindings.map((finding: any) => {
              if (typeof finding === 'string') {
                return { finding, severity: guessSeverity(finding) };
              }
              return finding;
            }),
            potentialImpacts: parsedResponse.potentialImpacts,
            recommendations: parsedResponse.recommendations.map((rec: any) => {
              if (typeof rec === 'string') {
                return { recommendation: rec, priority: guessPriority(rec) };
              }
              return rec;
            })
          };
        }
      } catch (parseError) {
        console.error("Error parsing JSON response:", parseError);
        console.log("Raw response:", rawResponse);

        // Fallback to using the raw response as the summary
        aiResponse = {
          summary: "Unable to parse structured response. See raw analysis below.",
          rawAnalysis: rawResponse,
          vulnerabilityCounts: {
            critical: 0,
            high: 0,
            medium: 0,
            low: 0
          },
          keyFindings: [],
          potentialImpacts: [],
          recommendations: []
        };
      }
    } catch (error) {
      console.error('Error getting analysis:', error);
      return res.status(500).json({ error: 'Failed to analyze report' });
    }

    // If scanId is provided, save the AI summary to the scan document
    if (scanId) {
      try {
        const db = getFirestore();
        const scanRef = db.collection('scans').doc(scanId);

        // Check if the scan exists
        const scanDoc = await scanRef.get();
        if (scanDoc.exists) {
          console.log(`Saving AI summary to scan document ${scanId}`);

          // Extract vulnerability counts from AI response
          const vulnerabilityCounts = aiResponse.vulnerabilityCounts || {
            critical: 0,
            high: 0,
            medium: 0,
            low: 0
          };

          // Log the extracted vulnerability counts for debugging
          console.log(`Extracted vulnerability counts for scan ${scanId}:`, {
            critical: vulnerabilityCounts.critical || 0,
            high: vulnerabilityCounts.high || 0,
            medium: vulnerabilityCounts.medium || 0,
            low: vulnerabilityCounts.low || 0
          });

          // Update the scan document with the AI summary and vulnerability counts
          await scanRef.update({
            aiSummary: aiResponse,
            criticalVulnerabilities: vulnerabilityCounts.critical || 0,
            highVulnerabilities: vulnerabilityCounts.high || 0,
            mediumVulnerabilities: vulnerabilityCounts.medium || 0,
            lowVulnerabilities: vulnerabilityCounts.low || 0,
            vulnerabilitiesUpdatedAt: new Date().toISOString()
          });

          console.log(`Successfully saved AI summary to scan document ${scanId}`);
        } else {
          console.warn(`Scan document ${scanId} not found, cannot save AI summary`);
        }
      } catch (saveError) {
        // Log the error but don't fail the request
        console.error(`Error saving summary to scan document ${scanId}:`, saveError);
      }
    }

    // Return the AI-generated summary
    return res.status(200).json({ summary: aiResponse });
  } catch (error) {
    console.error('Unexpected error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
