import type { NextApiRequest, NextApiResponse } from "next";
import { auth } from "@/lib/firebase-admin";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ exists: boolean; message?: string; userId?: string }>
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ exists: false, message: `Method ${req.method} Not Allowed` });
  }

  const { email, create } = req.body;

  if (!email) {
    return res.status(400).json({ exists: false, message: "Email is required" });
  }

  try {
    const userRecord = await auth.getUserByEmail(email).catch(() => null);

    if (userRecord) {
      return res.status(200).json({ exists: true, userId: userRecord.uid });
    }

    if (create) {
      const newUserRecord = await auth.createUser({
        email,
        emailVerified: false, // Will be verified by the passwordless link
      });
      await auth.setCustomUserClaims(newUserRecord.uid, { role: 'client' });
      return res.status(201).json({
        exists: true,
        userId: newUserRecord.uid,
        message: "User created successfully",
      });
    }

    return res.status(200).json({ exists: false });
  } catch (error: any) {
    console.error("Error in user check/creation:", error);
    return res.status(500).json({
      exists: false,
      message: `An error occurred: ${error.message}`,
    });
  }
}
