import type { NextApiRequest, NextApiResponse } from "next";
import { auth, db } from "@/lib/firebase-admin";

interface TeamMessage {
  id: string;
  userId: string;
  userEmail: string;
  message: string;
  timestamp: string;
  status: "read" | "unread";
  response?: string;
  responseTimestamp?: string;
}

// Rate limiting map to track requests per user
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100; // 100 requests per window for client messages

// Rate limiting function
const checkRateLimit = (userId: string): boolean => {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  userLimit.count++;
  return true;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<
    | { messages: TeamMessage[]; pagination?: { page: number; limit: number; total: number; hasMore: boolean } }
    | { message: string }
    | { error: string }
  >
) {
  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;

    // Check rate limit
    if (!checkRateLimit(userId)) {
      return res.status(429).json({
        error: "Rate limit exceeded. Please wait before making more requests."
      });
    }
    
    // Handle GET request to fetch client's messages with pagination
    if (req.method === "GET") {
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 50); // Max 50 items per page
      const offset = (page - 1) * limit;

      // Get total count for this user
      const countSnapshot = await db
        .collection("team-messages")
        .where("userId", "==", userId)
        .count()
        .get();
      const total = countSnapshot.data().count;

      // Fetch messages with pagination
      const snapshot = await db
        .collection("team-messages")
        .where("userId", "==", userId) // Only fetch messages for this user
        .orderBy("timestamp", "desc")
        .limit(limit)
        .offset(offset)
        .get();

      const messages: TeamMessage[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...(doc.data() as Omit<TeamMessage, "id">),
      }));

      const hasMore = offset + messages.length < total;

      return res.status(200).json({
        messages,
        pagination: {
          page,
          limit,
          total,
          hasMore
        }
      });
    }

    // Handle unsupported methods
    return res.status(405).json({ error: "Method not allowed" });
  } catch (error: any) {
    console.error("Error handling client messages:", error);
    
    // Don't expose internal error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    return res.status(500).json({
      error: isDevelopment
        ? `Internal server error: ${error.message}`
        : "Internal server error"
    });
  }
}
