
import type { NextApiRequest, NextApiResponse } from 'next';
import { Resend } from 'resend';
import { auth, db } from '@/lib/firebase-admin';
import { isEmailNotificationsEnabled } from '@/lib/user-preferences';
import { getAdminUserEmails, getUserEmailById } from '@/lib/user-utils';
import { StatusChangeEmail } from '@/emails/StatusChangeEmail';
import { ProgressUpdateEmail } from '@/emails/ProgressUpdateEmail';
import ReactDOMServer from 'react-dom/server';
import React from 'react';


// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const token = authHeader.split('Bearer ')[1];
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Verify the token and get the user
    let decodedToken;
    let userId;
    
    try {
      // Try ID token first
      decodedToken = await auth.verifyIdToken(token);
      userId = decodedToken.uid;
    } catch (idTokenError: any) {
      // If ID token fails, try session cookie
      try {
        decodedToken = await auth.verifySessionCookie(token);
        userId = decodedToken.uid;
      } catch (sessionError: any) {
        console.error('Token verification failed:', { idTokenError: idTokenError.message, sessionError: sessionError.message });
        return res.status(401).json({ error: 'Invalid token' });
      }
    }

    // Get request body
    const {
      scanId,
      recipients,
      oldStatus,
      newStatus,
      scanDetails,
      progressUpdate // Add progressUpdate to the destructured body
    } = req.body;

    // Adjust validation based on whether it's a status update or progress update
    if (!scanId || !recipients || !Array.isArray(recipients) || !scanDetails) {
      return res.status(400).json({ error: 'Missing required fields for email' });
    }

    if (progressUpdate === undefined && !newStatus) {
      return res.status(400).json({ error: 'Missing newStatus field for email' });
    }

    // Format status for display
    const formatStatus = (status: string) => {
      switch (status) {
        case 'pending': return 'Pending';
        case 'in-progress': return 'In Progress';
        case 'completed': return 'Completed';
        case 're-test': return 'Re-test';
        default: return status;
      }
    };

    // Format asset type for display
    const formatAssetType = (assetType: string) => {
      if (!assetType) return 'Unknown';
      return assetType
        .replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    let subject: string;
    let html: string;

    if (progressUpdate !== undefined) {
      subject = `Pen Test Progress Update: ${progressUpdate}% Complete`;
      html = ReactDOMServer.renderToStaticMarkup(
        React.createElement(ProgressUpdateEmail, {
          scanName: scanDetails.target,
          assetType: scanDetails.asset_type,
          progress: progressUpdate,
          scanId: scanId,
        })
      );
    } else {
      if (oldStatus) {
        subject = `Scan Status Update: ${formatStatus(newStatus)}`;
      } else {
        subject = `New Scan Created: ${formatStatus(newStatus)}`;
      }
      html = ReactDOMServer.renderToStaticMarkup(
        React.createElement(StatusChangeEmail, {
          scanName: scanDetails.target,
          assetType: scanDetails.asset_type,
          oldStatus: oldStatus || 'New',
          newStatus: newStatus,
          scanId: scanId,
        })
      );
    }

    // Get admin user emails
    const adminEmails = await getAdminUserEmails();
    const recipientsSet = new Set(adminEmails);

    // Add the user who triggered the action to the recipients list
    const triggererEmail = await getUserEmailById(userId);
    if (triggererEmail) {
      recipientsSet.add(triggererEmail);
    }

    // If it's a status update or progress update, also add the original creator of the scan
    if (newStatus || progressUpdate !== undefined) {
        const scanDoc = await db.collection('scans').doc(scanId).get();
        if (scanDoc.exists) {
            const scanData = scanDoc.data();
            if (scanData && scanData.userId) {
                const originalCreatorEmail = await getUserEmailById(scanData.userId);
                if (originalCreatorEmail) {
                    recipientsSet.add(originalCreatorEmail);
                }
            }
        }
    }
    
    const recipientsList = Array.from(recipientsSet);

    if (recipientsList.length === 0) {
      console.log('No recipients found to send email to.');
      return res.status(200).json({
        success: true,
        message: 'No recipients found to send email to.',
        totalRecipients: 0,
        successCount: 0,
        failureCount: 0,
        emailResults: []
      });
    }

    // Send email to all recipients
    let emailResults = [];
    let successCount = 0;
    let failureCount = 0;

    for (const recipientEmail of recipientsList) {
      try {
        const result = await resend.emails.send({
          from: 'DeepScan <<EMAIL>>',
          to: recipientEmail,
          subject: subject,
          html: html,
          replyTo: '<EMAIL>'
        });
        
        console.log(`✅ Email sent successfully to ${recipientEmail}:`, result.data?.id || 'no-id');
        emailResults.push({ email: recipientEmail, success: true, id: result.data?.id || null });
        successCount++;
      } catch (emailError: any) {
        console.error(`❌ Failed to send email to ${recipientEmail}:`, emailError.message);
        emailResults.push({ email: recipientEmail, success: false, error: emailError.message });
        failureCount++;
      }
    }

    console.log(`📊 Email sending summary: ${successCount} successful, ${failureCount} failed`);

    // Log email notification results in database
    const logData: any = {
      scanId,
      recipients: recipientsList,
      emailResults,
      successCount,
      failureCount,
      sentAt: new Date().toISOString(),
      sentBy: userId,
      success: successCount > 0
    };

    if (progressUpdate !== undefined) {
      logData.progressUpdate = progressUpdate;
    } else {
      logData.oldStatus = oldStatus;
      logData.newStatus = newStatus;
    }

    await db.collection('email-notifications').add(logData);
    console.log(`📝 Email notification logged to database for scan ${scanId}`);

    return res.status(200).json({
      success: successCount > 0,
      message: `Email notifications: ${successCount} sent successfully, ${failureCount} failed`,
      totalRecipients: recipientsList.length,
      successCount,
      failureCount,
      emailResults
    });
  } catch (error: any) {
    console.error('Error sending email notification:', error);

    // Log failed email attempt
    try {
      const errorLogData: any = {
        scanId: req.body.scanId,
        recipients: req.body.recipients,
        sentAt: new Date().toISOString(),
        error: error.message || 'Unknown error',
        success: false
      };

      if (req.body.progressUpdate !== undefined) {
        errorLogData.progressUpdate = req.body.progressUpdate;
      } else {
        errorLogData.oldStatus = req.body.oldStatus;
        errorLogData.newStatus = req.body.newStatus;
      }

      await db.collection('email-notifications').add(errorLogData);
    } catch (logError) {
      console.error('Error logging email failure:', logError);
    }

    return res.status(500).json({
      error: `Failed to send email notification: ${error.message}`
    });
  }
}
