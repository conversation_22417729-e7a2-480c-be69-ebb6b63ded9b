import type { NextApiRequest, NextApiResponse } from "next";
import formidable, { Fields, Files } from "formidable"; // Import types
import fs from "fs";
import { db, getBucket } from "@/lib/firebase-admin";
import {
  MAX_FILE_SIZE,
  validateFileSize,
  validateFileExtension,
  getContentType,
  formatFileSize,
  getFileErrorMessage,
  generateUniqueFileName
} from "@/lib/file-utils";
import { formatScanTitle } from "@/lib/utils/formatScanTitle";

// Disable Next.js body parser
export const config = {
  api: {
    bodyParser: false,
  },
};

// Define the response type for successful uploads
interface SuccessResponse {
  message: string;
  fileUrl?: string;
  file?: {
    originalName: string;
    size: number;
    contentType: string;
    uploadedAt: string;
    url: string;
  };
  vulnerabilityCounts?: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  aiSummary?: any;
}

// Define the error response type
interface ErrorResponse {
  error: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SuccessResponse | ErrorResponse>
) {
  console.log("[upload-scan-result] Handler started");

  if (req.method !== "POST") {
    console.log(`[upload-scan-result] Method ${req.method} not allowed`);
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  console.log("[upload-scan-result] Configuring formidable");
  // Configure formidable with file size limit
  const form = formidable({
    maxFileSize: MAX_FILE_SIZE, // 10MB limit
  });

  // Add types to callback parameters
  console.log("[upload-scan-result] Starting to parse form data");
  form.parse(req, async (err: Error | null, fields: Fields, files: Files) => {
    console.log("[upload-scan-result] Form parsing callback entered");

    // Handle formidable errors (including file size limit)
    if (err) {
      console.error("[upload-scan-result] Error parsing form data:", err);
      // Check if it's a file size error
      if (err.message && err.message.includes("maxFileSize")) {
        console.log("[upload-scan-result] File size error detected");
        return res.status(413).json({
          error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
        });
      }
      return res.status(500).json({ error: "Error parsing form data" });
    }

    console.log("[upload-scan-result] Form parsed successfully");

    // Ensure fields.scanId is treated as a string
    console.log("[upload-scan-result] Processing form fields:", JSON.stringify(fields));
    const scanId = Array.isArray(fields.scanId)
      ? fields.scanId[0]
      : fields.scanId;
    console.log(`[upload-scan-result] Extracted scanId: ${scanId}`);

    // Check if we should replace existing files
    const replaceExisting = Array.isArray(fields.replaceExisting)
      ? fields.replaceExisting[0] === 'true'
      : fields.replaceExisting === 'true';
    console.log(`[upload-scan-result] Replace existing files: ${replaceExisting}`);

    // Ensure files.file is treated as a single File object
    console.log("[upload-scan-result] Files received:", Object.keys(files));
    const file = Array.isArray(files.file) ? files.file[0] : files.file;
    console.log(`[upload-scan-result] File object extracted: ${file ? 'yes' : 'no'}`);

    if (!scanId) {
      console.log("[upload-scan-result] Error: Missing scanId");
      return res.status(400).json({ error: "Missing scanId" });
    }

    // Check if file exists and has necessary properties
    if (!file || !file.filepath || !file.originalFilename) {
      console.log("[upload-scan-result] Error: Invalid file uploaded", file);
      return res.status(400).json({ error: "Invalid file uploaded" });
    }
    console.log(`[upload-scan-result] File details - Name: ${file.originalFilename}, Size: ${file.size}, Path: ${file.filepath}`);

    // Validate file extension
    if (!validateFileExtension(file.originalFilename)) {
      console.log(`[upload-scan-result] Error: Invalid file extension for ${file.originalFilename}`);
      return res.status(400).json({
        error: "Invalid file type. Only PDF and Word documents are allowed."
      });
    }
    console.log("[upload-scan-result] File extension validated successfully");

    // Double-check file size (in case formidable config is bypassed)
    if (!validateFileSize(file.size)) {
      console.log(`[upload-scan-result] Error: File too large (${file.size} bytes)`);
      return res.status(413).json({
        error: `File too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`
      });
    }
    console.log("[upload-scan-result] File size validated successfully");

    try {
      console.log("[upload-scan-result] Starting try block for main processing");

      // Get the scan document to check for existing files
      console.log(`[upload-scan-result] Getting scan document with ID: ${scanId}`);
      const scanRef = db.collection("scans").doc(scanId);
      const scanDoc = await scanRef.get();
      console.log(`[upload-scan-result] Scan document exists: ${scanDoc.exists}`);

      if (!scanDoc.exists) {
        console.log(`[upload-scan-result] Error: Scan with ID ${scanId} not found`);
        return res.status(404).json({ error: "Scan not found" });
      }

      const scanData = scanDoc.data();
      console.log(`[upload-scan-result] Retrieved scan data: ${scanData ? 'yes' : 'no'}`);

      // OPTIMIZATION: Process the PDF directly from the uploaded file first
      console.log(`[upload-scan-result] Processing PDF directly from uploaded file for scan ${scanId}`);

      // Load required modules
      console.log("[upload-scan-result] Loading required modules");
      const pdfParse = require('pdf-parse');
      const OpenAI = require('openai');
      const fs = require('fs');

      // Initialize OpenAI client
      console.log("[upload-scan-result] Initializing OpenAI client");
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      console.log(`[upload-scan-result] OpenAI API key available: ${!!process.env.OPENAI_API_KEY}`);

      // Read the file directly from disk
      console.log(`[upload-scan-result] Reading file from disk: ${file.filepath}`);
      let reportBuffer;
      try {
        reportBuffer = fs.readFileSync(file.filepath);
        console.log(`[upload-scan-result] File read successfully, buffer size: ${reportBuffer.length} bytes`);
      } catch (readError: any) {
        console.error(`[upload-scan-result] Error reading file: ${readError.message}`);
        throw readError;
      }

      // Parse the PDF to extract text
      console.log(`[upload-scan-result] PDF Buffer received, size: ${reportBuffer.length} bytes`);
      let pdfData, reportText, aiResponse, vulnerabilityCounts;
      let keyFindingsSection = ""; // Moved outside the try block so it's accessible later

      try {
        // Process the PDF and generate AI summary in parallel with the file upload
        console.log("[upload-scan-result] Starting PDF parsing");
        pdfData = await pdfParse(reportBuffer);
        reportText = pdfData.text;

        // Look for sections that might contain tables with vulnerability information
        // These are often labeled as "Key Findings", "Vulnerabilities", etc.
        console.log("[upload-scan-result] Searching for Key Findings section in the report");

        // Try multiple patterns to find the key findings section
        const keyFindingPatterns = [
          // Standard key findings section
          /(?:Key Findings|Vulnerabilities|Security Issues|Findings)[\s\S]*?(?=\n\n\n|\n\n[A-Z]|$)/i,

          // Table with findings
          /(?:Finding|Vulnerability|Issue)[\s\S]*?(?:Severity|Risk|Impact)[\s\S]*?(?:\n-{3,}|\n={3,}|\n\n\n|\n\n[A-Z]|$)/i,

          // Numbered or bulleted list of findings
          /(?:\d+\.\s+|\*\s+|•\s+)(?:.*?vulnerability|.*?issue|.*?finding)[\s\S]*?(?:\n\n\n|\n\n[A-Z]|$)/i,

          // Section with "Results" or "Summary"
          /(?:Results|Summary|Assessment)[\s\S]*?(?:\n\n\n|\n\n[A-Z]|$)/i
        ];

        // Try each pattern until we find a match
        for (const pattern of keyFindingPatterns) {
          const match = reportText.match(pattern);
          if (match) {
            keyFindingsSection = match[0];
            console.log(`[upload-scan-result] Found potential Key Findings section using pattern: ${pattern}`);
            console.log("[upload-scan-result] Key Findings excerpt:", keyFindingsSection.substring(0, 500) + "...");
            break;
          }
        }

        // If no match found with patterns, use a larger chunk of the text
        if (!keyFindingsSection) {
          console.log("[upload-scan-result] No specific Key Findings section found, using first part of the report");
          keyFindingsSection = reportText.substring(0, Math.min(reportText.length, 15000));
        }

        // If the text is too long, truncate it to fit within OpenAI's token limits
        // But make sure to preserve the Key Findings section if found
        const words = reportText.split(/\s+/);
        if (words.length > 6000) {
          // If we found a key findings section, make sure it's included in the truncated text
          if (keyFindingsSection) {
            // First, remove the key findings section from the original text to avoid duplication
            const textWithoutKeyFindings = reportText.replace(keyFindingsSection, "");
            const wordsWithoutKeyFindings = textWithoutKeyFindings.split(/\s+/);

            // Calculate how many words we can include (leaving room for the key findings)
            const keyFindingsWords = keyFindingsSection.split(/\s+/).length;
            const maxWords = Math.min(6000 - keyFindingsWords, wordsWithoutKeyFindings.length);

            // Create the truncated text with the key findings section at the beginning
            reportText = keyFindingsSection + "\n\n" +
                        wordsWithoutKeyFindings.slice(0, maxWords).join(' ') +
                        '... [content truncated due to length]';
          } else {
            // No key findings section found, just truncate normally
            reportText = words.slice(0, 6000).join(' ') + '... [content truncated due to length]';
          }
          console.log("Text truncated from", words.length, "words to approximately 6000 words");
        }

        console.log(`Extracted ${words.length} words from PDF`);

        // Create a prompt for the AI with the extracted text
        const target = scanData?.target || scanData?.targetDetails || '';
        const prompt = `
          Please analyze this security scan report for ${target} and provide a concise summary.

          Here is the extracted text from the report:

          ${reportText}

          MOST IMPORTANT TASK: Look for a "Key Findings" table or section in the report. This table typically lists vulnerabilities with their severity levels (Critical, High, Medium, Low). Count the exact number of vulnerabilities for each severity level from this table.

          Structure your response in JSON format with the following sections:
          1. "summary": A brief overview (2-3 sentences)
          2. "vulnerabilityCounts": Count of vulnerabilities by severity level EXACTLY as they appear in the Key Findings table
          3. "keyFindings": List of the most important security findings (array of objects with "finding" and "severity" properties)
          4. "potentialImpacts": List of potential impacts of these vulnerabilities (array of strings)
          5. "recommendations": List of recommended actions (array of objects with "recommendation" and "priority" properties)

          Example format:
          {
            "summary": "This scan identified 5 critical vulnerabilities in the system...",
            "vulnerabilityCounts": {
              "critical": 2,
              "high": 3,
              "medium": 5,
              "low": 8
            },
            "keyFindings": [
              {"finding": "Insecure authentication mechanism", "severity": "critical"},
              {"finding": "Unpatched software components", "severity": "high"}
            ],
            "potentialImpacts": ["Unauthorized data access", "System compromise"],
            "recommendations": [
              {"recommendation": "Update all software components", "priority": "high"},
              {"recommendation": "Implement multi-factor authentication", "priority": "medium"}
            ]
          }

          For severity, use one of: "critical", "high", "medium", or "low".
          For priority, use one of: "immediate", "high", "medium", or "low".

          CRITICAL INSTRUCTION: For the vulnerabilityCounts, do NOT estimate or guess. Count the EXACT number of findings for each severity level from the Key Findings table in the report. If you can identify a table with columns for ID, Vulnerability, and Severity, use that table to count the vulnerabilities by severity level.

          Ensure your response is valid JSON. Do not include any HTML tags or markdown formatting.
        `;

        // Get AI analysis
        console.log("[upload-scan-result] Sending to OpenAI for analysis");
        let completion;
        try {
          completion = await openai.chat.completions.create({
            model: "gpt-4.1-nano",
            messages: [
              {
                role: "system",
                content: "You are a cybersecurity expert specializing in vulnerability assessment. Your primary task is to accurately extract vulnerability counts from security reports. Look for tables labeled 'Key Findings' or similar that list vulnerabilities with severity levels. Count the exact number of vulnerabilities for each severity level (Critical, High, Medium, Low) from these tables. Provide clear, concise summaries in structured JSON format."
              },
              {
                role: "user",
                content: prompt
              }
            ],
            temperature: 0.3, // Lower temperature for more deterministic results
            response_format: { type: "json_object" } // Ensure we get valid JSON
          });
          console.log("[upload-scan-result] OpenAI API call successful");
        } catch (openaiError: any) {
          console.error(`[upload-scan-result] Error calling OpenAI API: ${openaiError.message}`);
          throw openaiError;
        }

        // Get the raw response from OpenAI
        const rawResponse = completion.choices[0].message.content || '';
        console.log(`[upload-scan-result] Raw OpenAI response received, length: ${rawResponse.length} characters`);

        // Parse the JSON response
        try {
          console.log("[upload-scan-result] Parsing OpenAI JSON response");
          aiResponse = JSON.parse(rawResponse);
          console.log("[upload-scan-result] JSON parsed successfully");

          // Ensure the response has the expected structure
          if (!aiResponse.summary) {
            console.log("[upload-scan-result] No summary found in response, adding default");
            aiResponse.summary = "Analysis complete. See details below.";
          }

          if (!aiResponse.vulnerabilityCounts) {
            console.log("[upload-scan-result] No vulnerability counts found in response, adding defaults");
            aiResponse.vulnerabilityCounts = {
              critical: 0,
              high: 0,
              medium: 0,
              low: 0
            };
          }

          if (!Array.isArray(aiResponse.keyFindings)) {
            console.log("[upload-scan-result] No key findings array found in response, adding empty array");
            aiResponse.keyFindings = [];
          }

          if (!Array.isArray(aiResponse.potentialImpacts)) {
            console.log("[upload-scan-result] No potential impacts array found in response, adding empty array");
            aiResponse.potentialImpacts = [];
          }

          if (!Array.isArray(aiResponse.recommendations)) {
            console.log("[upload-scan-result] No recommendations array found in response, adding empty array");
            aiResponse.recommendations = [];
          }

        } catch (parseError) {
          console.error("[upload-scan-result] Error parsing OpenAI JSON response:", parseError);
          console.log("[upload-scan-result] Raw response:", rawResponse);

          // Fallback to using the raw response as the summary
          console.log("[upload-scan-result] Using fallback response structure");
          aiResponse = {
            summary: "Unable to parse structured response. See raw analysis below.",
            rawAnalysis: rawResponse,
            vulnerabilityCounts: {
              critical: 0,
              high: 0,
              medium: 0,
              low: 0
            },
            keyFindings: [],
            potentialImpacts: [],
            recommendations: []
          };
        }

        // Extract vulnerability counts from AI response
        vulnerabilityCounts = aiResponse.vulnerabilityCounts || {
          critical: 0,
          high: 0,
          medium: 0,
          low: 0
        };

        // Log the extracted vulnerability counts for debugging
        console.log(`Extracted vulnerability counts for scan ${scanId}:`, {
          critical: vulnerabilityCounts.critical || 0,
          high: vulnerabilityCounts.high || 0,
          medium: vulnerabilityCounts.medium || 0,
          low: vulnerabilityCounts.low || 0
        });

      } catch (pdfError) {
        console.error("Error processing PDF:", pdfError);
        // Continue with the upload even if PDF processing fails
        reportText = "Error processing PDF content"; // Define reportText in case of error
        aiResponse = {
          summary: "Error processing PDF. The file may be corrupted or password-protected.",
          vulnerabilityCounts: {
            critical: 0,
            high: 0,
            medium: 0,
            low: 0
          },
          keyFindings: [],
          potentialImpacts: [],
          recommendations: []
        };
        vulnerabilityCounts = aiResponse.vulnerabilityCounts;
      }

      // Now upload the file to Firebase Storage
      console.log("Uploading file to Firebase Storage");
      const bucket = getBucket();

      // Generate a unique filename that preserves the original name
      const fileName = generateUniqueFileName(file.originalFilename);
      const filePath = `scan-results/${scanId}/${fileName}`;

      // Get content type based on file extension
      const contentType = getContentType(file.originalFilename);

      // Create write stream with proper content type
      const fileUploadStream = bucket.file(filePath).createWriteStream({
        metadata: {
          contentType: contentType,
        },
        resumable: false, // For small files, non-resumable uploads are faster
      });

      // Set up error handling for the stream
      fileUploadStream.on('error', (error: Error) => {
        console.error("Stream error during file upload:", error);
        throw error;
      });

      // Use streaming instead of reading entire file into memory
      const readStream = fs.createReadStream(file.filepath);

      // Handle read stream errors
      readStream.on('error', (error: Error) => {
        console.error("Error reading file:", error);
        throw error;
      });

      // Pipe the file to Firebase Storage
      await new Promise<void>((resolve, reject) => {
        readStream.pipe(fileUploadStream)
          .on('finish', () => resolve())
          .on('error', (error: Error) => reject(error));
      });

      // Get the download URL (use a more reasonable expiration)
      const [url] = await bucket.file(filePath).getSignedUrl({
        action: "read",
        expires: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      });

      // If replacing existing files and there are existing files in storage, delete them
      if (replaceExisting && scanData && scanData.files && Array.isArray(scanData.files) && scanData.files.length > 0) {
        try {
          // For each file, extract the path from the URL and delete it
          for (const existingFile of scanData.files) {
            if (existingFile.url && existingFile.url.includes('scan-results')) {
              // Extract the path from the URL - this is a simplistic approach and might need adjustment
              // based on your actual URL structure
              const urlPath = existingFile.url.split('scan-results/')[1];
              if (urlPath) {
                const fullPath = `scan-results/${urlPath}`;
                try {
                  await bucket.file(fullPath).delete();
                  console.log(`Deleted previous file: ${fullPath}`);
                } catch (deleteErr) {
                  // Log but continue if we can't delete a file
                  console.error(`Error deleting file ${fullPath}:`, deleteErr);
                }
              }
            }
          }
        } catch (deleteError) {
          console.error("Error deleting existing files:", deleteError);
          // Continue with the upload even if deletion fails
        }
      }

      // Create a new file object for the database
      const newFile = {
        originalName: file.originalFilename,
        size: file.size,
        contentType: contentType,
        uploadedAt: new Date().toISOString(),
        url: url
      };

      // Update the scan document in Firestore with all information at once
      // This includes the file URL, file info, and AI summary data
      await scanRef.update({
        resultFileUrl: url,
        resultUploadedAt: new Date().toISOString(),
        // Replace existing files array with the new file
        files: [newFile],
        // Add the AI summary and vulnerability counts that we already generated
        aiSummary: aiResponse,
        criticalVulnerabilities: vulnerabilityCounts.critical || 0,
        highVulnerabilities: vulnerabilityCounts.high || 0,
        mediumVulnerabilities: vulnerabilityCounts.medium || 0,
        lowVulnerabilities: vulnerabilityCounts.low || 0,
        vulnerabilitiesUpdatedAt: new Date().toISOString()
      });

      // Extract vulnerabilities from the report and save them to the database
      try {
        console.log(`[upload-scan-result] Extracting vulnerabilities from report for scan ${scanId}`);

        // Create a prompt for OpenAI to extract vulnerabilities
        console.log("[upload-scan-result] Creating prompt for vulnerability extraction");
        const extractPrompt = `
IMPORTANT: Extract ALL individual vulnerabilities from the following security report.
Pay special attention to the "Key Findings" or "Vulnerabilities" section if available.

Report Text:
${keyFindingsSection || reportText.substring(0, 10000)}

CRITICAL INSTRUCTIONS:
1. Be extremely thorough and extract EVERY vulnerability mentioned in the report, especially in tables or lists.
2. Look for any tables that list findings with severity ratings.
3. Make sure to capture ALL vulnerabilities, even if they are mentioned briefly or in passing.
4. Pay special attention to any numbered or bulleted lists of findings.
5. If there's a table with columns for ID, Finding/Vulnerability, and Severity, extract each row as a separate vulnerability.

For each vulnerability, extract the following information:
1. Name/title of the vulnerability
2. Severity level (critical, high, medium, low)
3. CVSS score (if available)
4. Brief description
5. Affected component or system

Return the results as a JSON array of vulnerability objects. Each object should have:
- name: The name or title of the vulnerability
- severity: The severity level (critical, high, medium, low)
- cvss: The CVSS score as a number (e.g., 9.8)
- description: A brief description of the vulnerability
- affectedComponent: The affected component or system

Example format:
{
  "vulnerabilities": [
    {
      "name": "SQL Injection in Login Form",
      "severity": "critical",
      "description": "The login form is vulnerable to SQL injection attacks, allowing attackers to bypass authentication.",
      "affectedComponent": "Authentication System"
    },
    {
      "name": "Outdated SSL Certificate",
      "severity": "medium",
      "description": "The SSL certificate is outdated and using deprecated encryption algorithms.",
      "affectedComponent": "Web Server"
    }
  ]
}

IMPORTANT: Be exhaustive in your extraction. Do not miss any vulnerabilities mentioned in the report.
If no vulnerabilities can be extracted, return an empty array.
`;

        // Call OpenAI API to extract vulnerabilities
        console.log("[upload-scan-result] Calling OpenAI API for vulnerability extraction");
        let extractCompletion;
        try {
          extractCompletion = await openai.chat.completions.create({
            model: "gpt-4.1-nano", // Using a more capable model for better extraction
            messages: [
              {
                role: "system",
                content: "You are a cybersecurity expert specializing in vulnerability assessment. Your primary task is to thoroughly extract ALL vulnerabilities from security reports, especially from Key Findings sections and tables. Be extremely detailed and make sure to capture every single vulnerability mentioned in the report, even if mentioned briefly. Pay special attention to tables that list findings with severity ratings.",
              },
              {
                role: "user",
                content: extractPrompt,
              },
            ],
            temperature: 0.1, // Lower temperature for more deterministic results
            response_format: { type: "json_object" },
          });
          console.log("[upload-scan-result] OpenAI API call for vulnerability extraction successful");
        } catch (openaiError: any) {
          console.error(`[upload-scan-result] Error calling OpenAI API for vulnerability extraction: ${openaiError.message}`);
          throw openaiError;
        }

        // Get the response
        const extractRawResponse = extractCompletion.choices[0].message.content || "";
        console.log(`[upload-scan-result] Raw vulnerability extraction response received, length: ${extractRawResponse.length} characters`);

        // Parse the JSON response
        let extractedVulnerabilities: any[] = [];
        try {
          console.log("[upload-scan-result] Parsing vulnerability extraction JSON response");
          const parsedResponse = JSON.parse(extractRawResponse);
          extractedVulnerabilities = parsedResponse.vulnerabilities || [];
          console.log(`[upload-scan-result] Found ${extractedVulnerabilities.length} vulnerabilities in response`);
        } catch (parseError) {
          console.error("[upload-scan-result] Error parsing OpenAI JSON response for vulnerabilities:", parseError);
          console.log("[upload-scan-result] Raw vulnerability extraction response:", extractRawResponse);
        }

        // If vulnerabilities were extracted, save them to Firestore
        if (extractedVulnerabilities.length > 0) {
          console.log(`[upload-scan-result] Extracted ${extractedVulnerabilities.length} vulnerabilities from report`);

          console.log("[upload-scan-result] Creating Firestore batch for saving vulnerabilities");
          const batch = db.batch();
          let validVulnerabilityCount = 0;

          for (const vuln of extractedVulnerabilities) {
            // Validate the vulnerability data
            if (!vuln.name || !vuln.severity || !vuln.description || !vuln.affectedComponent) {
              console.log("[upload-scan-result] Skipping invalid vulnerability:", vuln);
              continue;
            }

            // Normalize severity
            let normalizedSeverity = vuln.severity.toLowerCase();
            if (!["critical", "high", "medium", "low"].includes(normalizedSeverity)) {
              console.log(`[upload-scan-result] Normalizing non-standard severity: ${normalizedSeverity}`);
              // Map other severity terms to our standard levels
              if (normalizedSeverity.includes("crit")) normalizedSeverity = "critical";
              else if (normalizedSeverity.includes("high")) normalizedSeverity = "high";
              else if (normalizedSeverity.includes("med")) normalizedSeverity = "medium";
              else if (normalizedSeverity.includes("low")) normalizedSeverity = "low";
              else normalizedSeverity = "medium"; // Default
              console.log(`[upload-scan-result] Normalized to: ${normalizedSeverity}`);
            }

            // Create a new vulnerability document
            const vulnRef = db.collection("vulnerabilities").doc();
            console.log(`[upload-scan-result] Created new vulnerability document with ID: ${vulnRef.id}`);

            const vulnData = {
              name: vuln.name,
              severity: normalizedSeverity,
              description: vuln.description,
              affectedComponent: vuln.affectedComponent,
              scanId: scanId,
              scanName: formatScanTitle(scanData || {}),
              target: scanData?.target || scanData?.targetDetails || "",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              status: "open",
              cvss: vuln.cvss || null,
            };
            console.log(`[upload-scan-result] Vulnerability data prepared: ${JSON.stringify(vulnData).substring(0, 200)}...`);

            batch.set(vulnRef, vulnData);
            validVulnerabilityCount++;
          }

          // Commit the batch
          console.log(`[upload-scan-result] Committing batch with ${validVulnerabilityCount} vulnerabilities`);
          try {
            await batch.commit();
            console.log(`[upload-scan-result] Successfully saved ${validVulnerabilityCount} vulnerabilities to database`);
          } catch (batchError: any) {
            console.error(`[upload-scan-result] Error committing vulnerability batch: ${batchError.message}`);
            throw batchError;
          }
        } else {
          console.log("[upload-scan-result] No vulnerabilities extracted from report");
        }
      } catch (extractError: any) {
        console.error(`[upload-scan-result] Error extracting vulnerabilities: ${extractError.message}`);
        console.error("[upload-scan-result] Full error:", extractError);
        // Continue with the upload even if vulnerability extraction fails
        console.log("[upload-scan-result] Continuing with upload despite vulnerability extraction failure");
      }

      console.log(`AI summary generated and saved for scan ${scanId}`);

      // Get the updated scan data to return to the client
      const updatedScanData = (await scanRef.get()).data();

      // Return success response with URL, updated vulnerability counts, and AI summary
      res.status(200).json({
        message: "File uploaded and scan updated successfully",
        fileUrl: url,
        file: newFile,
        vulnerabilityCounts: {
          critical: updatedScanData?.criticalVulnerabilities || 0,
          high: updatedScanData?.highVulnerabilities || 0,
          medium: updatedScanData?.mediumVulnerabilities || 0,
          low: updatedScanData?.lowVulnerabilities || 0
        },
        aiSummary: updatedScanData?.aiSummary || null
      });
    } catch (error: any) {
      console.error("Error uploading file or updating scan:", error);

      // Use our helper to get a user-friendly error message
      const errorMessage = getFileErrorMessage(error);
      res.status(500).json({ error: errorMessage });
    } finally {
      // Clean up the temporary file created by formidable
      fs.unlink(file.filepath, (unlinkErr: NodeJS.ErrnoException | null) => {
        if (unlinkErr)
          console.error("Error deleting temporary file:", unlinkErr);
      });
    }
  });
}
