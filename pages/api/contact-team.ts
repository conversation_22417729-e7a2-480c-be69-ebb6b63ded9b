import type { NextApiRequest, NextApiResponse } from "next";
import { auth, db } from "@/lib/firebase-admin";
const Pusher = require('pusher');

const pusher = new Pusher({
  appId: process.env.PUSHER_APP_ID,
  key: process.env.PUSHER_KEY,
  secret: process.env.PUSHER_SECRET,
  cluster: process.env.PUSHER_CLUSTER,
  useTLS: true
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; message: string } | { error: string }>
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Get authorization token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const token = authHeader.split("Bearer ")[1];
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  try {
    // Verify the token and get the user
    const decodedToken = await auth.verifyIdToken(token);
    const userId = decodedToken.uid;
    const userEmail = decodedToken.email || "unknown";

    // Get the message from the request body
    const { message } = req.body;
    if (!message || typeof message !== "string") {
      return res.status(400).json({ error: "Missing or invalid message" });
    }

    // Create a new document in the "team-messages" collection
    const timestamp = new Date().toISOString();
    const messageData = {
      userId,
      userEmail,
      message,
      timestamp,
      status: "unread",
    };

    const docRef = await db.collection("team-messages").add(messageData);

    // Create the complete message object for Pusher notification
    const newMessage = {
      id: docRef.id,
      ...messageData
    };

    // Emit the message using Pusher to admin channel
    try {
      await pusher.trigger('admin-messages', 'new-message', newMessage);
      console.log(`New message ${docRef.id} created for user ${userId}`);
    } catch (error) {
      console.error('Error emitting new message event:', error);
    }

    // Return success
    return res.status(200).json({
      success: true,
      message: "Message sent to team successfully",
    });
  } catch (error: any) {
    console.error("Error sending message to team:", error);
    return res.status(500).json({
      error: `Internal server error: ${error.message}`,
    });
  }
}
