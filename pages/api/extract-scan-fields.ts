import type { NextApiRequest, NextApiResponse } from "next";

const promptTemplate = (conversation: string) =>
  `You are an expert AI assistant for GrowthGuard's security services.

TASK:
- Carefully analyze the conversation to extract information about a penetration test request
- Identify the asset type: Web Application, Mobile Application, API, Network, or Cloud Infrastructure
- Extract ALL relevant fields mentioned in the conversation
- Generate a concise, descriptive title for the conversation based on the extracted asset type and name. This title should be included in a field called "conversationTitle".
- Return ONLY a valid JSON object with no additional text or commentary
- Use empty strings for any required fields not mentioned in the conversation

IMPORTANT:
- Be thorough in your extraction - look for both direct answers and implied information
- If information is ambiguous, use the most specific interpretation
- Convert dates to YYYY-MM-DD format when possible
- Normalize field values to be consistent with examples
- The targetDetails field should contain the main URL, IP range, or identifier of the asset
- ALWAYS extract the application_name or asset_name when mentioned in any form
- Look for application names in phrases like "called X", "named X", "app name is X", etc.
- If the user mentions any files they've uploaded or plan to upload, include this information in a "files_mentioned" field
- If the user mentions specific reports or documentation, note this in the "documentation_notes" field
- Pay special attention to numbered questions in the conversation (e.g., "1. What is your application name?") as these follow our new conversation format

REQUIRED FIELDS BY ASSET TYPE:
---
Web Application:
  asset_type, url, targetDetails, function, authentication, tech_stack, app_type, focus_areas, exclusions, test_accounts, environment, test_window, sensitive_data, start_date, deadline, contact_name, contact_email

Mobile Application:
  asset_type, platforms, name, targetDetails, function, availability, authentication, sensitive_data, backend_api, payment, framework, libraries, offline, features, focus_areas, exclusions, test_accounts, environment, test_window, start_date, deadline, contact_name, contact_email

API:
  asset_type, base_url, targetDetails, api_type, authentication, permission_levels, endpoints, sensitive_data, tech_stack, database, documentation, focus_areas, exclusions, test_credentials, rate_limiting, environment, start_date, deadline, contact_name, contact_email

Network:
  asset_type, overview, targetDetails, assessment_type, ip_ranges, components, remote_access, operating_systems, critical_systems, exclusions, test_window, contacts, previous_tests, concerns, start_date, deadline, contact_name, contact_email

Cloud Infrastructure:
  asset_type, providers, targetDetails, resources, iac, network_config, access_management, environments, data_types, compliance, security_tools, monitoring_tools, assessment_type, focus_areas, exclusions, access, contacts, test_window, start_date, deadline, contact_name, contact_email
---

EXAMPLE OUTPUTS:

Web Application:
{
  "asset_type": "web_application",
  "application_name": "MyCompany Customer Portal",
  "asset_name": "MyCompany Customer Portal",
  "url": "https://app.mycompany.com",
  "targetDetails": "https://app.mycompany.com",
  "function": "Customer Portal",
  "authentication": "Username/password and Google SSO",
  "tech_stack": "React, Node.js, MongoDB",
  "app_type": "Single-page application",
  "focus_areas": "Payment processing, user data management",
  "exclusions": "Admin dashboard",
  "test_accounts": "Basic user, premium user, support staff",
  "environment": "Staging",
  "test_window": "Business hours (9 AM - 5 PM EST)",
  "sensitive_data": "PII, payment information",
  "start_date": "2025-05-05",
  "deadline": "2025-05-15",
  "contact_name": "John Smith",
  "contact_email": "<EMAIL>",
  "files_mentioned": "Architecture diagram, previous security report",
  "documentation_notes": "User mentioned they will upload API documentation",
  "conversationTitle": "Web App - MyCompany Customer Portal"
}

Mobile Application:
{
  "asset_type": "mobile_application",
  "application_name": "HealthTrack Pro",
  "asset_name": "HealthTrack Pro",
  "platforms": "iOS, Android",
  "name": "HealthTrack Pro",
  "targetDetails": "HealthTrack Pro (iOS/Android)",
  "function": "Health and fitness tracking",
  "availability": "App Store, Google Play",
  "authentication": "Google/Apple sign-in, biometric",
  "sensitive_data": "Health info, location, personal details",
  "backend_api": "Yes",
  "payment": "In-app purchases",
  "framework": "React Native",
  "libraries": "Analytics, crash reporting, payment",
  "offline": "Works offline with sync",
  "features": "Health tracking, location, premium",
  "focus_areas": "Health data security, payment processing",
  "exclusions": "None",
  "test_accounts": "Free, premium",
  "environment": "Staging",
  "test_window": "24/7",
  "start_date": "2025-05-10",
  "deadline": "2025-05-25",
  "contact_name": "Sarah Johnson",
  "contact_email": "<EMAIL>",
  "files_mentioned": "APK file, iOS TestFlight build",
  "documentation_notes": "User will provide API documentation for backend services",
  "conversationTitle": "Mobile App - HealthTrack Pro"
}

Here is the conversation:
${conversation}`;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log("[extract-scan-fields] API request received");

  if (req.method !== "POST") {
    console.log("[extract-scan-fields] Invalid method:", req.method);
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { conversation } = req.body;
  if (!conversation) {
    console.log("[extract-scan-fields] Missing conversation in request body");
    return res.status(400).json({ error: "Missing conversation" });
  }

  console.log("[extract-scan-fields] Conversation length:", conversation.length);
  console.log("[extract-scan-fields] Conversation preview:", conversation.substring(0, 200) + "...");

  try {
    console.log("[extract-scan-fields] Calling OpenAI API...");
    const openaiRes = await fetch(
      "https://api.openai.com/v1/chat/completions",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            {
              role: "system",
              content:
                "You are a helpful assistant that extracts structured data from conversations.",
            },
            { role: "user", content: promptTemplate(conversation) },
          ],
          temperature: 0,
        }),
      }
    );

    console.log("[extract-scan-fields] OpenAI API response status:", openaiRes.status);

    if (!openaiRes.ok) {
      const errorData = await openaiRes.text().catch(() => "Could not read error response");
      console.error("[extract-scan-fields] API error:", openaiRes.status, errorData);
      return res.status(500).json({ error: "API Error", details: errorData });
    }

    const data = await openaiRes.json();
    console.log("[extract-scan-fields] OpenAI API response received");

    const text = data.choices?.[0]?.message?.content;
    console.log("[extract-scan-fields] response content:", text ? text.substring(0, 200) + "..." : "No content");

    let extracted;
    try {
      extracted = JSON.parse(text);
      console.log("[extract-scan-fields] Successfully parsed JSON response");

      // Log all name-related fields for debugging
      console.log("[extract-scan-fields] Name-related fields:", JSON.stringify({
        application_name: extracted.application_name || "Not found",
        asset_name: extracted.asset_name || "Not found",
        name: extracted.name || "Not found",
        targetDetails: extracted.targetDetails || "Not found"
      }, null, 2));

      // Log other important fields
      console.log("[extract-scan-fields] Other important fields:", JSON.stringify({
        asset_type: extracted.asset_type || "Not found",
        function: extracted.function || "Not found",
        contact_name: extracted.contact_name || "Not found",
        contact_email: extracted.contact_email || "Not found"
      }, null, 2));

      // If application_name or asset_name is missing, try to infer it from other fields
      if (!extracted.application_name && !extracted.asset_name) {
        console.log("[extract-scan-fields] No application_name or asset_name found, attempting to infer");

        // Try to infer from name field (used in mobile applications)
        if (extracted.name && typeof extracted.name === 'string' && extracted.name.trim() !== '') {
          console.log(`[extract-scan-fields] Inferring application_name from name field: "${extracted.name}"`);
          extracted.application_name = extracted.name.trim();
        }
        // Try to infer from targetDetails if it doesn't look like a URL
        else if (extracted.targetDetails &&
                typeof extracted.targetDetails === 'string' &&
                extracted.targetDetails.trim() !== '' &&
                !extracted.targetDetails.includes('://') &&
                !extracted.targetDetails.includes('.com') &&
                !extracted.targetDetails.includes('.org') &&
                !extracted.targetDetails.includes('.net')) {
          console.log(`[extract-scan-fields] Inferring application_name from targetDetails: "${extracted.targetDetails}"`);
          extracted.application_name = extracted.targetDetails.trim();
        }
      }

      // Generate conversation title based on extracted fields
      let appName = "Unknown App";
      if (extracted.application_name?.trim() && extracted.application_name.toLowerCase() !== 'unknown') appName = extracted.application_name.trim();
      else if (extracted.asset_name?.trim() && extracted.asset_name.toLowerCase() !== 'unknown') appName = extracted.asset_name.trim();
      else if (extracted.name?.trim() && extracted.name.toLowerCase() !== 'unknown') appName = extracted.name.trim();
      else if (extracted.targetDetails?.trim() && !/:\/\//.test(extracted.targetDetails) && !/\.(com|org|net)/.test(extracted.targetDetails)) appName = extracted.targetDetails.trim();
      
      const businessFunc = extracted.function || "Unknown Function";
      extracted.conversationTitle = `${appName} - ${businessFunc}`;

    } catch (e) {
      console.error("[extract-scan-fields] Failed to parse", e);
      console.error("[extract-scan-fields] Raw response text:", text);
      return res
        .status(500)
        .json({ error: "Failed to parse.", response: text });
    }
 
     console.log("[extract-scan-fields] Returning successful response");
     return res.status(200).json({ fields: extracted });
   } catch (e) {
     console.error("[extract-scan-fields] Unhandled error:", e);
     return res
       .status(500)
       .json({ error: "Internal server error", details: (e as Error).message, stack: (e as Error).stack });
   }
 }
