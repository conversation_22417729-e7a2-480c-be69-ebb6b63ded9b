import type { NextApiRequest } from "next";
import type { QueryDocumentSnapshot } from "firebase-admin/firestore";
import { db, auth, storage } from "@/lib/firebase-admin";
import fetch from "node-fetch";
import { memoryCache } from "@/lib/cache";
import { scanOptimizer, OptimizedQueryBuilder } from "@/lib/scan-performance-optimizer";
import { NextApiResponseWithSocket, getSocketIO, emitScanCreated, emitScanUpdated, emitScanDeleted } from "@/lib/socket-server";
import { extractOrganizationFromEmail } from "@/lib/user-utils";
import { getNotificationRecipients, sendNotificationEmail } from "@/lib/email-utils";
import { getUserOrganization, getOrganizationMembers, getOrganizationScanIds } from "@/lib/organization-utils";
import {
  logOrganizationAccess,
  validateOrganizationAccess,
  checkOrganizationRateLimit,
  sanitizeOrganizationData
} from "@/lib/organization-security";

// Accept any asset-specific scan fields; type is loose for demo purposes
interface ScanRequestBody {
  asset_type: string;
  // All other fields are optional and dynamic
  [key: string]: any;
}

interface ScanType {
  id: string;
  userId?: string;
  userEmail?: string;
  status?: string;
  teamId?: string;
  managerId?: string;
  managerEmail?: string;
  stableId?: string;
  [key: string]: any;
}

// We don't need the ScanApiResponse type anymore since we're not using generics

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponseWithSocket // Remove generic type which is not supported
) {
  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  const userId = decodedToken.uid;
  const userEmail = decodedToken.email || "";

  if (req.method === "POST") {
    try {
      console.log("[scans:POST] Scan submission request received");

      // Accept the posted scan object as-is
      const scan = req.body as ScanRequestBody;
      console.log("[scans:POST] Request body received:", JSON.stringify({
        asset_type: scan.asset_type,
        targetDetails: scan.targetDetails,
        conversationId: scan.conversationId,
        fileCount: scan.files?.length || 0,
        // Include a few key fields for debugging
        function: scan.function,
        contact_name: scan.contact_name,
        contact_email: scan.contact_email
      }, null, 2));

      // Extract conversationId if provided
      const conversationId = scan.conversationId;
      console.log("[scans:POST] Conversation ID:", conversationId || "None");

      // Validate asset_type and at least one identifying field
      if (!scan.asset_type) {
        console.error("[scans:POST] Missing asset_type field in request");
        return res.status(400).json({ error: "Missing asset_type field" });
      }

      console.log("[scans:POST] Asset type:", scan.asset_type);

      // Optionally: check at least one key field for each asset type
      // For demo, just require asset_type and at least one non-empty field besides asset_type
      const keys = Object.keys(scan).filter((k) => k !== "asset_type" && k !== "conversationId");
      console.log("[scans:POST] Available fields:", keys);

      const hasData = keys.some(
        (k) => scan[k] && String(scan[k]).trim() !== ""
      );

      console.log("[scans:POST] Has required data fields:", hasData);

      if (!hasData) {
        console.error("[scans:POST] Missing required scan details - no non-empty fields besides asset_type");
        return res.status(400).json({ error: "Missing required scan details" });
      }

      // Attach scan_id, status, etc., and the userId and userEmail
      console.log(`[scans:POST] Creating scan for user: ${userId} (${userEmail}), role: ${decodedToken.role || 'client'}`);

      // If there's a conversation ID, try to get the conversation title
      let conversationTitle = null;
      if (conversationId) {
        try {
          console.log(`[scans:POST] Fetching conversation title for conversation ID: ${conversationId}`);
          const conversationDoc = await db.collection("conversations").doc(conversationId).get();
          if (conversationDoc.exists) {
            const conversationData = conversationDoc.data();
            conversationTitle = conversationData?.title || null;
            console.log(`[scans:POST] Found conversation title: "${conversationTitle}"`);

            // Check if it's a default title
            if (conversationTitle === "Pentest Scan Request" || conversationTitle === "Pentest Request") {
              console.log(`[scans:POST] Warning: Using default conversation title. This may not display correctly in scan cards.`);
            }
          } else {
            console.log(`[scans:POST] Conversation not found for ID: ${conversationId}`);
          }
        } catch (error) {
          console.error(`[scans:POST] Error fetching conversation title:`, error);
          // Continue without the conversation title if there's an error
        }
      }

      const newScan: any = {
        scan_id: `scan_${Date.now()}`,
        ...scan,
        status: "pending",
        requestedAt: new Date().toISOString(),
        userId: userId, // Attach the authenticated user's ID
        userEmail: userEmail, // Attach the authenticated user's email
        userDisplayName: decodedToken.name || null, // Attach the authenticated user's display name
        // Store the conversation ID if provided
        conversationId: conversationId || null,
        // Store the conversation title if available
        conversationTitle: conversationTitle,
        // Demo: default vuln counts
        criticalVulnerabilities: 0,
        highVulnerabilities: 0,
        mediumVulnerabilities: 0,
        lowVulnerabilities: 0,
      };

      console.log("[scans:POST] Prepared new scan object with defaults");

      // Check if this user is part of a team
      try {
        console.log("[scans:POST] Checking if user is part of a team");
        // Look for teams where this user's email is in teamMemberEmails
        const teamQuery = db.collection("teams").where("teamMemberEmails", "array-contains", userEmail);
        console.log("[scans:POST] Executing team query for email:", userEmail);
        const teamSnapshot = await teamQuery.get();
        console.log("[scans:POST] Team query results:", teamSnapshot.size, "teams found");

        if (!teamSnapshot.empty) {
          // User is part of a team
          const teamDoc = teamSnapshot.docs[0];
          const teamData = teamDoc.data();

          console.log(`[scans:POST] User ${userEmail} is part of team ${teamDoc.id}`);
          console.log("[scans:POST] Team data:", JSON.stringify({
            teamId: teamDoc.id,
            managerId: teamData.managerId,
            managerEmail: teamData.managerEmail,
            memberCount: teamData.teamMemberEmails?.length || 0
          }, null, 2));

          // Add team information to the scan
          newScan.teamId = teamDoc.id;
          newScan.managerId = teamData.managerId;
          newScan.managerEmail = teamData.managerEmail;
          console.log("[scans:POST] Added team information to scan");
        } else {
          console.log(`[scans:POST] User ${userEmail} is not part of any team`);
        }
      } catch (error) {
        console.error("[scans:POST] Error checking team membership:", error);
        // Continue without team info if there's an error
      }

      // Save the new scan to Firestore
      console.log("[scans:POST] Saving scan to Firestore...");
      try {
        const docRef = await db.collection("scans").add(newScan);
        console.log("[scans:POST] Scan saved successfully with ID:", docRef.id);

        // Add the document ID to the scan data
        const scanWithId = {
          ...newScan,
          id: docRef.id,
          // Keep scan_id for backward compatibility but ensure id is the Firestore document ID
          scan_id: newScan.scan_id
        };

        // Also update the document with its own ID to ensure consistency
        console.log("[scans:POST] Updating document with its own ID");
        await docRef.update({ id: docRef.id });
        console.log("[scans:POST] Document updated successfully");

        // Invalidate all relevant caches for this user and organization members
        try {
          // Clear all cache variations for the current user
          memoryCache.clearByPrefix(`scans:${userId}`);
          memoryCache.delete(`org-scans:${userId}`);
          
          const userOrg = await getUserOrganization(userId);
          if (userOrg) {
            console.log(`[scans:POST] User ${userId} belongs to organization ${userOrg}. Invalidating organization cache.`);
            const orgMembers = await getOrganizationMembers(userOrg);
            for (const member of orgMembers) {
              // Clear all cache variations for each organization member
              memoryCache.clearByPrefix(`scans:${member.userId}`);
              memoryCache.delete(`org-scans:${member.userId}`);
            }
            console.log(`[scans:POST] Invalidated all caches for ${orgMembers.length} organization members.`);
          } else {
            console.log(`[scans:POST] User ${userId} does not belong to an organization.`);
          }
        } catch (orgCacheError) {
          console.error("[scans:POST] Error invalidating caches:", orgCacheError);
        }

        // Emit WebSocket event for scan creation
        try {
          const io = getSocketIO(res);
          if (io) {
            console.log("[scans:POST] Emitting WebSocket event for scan creation");
            emitScanCreated(io, scanWithId);
          } else {
            console.log("[scans:POST] WebSocket IO not available, skipping event emission");
          }
        } catch (socketError) {
          console.error("[scans:POST] Error with WebSocket functionality:", socketError);
          // Continue without WebSocket functionality
        }

        // Send email notification for new scan
        try {
          console.log("[scans:POST] Sending email notification for new scan");
          await sendNotificationEmail({
            scanId: scanWithId.id,
            oldStatus: null,
            newStatus: scanWithId.status,
            scanDetails: {
              target: scanWithId.target || scanWithId.targetDetails || 'Unknown',
              asset_type: scanWithId.asset_type || 'unknown'
            },
            triggeringUserId: userId,
          });
        } catch (emailError) {
          console.error("[scans:POST] Error sending email notification:", emailError);
        }

        console.log("[scans:POST] Sending successful response");
        res.status(201).json({
          message: "Scan request received successfully",
          scanId: docRef.id, // Use the Firestore generated ID
          receivedData: scanWithId, // Use the data with the correct ID
        });
      } catch (firestoreError) {
        console.error("[scans:POST] Error saving scan to Firestore:", firestoreError);
        throw firestoreError; // Re-throw to be caught by the outer catch block
      }
    } catch (error: any) {
      console.error("[scans:POST] Error processing scan request:", error);
      console.error("[scans:POST] Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });

      // Provide more detailed error information
      res
        .status(500)
        .json({
          error: "Internal Server Error processing scan request",
          details: error.message,
          code: error.code || "unknown_error"
        });
    }
  } else if (req.method === "GET") {
    try {
      const { role, includeTeam, includeOrganization } = req.query;

      // Generate a cache key based on the request parameters
      const cacheKey = `scans:${userId}:${role}:${includeTeam}:${includeOrganization}`;

      // Check for cache busting parameter
      const cacheBuster = req.query._ || '';
      // Force refresh if cache buster is present or if the 'force' query parameter is set
      const forceRefresh = cacheBuster !== '' || req.query.force === 'true';

      // Clear the cache if requested
      if (forceRefresh) {
        console.log(`Forcing refresh for ${cacheKey}, clearing cache`);
        memoryCache.delete(cacheKey);
        memoryCache.delete(`${cacheKey}:lastModified`);
      }

      // Get the last modified timestamp for this data
      const lastModifiedKey = `${cacheKey}:lastModified`;
      let lastModified = memoryCache.get(lastModifiedKey) || new Date().toUTCString();
      // Ensure lastModified is a string
      if (typeof lastModified !== 'string') {
        lastModified = new Date().toUTCString();
      }

      // Check if we have a cached response and not forcing refresh
      const cachedData = !forceRefresh ? memoryCache.get(cacheKey) : null;
      if (cachedData) {
        // Add cache control headers with a longer max-age
        res.setHeader('Cache-Control', 'private, max-age=600');
        res.setHeader('ETag', `"${cacheKey}:${cacheBuster || 'default'}"`);
        // Ensure lastModified is a string before setting the header
        res.setHeader('Last-Modified', lastModified as string);

        // Check if the client sent an If-None-Match header
        const ifNoneMatch = req.headers['if-none-match'];
        if (ifNoneMatch && ifNoneMatch.includes(cacheKey)) {
          // Client already has the latest data, return 304
          console.log(`Client has latest data for ${cacheKey}, returning 304`);
          return res.status(304).end();
        }

        // Check if the client sent an If-Modified-Since header
        const ifModifiedSince = req.headers['if-modified-since'];
        if (ifModifiedSince && new Date(ifModifiedSince).toUTCString() === lastModified) {
          // Client already has the latest data, return 304
          console.log(`Client has latest data for ${cacheKey} (by date), returning 304`);
          return res.status(304).end();
        }

        console.log(`Using cached scan data for ${cacheKey}`);
        return res.status(200).json({ result: cachedData });
      }

      // If forcing refresh, log it
      if (forceRefresh) {
        console.log(`Force refreshing data for ${cacheKey}`);
      }

      console.log(`Fetching fresh scan data for ${cacheKey}`);

      let query: any = db.collection("scans");

      // Different query logic based on role
      console.log(`Fetching scans for user: ${userId}, role: ${role}`);

      // If role is not specified or invalid, default to client behavior
      const userRole = (role === "admin" || role === "manager" || role === "client") ? role : "client";
      console.log(`Using role: ${userRole} for query filtering`);

      if (userRole === "client") {
        // For clients, check if organization-wide access is requested
        const { includeOrganization } = req.query;
        
        console.log(`[DEBUG] Client user ${userId} (${userEmail}) - includeOrganization: ${includeOrganization}`);
        
        if (includeOrganization === "true") {
          console.log(`[DEBUG] Client user ${userId} requesting organization-wide data`);
          
          try {
            // Try to get cached organization data first for instant response
            const cachedOrgData = scanOptimizer.get(`org-scans:${userId}`, 10 * 60 * 1000); // 10 min cache
            if (cachedOrgData && !forceRefresh) {
              console.log('Using cached organization data for instant switch');
              res.setHeader('Cache-Control', 'private, max-age=600');
              res.setHeader('ETag', `"${cacheKey}:cached"`);
              return res.status(200).json({ result: cachedOrgData });
            }

            // Validate organization access
            const accessValidation = await validateOrganizationAccess(userId, userEmail);
            
            if (!accessValidation.isValid) {
              console.log(`Organization access denied: ${accessValidation.errorMessage}`);
              await logOrganizationAccess(
                userId,
                userEmail,
                'VIEW_ORGANIZATION_SCANS',
                'scans',
                undefined,
                accessValidation.userOrganization || undefined,
                false,
                accessValidation.errorMessage
              );
              // Fall back to user's own scans
              query = query.where("userId", "==", userId);
            } else {
              const userOrganization = accessValidation.userOrganization!;
              
              // Check rate limiting
              const rateLimit = await checkOrganizationRateLimit(userId, 'VIEW_ORGANIZATION_SCANS');
              
              if (!rateLimit.isAllowed) {
                console.log(`Rate limit exceeded for user ${userId}`);
                await logOrganizationAccess(
                  userId,
                  userEmail,
                  'VIEW_ORGANIZATION_SCANS',
                  'scans',
                  undefined,
                  userOrganization,
                  false,
                  'Rate limit exceeded'
                );
                return res.status(429).json({
                  error: "Rate limit exceeded",
                  resetTime: rateLimit.resetTime
                });
              }
              
              console.log(`User belongs to organization: ${userOrganization}`);
              
              // Optimized organization scan fetching with parallel queries
              const orgMembers = await getOrganizationMembers(userOrganization); // Fetch members first
              const orgScanIds = await getOrganizationScanIds(orgMembers.map(member => member.userId)); // Then fetch scan IDs
              
              if (orgScanIds.length > 0) {
                console.log(`Found ${orgScanIds.length} scans for organization`);
                
                // Optimized batch fetching with larger batch size and parallel processing
                const batchSize = 25; // Increased from 10 for better performance
                const allOrgScans: any[] = [];
                const batchPromises: Promise<any[]>[] = [];
                
                for (let i = 0; i < orgScanIds.length; i += batchSize) {
                  const batch = orgScanIds.slice(i, i + batchSize);
                  batchPromises.push(
                    Promise.all(batch.map(scanId =>
                      db.collection("scans").doc(scanId).get()
                    )).then(batchResults =>
                      batchResults
                        .filter(doc => doc.exists)
                        .map(doc => ({ id: doc.id, ...doc.data() }))
                    )
                  );
                }
                
                // Execute all batches in parallel
                const batchResults = await Promise.all(batchPromises);
                batchResults.forEach(batch => allOrgScans.push(...batch));
                
                // Sort by requestedAt date (newest first) - more efficient sorting
                allOrgScans.sort((a, b) => {
                  const dateA = a.requestedAt ? new Date(a.requestedAt).getTime() : 0;
                  const dateB = b.requestedAt ? new Date(b.requestedAt).getTime() : 0;
                  return dateB - dateA;
                });
                
                // Group scans by user for organization view
                const organizationData = sanitizeOrganizationData({
                  organization: userOrganization,
                  members: orgMembers,
                  scans: allOrgScans,
                  totalScans: allOrgScans.length
                });
                
                // Log successful access
                await logOrganizationAccess(
                  userId,
                  userEmail,
                  'VIEW_ORGANIZATION_SCANS',
                  'scans',
                  undefined,
                  userOrganization,
                  true
                );
                
                // Enhanced caching with longer TTL
                const responseData = { organizationData, scans: allOrgScans };
                const now = new Date().toUTCString();
                
                // Use the performance optimizer for better caching
                scanOptimizer.set(`org-scans:${userId}`, responseData);
                
                // Also update the old cache for backward compatibility
                memoryCache.set(`${cacheKey}:lastModified`, now, 3600);
                memoryCache.set(cacheKey, responseData, 1200); // Increased to 20 minutes
                
                // Set cache headers with longer max-age
                res.setHeader('Cache-Control', 'private, max-age=1200');
                res.setHeader('ETag', `"${cacheKey}:${cacheBuster || 'default'}"`);
                res.setHeader('Last-Modified', now);
                
                console.log(`Returning organization-wide scans: ${allOrgScans.length} scans`);
                return res.status(200).json({ result: responseData });
              } else {
                console.log(`No scans found for organization: ${userOrganization}`);
                await logOrganizationAccess(
                  userId,
                  userEmail,
                  'VIEW_ORGANIZATION_SCANS',
                  'scans',
                  undefined,
                  userOrganization,
                  true,
                  'No scans found for organization'
                );
              }
            }
          } catch (orgError) {
            console.error("Error fetching organization data:", orgError);
            // Fall back to user's own scans
          }
        }
        
        // Default behavior: clients only see their own scans
        console.log(`Client user ${userId} - filtering scans by userId`);
        query = query.where("userId", "==", userId);
      } else if (userRole === "manager") {
        console.log(`Processing scans request for manager ${userId}, includeTeam=${includeTeam}`);

        // Managers need to see scans from their team members
        // First, get the team members for this manager
        const teamRef = db.collection("teams").where("managerId", "==", userId);
        const teamSnapshot = await teamRef.get();

        if (!teamSnapshot.empty) {
          // Get the first team document (assuming a manager has one team)
          const teamDoc = teamSnapshot.docs[0];
          const teamData = teamDoc.data();
          const teamMembers = teamData.teamMembers || [];
          const teamMemberEmails = teamData.teamMemberEmails || [];

          console.log("Found team for manager:", teamDoc.id);
          console.log("Team members:", teamMembers);
          console.log("Team member emails:", teamMemberEmails);

          // If there are team members with IDs, include the manager's scans and team members' scans
          if (teamMembers.length > 0) {
            console.log("Using 'in' query with team member IDs");
            // We need to use "in" query which requires an array of values
            const userIds = [userId, ...teamMembers];
            query = query.where("userId", "in", userIds);
          } else if (teamMemberEmails.length > 0 && includeTeam === "true") {
            console.log("Using optimized query with team member emails");

            // First, get the Firebase UIDs for all team members by email
            // This is a one-time operation that will be much faster than looking up each scan
            const teamMemberUids: string[] = [];
            const emailToUidMap: Record<string, string> = {};

            // Process team member emails in batches to avoid rate limits
            const batchSize = 10;
            for (let i = 0; i < teamMemberEmails.length; i += batchSize) {
              const emailBatch = teamMemberEmails.slice(i, i + batchSize);

              // Process each email in parallel
              const userRecords = await Promise.all(
                emailBatch.map(async (email: string) => {
                  try {
                    return await auth.getUserByEmail(email);
                  } catch (error) {
                    console.log(`No Firebase user found for email: ${email}`);
                    return null;
                  }
                })
              );

              // Add valid UIDs to our list
              userRecords.forEach((record, index) => {
                if (record && record.uid) {
                  teamMemberUids.push(record.uid);
                  emailToUidMap[emailBatch[index]] = record.uid;
                }
              });
            }

            console.log(`Found ${teamMemberUids.length} Firebase UIDs for team members`);

            // Add the manager's ID to the list of UIDs to query
            const allUids = [userId, ...teamMemberUids];

            // Use Firestore's "in" query to get all scans for these UIDs
            // This is much more efficient than fetching all scans and filtering
            let scansQuery = db.collection("scans").where("userId", "in", allUids);

            // Execute the query
            let scansSnapshot = await scansQuery.get();
            let scans: ScanType[] = scansSnapshot.docs.map((doc) => {
              const data = doc.data();
              return {
                id: doc.id,
                ...data,
              };
            });

            console.log(`Found ${scans.length} scans with matching UIDs`);

            // Now get scans that might only have email but no UID
            // We'll use a more targeted query instead of getting ALL scans
            const emailQueries = [];

            // Process in batches of 10 to avoid hitting Firestore limits
            for (let i = 0; i < teamMemberEmails.length; i += 10) {
              const emailBatch = teamMemberEmails.slice(i, i + 10);
              emailQueries.push(
                db.collection("scans")
                  .where("userEmail", "in", emailBatch)
                  .get()
              );
            }

            // Execute all email queries in parallel
            const emailQueryResults = await Promise.all(emailQueries);

            // Process the results
            const emailScans: ScanType[] = [];
            const scansToUpdate: { id: string; updates: any }[] = [];

            emailQueryResults.forEach((snapshot) => {
              snapshot.docs.forEach((doc) => {
                const data = doc.data();
                const scan: ScanType = {
                  id: doc.id,
                  ...data,
                };

                // Skip if we already have this scan
                if (scans.some(s => s.id === scan.id)) {
                  return;
                }

                // Add to our results
                emailScans.push(scan);

                // If we have a UID for this email but the scan doesn't have it,
                // queue an update to add the UID
                if (scan.userEmail && emailToUidMap[scan.userEmail] &&
                    (!scan.userId || scan.userId !== emailToUidMap[scan.userEmail])) {
                  scansToUpdate.push({
                    id: scan.id,
                    updates: { userId: emailToUidMap[scan.userEmail] }
                  });
                }
              });
            });

            console.log(`Found ${emailScans.length} additional scans with matching emails`);

            // Combine all scans
            scans = [...scans, ...emailScans];

            // Update scans that need UIDs (in batch for efficiency)
            if (scansToUpdate.length > 0) {
              console.log(`Updating ${scansToUpdate.length} scans with missing UIDs`);

              // Process in batches of 500 (Firestore batch limit)
              for (let i = 0; i < scansToUpdate.length; i += 500) {
                const batch = db.batch();
                const updateBatch = scansToUpdate.slice(i, i + 500);

                updateBatch.forEach(({ id, updates }) => {
                  batch.update(db.collection("scans").doc(id), updates);
                });

                await batch.commit();
              }
            }

            // Organize scans by team member
            const result = scans;
            const teamMemberScans: Record<string, ScanType[]> = {};

            // Group by user ID
            scans.forEach(scan => {
              // Add to manager's scans
              if (scan.userId === userId) {
                if (!teamMemberScans[userId]) {
                  teamMemberScans[userId] = [];
                }
                teamMemberScans[userId].push(scan);
              }

              // Add to team member's scans by ID
              if (scan.userId && teamMemberUids.includes(scan.userId)) {
                if (!teamMemberScans[scan.userId]) {
                  teamMemberScans[scan.userId] = [];
                }
                teamMemberScans[scan.userId].push(scan);
              }

              // Also add by email for backward compatibility
              if (scan.userEmail) {
                const emailKey = `email-${scan.userEmail}`;
                if (!teamMemberScans[emailKey]) {
                  teamMemberScans[emailKey] = [];
                }
                teamMemberScans[emailKey].push(scan);
              }
            });

            // Sort scans by requestedAt date (newest first)
            result.sort((a, b) => {
              const dateA = new Date(a.requestedAt || 0);
              const dateB = new Date(b.requestedAt || 0);
              return dateB.getTime() - dateA.getTime();
            });

            console.log(`Total scans found for team: ${result.length}`);
            console.log(`Team member scans by key:`, Object.keys(teamMemberScans).map(key => ({
              key,
              count: teamMemberScans[key].length
            })));

            // Cache the results for 10 minutes
            const responseData = { scans: result, teamMemberScans };

            // Update the last modified timestamp
            const now = new Date().toUTCString();
            memoryCache.set(`${cacheKey}:lastModified`, now, 3600); // Cache timestamp for 1 hour

            // Store the result in cache for future requests
            memoryCache.set(cacheKey, responseData, 600); // Cache for 10 minutes

            // Set cache headers in the response
            res.setHeader('Cache-Control', 'private, max-age=600');
            res.setHeader('ETag', `"${cacheKey}:${cacheBuster || 'default'}"`);
            res.setHeader('Last-Modified', now);

            // Return the filtered results directly
            console.log(`Returning team scans response with ${result.length} scans and ${Object.keys(teamMemberScans).length} team member entries`);
            return res.status(200).json({ result: responseData });
          } else {
            // If no team members or includeTeam is false, just show manager's own scans
            query = query.where("userId", "==", userId);
          }
        } else {
          // If no team found, just show manager's own scans
          query = query.where("userId", "==", userId);
        }
      }
      // For admin role, we need to group scans by user
      if (userRole === "admin") {
        console.log(`Processing scans request for admin user ${userId}`);
        // Execute the query to get all scans
        console.log('Executing query to get all scans for admin view');
        const snapshot = await query.orderBy("requestedAt", "desc").get();
        console.log(`Query returned ${snapshot.size} scans`);

        // Format the results
        const allScans = snapshot.docs.map((doc: QueryDocumentSnapshot) => ({
          id: doc.id,
          ...doc.data(),
        }));

        // Group scans by userId
        const userScansMap: Record<string, ScanType[]> = {};
        const userInfoMap: Record<string, { email: string; displayName?: string }> = {};

        // First pass: group scans by userId and collect unique userIds
        for (const scan of allScans) {
          if (scan.userId) {
            if (!userScansMap[scan.userId]) {
              userScansMap[scan.userId] = [];
              // Initialize user info with what we have from the scan
              userInfoMap[scan.userId] = {
                email: scan.userEmail || 'Unknown',
                displayName: scan.userDisplayName || undefined
              };
            }
            userScansMap[scan.userId].push(scan);
          } else {
            // Handle scans without userId (should be rare)
            if (!userScansMap['Unknown']) {
              userScansMap['Unknown'] = [];
              userInfoMap['Unknown'] = { email: 'Unknown User', displayName: 'Unknown User' };
            }
            userScansMap['Unknown'].push(scan);
          }
        }

        // Second pass: fetch additional user info for each userId
        const userIds = Object.keys(userScansMap).filter(id => id !== 'Unknown');

        // Fetch user details in batches to avoid hitting Firebase limits
        const batchSize = 100;
        for (let i = 0; i < userIds.length; i += batchSize) {
          const batch = userIds.slice(i, i + batchSize);

          // Get user records for this batch
          try {
            const userRecords = await Promise.all(
              batch.map(async (userId) => {
                try {
                  return await auth.getUser(userId);
                } catch (error) {
                  console.error(`Error fetching user ${userId}:`, error);
                  return null;
                }
              })
            );

            // Update user info map with fetched data
            userRecords.forEach((record, index) => {
              if (record) {
                const userId = batch[index];
                userInfoMap[userId] = {
                  email: record.email || userInfoMap[userId].email,
                  displayName: record.displayName || userInfoMap[userId].displayName
                };
              }
            });
          } catch (error) {
            console.error('Error fetching user batch:', error);
          }
        }

        // Third pass: fetch organization information from Firestore for each user
        const userOrganizationMap: Record<string, string> = {};

        try {
          // Get user documents from Firestore in batches
          for (let i = 0; i < userIds.length; i += batchSize) {
            const batch = userIds.slice(i, i + batchSize);

            const userDocs = await Promise.all(
              batch.map(async (userId) => {
                try {
                  const userDoc = await db.collection('users').doc(userId).get();
                  return { userId, data: userDoc.exists ? userDoc.data() : null };
                } catch (error) {
                  console.error(`Error fetching user document ${userId}:`, error);
                  return { userId, data: null };
                }
              })
            );

            // Extract organization from user documents or email
            userDocs.forEach(({ userId, data }) => {
              console.log(`Processing user ${userId} with data:`, data);

              if (data && data.organization) {
                // Use organization from Firestore if available
                console.log(`User ${userId} has organization in Firestore: ${data.organization}`);
                userOrganizationMap[userId] = data.organization;
              } else if (userInfoMap[userId] && userInfoMap[userId].email) {
                // Extract organization from email as fallback
                const email = userInfoMap[userId].email;
                console.log(`User ${userId} has email: ${email}`);
                const organization = extractOrganizationFromEmail(email);
                if (organization) {
                  console.log(`Extracted organization from email: ${organization}`);
                  userOrganizationMap[userId] = organization;
                } else {
                  console.log(`Could not extract organization from email: ${email}`);
                  userOrganizationMap[userId] = 'Unknown';
                }
              } else {
                console.log(`User ${userId} has no organization or email`);
                userOrganizationMap[userId] = 'Unknown';
              }
            });
          }
        } catch (error) {
          console.error('Error fetching user organizations:', error);
        }

        // Group users by organization
        const organizationUsersMap: Record<string, any[]> = {};

        console.log('Final userOrganizationMap:', userOrganizationMap);

        Object.keys(userScansMap).forEach(userId => {
          const organization = userOrganizationMap[userId] || 'Unknown';
          console.log(`Grouping user ${userId} under organization: ${organization}`);

          if (!organizationUsersMap[organization]) {
            organizationUsersMap[organization] = [];
          }

          organizationUsersMap[organization].push({
            userId,
            email: userInfoMap[userId].email,
            displayName: userInfoMap[userId].displayName,
            organization, // Explicitly include organization in the user object
            scans: userScansMap[userId]
          });
        });

        // Prepare the result with organization grouping
        const result = {
          groupedByUser: true,
          groupedByOrganization: true,
          organizations: Object.keys(organizationUsersMap).map(orgName => {
            console.log(`Creating organization group for: ${orgName}`);
            return {
              name: orgName,
              users: organizationUsersMap[orgName]
            };
          }),
          // Keep the flat users array for backward compatibility
          users: Object.keys(userScansMap).map(userId => {
            const organization = userOrganizationMap[userId] || 'Unknown';
            console.log(`Adding user ${userId} to flat list with organization: ${organization}`);
            return {
              userId,
              email: userInfoMap[userId].email,
              displayName: userInfoMap[userId].displayName,
              organization,
              scans: userScansMap[userId]
            };
          })
        };

        // Update the last modified timestamp
        const now = new Date().toUTCString();
        memoryCache.set(`${cacheKey}:lastModified`, now, 3600); // Cache timestamp for 1 hour

        // Cache the results for 10 minutes
        memoryCache.set(cacheKey, result, 600);

        // Set cache headers in the response
        res.setHeader('Cache-Control', 'private, max-age=600');
        res.setHeader('ETag', `"${cacheKey}:${cacheBuster || 'default'}"`);
        res.setHeader('Last-Modified', now);

        // Return the grouped results
        res.status(200).json({ result });
      } else {
        // For non-admin roles, continue with the existing logic
        // Execute the query
        const snapshot = await query.orderBy("requestedAt", "desc").get();

        // Format the results
        const result = snapshot.docs.map((doc: QueryDocumentSnapshot) => ({
          id: doc.id,
          ...doc.data(),
        }));

        // Update the last modified timestamp
        const now = new Date().toUTCString();
        memoryCache.set(`${cacheKey}:lastModified`, now, 3600); // Cache timestamp for 1 hour

        // Cache the results for 10 minutes
        memoryCache.set(cacheKey, result, 600);

        // Set cache headers in the response
        res.setHeader('Cache-Control', 'private, max-age=600');
        res.setHeader('ETag', `"${cacheKey}:${cacheBuster || 'default'}"`);
        res.setHeader('Last-Modified', now);

        // Return the results
        res.status(200).json({ result });
      }
    } catch (error: any) {
      console.error("Error fetching scans:", error);
      res.status(500).json({ error: "Failed to fetch scans from Firestore" });
    }
  } else if (req.method === "PUT") {
    try {
      const {
        scanId,
        status,
        resultFileUrl,
        criticalVulnerabilities,
        highVulnerabilities,
        mediumVulnerabilities,
        lowVulnerabilities,
        progress
      } = req.body;

      // Check if scanId exists and at least one update field is provided
      if (!scanId || (!status && !resultFileUrl &&
          criticalVulnerabilities === undefined &&
          highVulnerabilities === undefined &&
          mediumVulnerabilities === undefined &&
          lowVulnerabilities === undefined &&
          progress === undefined)) {
        return res.status(400).json({
          error: "Missing scanId and at least one field to update",
        });
      }

      const scanRef = db.collection("scans").doc(scanId);
      const doc = await scanRef.get();

      if (!doc.exists) {
        return res.status(404).json({ error: "Scan not found" });
      }

      const scanData = doc.data() || {}; // Get the current scan data
      const oldStatus = scanData.status || 'pending';
      const oldProgress = scanData.progress || 0; // Get old progress

      const updateData: any = {};

      // Add fields to update data if they exist in the request
      if (status) {
        updateData.status = status;

        // Set completedAt timestamp when status is changed to "completed"
        if (status === "completed") {
          updateData.completedAt = new Date().toISOString();
        }
      }
      if (resultFileUrl) {
        updateData.resultFileUrl = resultFileUrl;
      }

      // Handle vulnerability counts
      if (criticalVulnerabilities !== undefined) {
        updateData.criticalVulnerabilities = criticalVulnerabilities;
      }
      if (highVulnerabilities !== undefined) {
        updateData.highVulnerabilities = highVulnerabilities;
      }
      if (mediumVulnerabilities !== undefined) {
        updateData.mediumVulnerabilities = mediumVulnerabilities;
      }
      if (lowVulnerabilities !== undefined) {
        updateData.lowVulnerabilities = lowVulnerabilities;
      }
      if (progress !== undefined) {
        updateData.progress = progress;
      }

      // Add timestamp for vulnerability updates if any vulnerability field is updated
      if (criticalVulnerabilities !== undefined ||
          highVulnerabilities !== undefined ||
          mediumVulnerabilities !== undefined ||
          lowVulnerabilities !== undefined) {
        updateData.vulnerabilitiesUpdatedAt = new Date().toISOString();
      }

      // Update the scan in Firestore
      await scanRef.update(updateData);

      // Clear any cached data for this user
      memoryCache.clearByPrefix(`scans:${userId}`);

      // If this is a team scan, also clear team cache
      if (scanData.userId && scanData.userId !== userId) {
        memoryCache.clearByPrefix(`scans:${scanData.userId}`);
      }

      // Clear cache for this specific scan
      memoryCache.delete(`scan:${scanId}:${userId}`);

      // Invalidate all relevant caches for this user and organization members
      try {
        // Clear all cache variations for the current user
        memoryCache.clearByPrefix(`scans:${userId}`);
        memoryCache.delete(`org-scans:${userId}`);
        
        const userOrg = await getUserOrganization(userId);
        if (userOrg) {
          console.log(`[scans:PUT] User ${userId} belongs to organization ${userOrg}. Invalidating organization cache.`);
          const orgMembers = await getOrganizationMembers(userOrg);
          for (const member of orgMembers) {
            // Clear all cache variations for each organization member
            memoryCache.clearByPrefix(`scans:${member.userId}`);
            memoryCache.delete(`org-scans:${member.userId}`);
          }
          console.log(`[scans:PUT] Invalidated all caches for ${orgMembers.length} organization members.`);
        } else {
          console.log(`[scans:PUT] User ${userId} does not belong to an organization.`);
        }
      } catch (orgCacheError) {
        console.error("[scans:PUT] Error invalidating caches:", orgCacheError);
      }

      // Get an admin token for the API calls within this block
      let adminToken: string | null = null;
      if (scanData.userId) {
        try {
          adminToken = await auth.createCustomToken(scanData.userId);
        } catch (tokenError) {
          console.error('Error creating admin token:', tokenError);
          // Continue without token if there's an error, subsequent API calls will likely fail
        }
      }

      // Check if this is a status change and if the scan is associated with a conversation
      const isStatusChange = status && status !== oldStatus;
      const isProgressChange = progress !== undefined && progress !== oldProgress; // Check for progress change
      const conversationId = scanData.conversationId;

      // If this is a status change and there's an associated conversation, add a status update message
      if (isStatusChange && conversationId && scanData.userId && adminToken) {
        try {
          // Call the API to add a status update message
          const statusUpdateResponse = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/conversations/${conversationId}/status-update`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`,
              },
              body: JSON.stringify({
                scanId,
                oldStatus,
                newStatus: status,
              }),
            }
          );

          if (!statusUpdateResponse.ok) {
            console.error('Failed to add status update message:', await statusUpdateResponse.text());
          }
        } catch (error) {
          console.error('Error adding status update message to conversation:', error);
          // Continue with the response even if this fails
        }

        // Send email notification for scan status change
        try {
          console.log("[scans:PUT] Sending email notification for scan status change");
          await sendNotificationEmail({
            scanId,
            oldStatus,
            newStatus: status,
            scanDetails: {
              target: scanData.targetDetails || 'N/A',
              asset_type: scanData.asset_type,
            },
            triggeringUserId: userId,
          });
        } catch (emailError) {
          console.error("[scans:PUT] Error sending status change email notification:", emailError);
        }
      }

      // Check for progress milestones and send email if crossed
      const progressMilestones = [25, 50, 75, 100];
      const currentProgress = progress !== undefined ? progress : oldProgress;
      const lastNotifiedProgressMilestone = scanData.lastNotifiedProgressMilestone || 0;

      for (const milestone of progressMilestones) {
        if (currentProgress >= milestone && oldProgress < milestone) {
          if (milestone > lastNotifiedProgressMilestone || (milestone === 100 && lastNotifiedProgressMilestone !== 100)) {
            try {
              console.log(`[scans:PUT] Sending email notification for progress milestone: ${milestone}%`);
              await sendNotificationEmail({
                scanId,
                progressUpdate: milestone,
                scanDetails: {
                  target: scanData.targetDetails || 'N/A',
                  asset_type: scanData.asset_type,
                },
                triggeringUserId: userId,
              });
              await scanRef.update({ lastNotifiedProgressMilestone: milestone });
            } catch (emailError) {
              console.error(`[scans:PUT] Error sending progress milestone email notification for ${milestone}%:`, emailError);
            }
          }
        }
      }


      // Get the updated scan data
      const updatedScanDoc = await scanRef.get();
      const updatedScanData = {
        id: scanId,
        ...updatedScanDoc.data()
      };

      // Emit WebSocket event for scan update
      try {
        const io = getSocketIO(res);
        if (io) {
          console.log("[scans:PUT] Emitting WebSocket event for scan update");
          emitScanUpdated(io, updatedScanData);
        } else {
          console.log("[scans:PUT] WebSocket IO not available, skipping event emission");
        }
      } catch (socketError) {
        console.error("[scans:PUT] Error with WebSocket functionality:", socketError);
        // Continue without WebSocket functionality
      }

      res.status(200).json({
        message: "Scan updated successfully",
        updatedFields: Object.keys(updateData)
      });
    } catch (error: any) {
      // Removed detailed error logging
      const errorMessage = error.message || "Unknown error";
      const errorCode = error.code || "unknown_error";

      res.status(500).json({
        error: "Failed to update scan in Firestore",
        details: errorMessage,
        code: errorCode
      });
    }
  } else if (req.method === "DELETE") {
    try {
      const { scanId } = req.query; // Get scanId from query parameters for DELETE

      if (!scanId || typeof scanId !== "string") {
        return res
          .status(400)
          .json({ error: "Missing or invalid scanId in query parameters" });
      }

      const scanRef = db.collection("scans").doc(scanId);
      const doc = await scanRef.get();

      if (!doc.exists) {
        return res.status(404).json({ error: "Scan not found" });
      }

      // Get the scan data to check for associated files
      const scanData = doc.data() || {};

      // Check if there are files associated with this scan
      if (scanData?.files && Array.isArray(scanData.files) && scanData.files.length > 0) {
        console.log(`Scan ${scanId} has ${scanData.files.length} associated files that will be deleted`);

        // Access the Firebase storage bucket
        const bucket = storage.bucket();

        // Delete each file from storage
        for (const file of scanData.files) {
          if (file.url && file.url.includes('scan-results')) {
            try {
              // Extract the path from the URL
              try {
                // Parse the URL to get just the pathname without query parameters
                const fileUrl = new URL(file.url);
                const pathname = fileUrl.pathname;

                // The pathname should contain the file path after the bucket name
                // Format: /deepscan-growthguard.firebasestorage.app/scan-results/...
                const pathParts = pathname.split('/');

                // Find the index of 'scan-results' in the path
                const scanResultsIndex = pathParts.findIndex(part => part === 'scan-results');

                if (scanResultsIndex !== -1) {
                  // Reconstruct the path starting from 'scan-results'
                  const filePath = pathParts.slice(scanResultsIndex).join('/');

                  console.log(`Attempting to delete file: ${filePath}`);
                  await bucket.file(filePath).delete();
                  console.log(`Successfully deleted file: ${filePath}`);
                } else {
                  console.log(`Could not find 'scan-results' in path: ${pathname}`);
                }
              } catch (parseError) {
                console.error(`Error parsing URL ${file.url}:`, parseError);

                // Fallback to the old method if URL parsing fails
                const urlPath = file.url.split('scan-results/')[1];
                if (urlPath) {
                  // Remove any query parameters
                  const cleanPath = urlPath.split('?')[0];
                  const fullPath = `scan-results/${cleanPath}`;

                  console.log(`Fallback: Attempting to delete file: ${fullPath}`);
                  await bucket.file(fullPath).delete();
                  console.log(`Fallback: Successfully deleted file: ${fullPath}`);
                }
              }
            } catch (deleteErr) {
              console.error(`Error deleting file ${file.url}:`, deleteErr);
              // Continue with other files even if one fails
            }
          }
        }
      } else {
        console.log(`Scan ${scanId} has no associated files to delete`);
      }

      // Delete associated vulnerabilities
      console.log(`[scans:DELETE] Checking for vulnerabilities associated with scan ${scanId}`);
      let vulnerabilitiesDeleted = 0;

      try {
        // Query for all vulnerabilities with this scanId
        const vulnerabilitiesQuery = db.collection("vulnerabilities").where("scanId", "==", scanId);
        const vulnerabilitiesSnapshot = await vulnerabilitiesQuery.get();

        if (!vulnerabilitiesSnapshot.empty) {
          console.log(`[scans:DELETE] Found ${vulnerabilitiesSnapshot.size} vulnerabilities to delete`);

          // Create a batch to delete all vulnerabilities
          const batch = db.batch();
          vulnerabilitiesSnapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
          });

          // Commit the batch
          await batch.commit();
          vulnerabilitiesDeleted = vulnerabilitiesSnapshot.size;
          console.log(`[scans:DELETE] Successfully deleted ${vulnerabilitiesDeleted} vulnerabilities`);
        } else {
          console.log("[scans:DELETE] No vulnerabilities found to delete");
        }
      } catch (vulnDeleteError) {
        console.error("[scans:DELETE] Error deleting vulnerabilities:", vulnDeleteError);
        // Continue with scan deletion even if vulnerability deletion fails
      }

      // Now delete the scan document
      await scanRef.delete();

      // Clear any cached data for this user
      memoryCache.clearByPrefix(`scans:${userId}`);

      // If this is a team scan, also clear team cache
      if (scanData.userId && scanData.userId !== userId) {
        memoryCache.clearByPrefix(`scans:${scanData.userId}`);
      }

      // Clear cache for this specific scan
      memoryCache.delete(`scan:${scanId}:${userId}`);

      // Invalidate all relevant caches for this user and organization members
      try {
        // Clear all cache variations for the current user
        memoryCache.clearByPrefix(`scans:${userId}`);
        memoryCache.delete(`org-scans:${userId}`);
        
        const userOrg = await getUserOrganization(userId);
        if (userOrg) {
          console.log(`[scans:DELETE] User ${userId} belongs to organization ${userOrg}. Invalidating organization cache.`);
          const orgMembers = await getOrganizationMembers(userOrg);
          for (const member of orgMembers) {
            // Clear all cache variations for each organization member
            memoryCache.clearByPrefix(`scans:${member.userId}`);
            memoryCache.delete(`org-scans:${member.userId}`);
          }
          console.log(`[scans:DELETE] Invalidated all caches for ${orgMembers.length} organization members.`);
        } else {
          console.log(`[scans:DELETE] User ${userId} does not belong to an organization.`);
        }
      } catch (orgCacheError) {
        console.error("[scans:DELETE] Error invalidating caches:", orgCacheError);
      }

      // Prepare scan data for the WebSocket event
      const deletedScanData = {
        id: scanId,
        userId: scanData.userId || userId,
        managerId: scanData.managerId || null,
      };

      // Emit WebSocket event for scan deletion
      try {
        const io = getSocketIO(res);
        if (io) {
          console.log("[scans:DELETE] Emitting WebSocket event for scan deletion");
          emitScanDeleted(io, deletedScanData);
        } else {
          console.log("[scans:DELETE] WebSocket IO not available, skipping event emission");
        }
      } catch (socketError) {
        console.error("[scans:DELETE] Error with WebSocket functionality:", socketError);
        // Continue without WebSocket functionality
      }

      res.status(200).json({
        message: "Scan deleted successfully",
        filesDeleted: scanData?.files?.length || 0,
        vulnerabilitiesDeleted
      });
    } catch (error: any) {
      console.error("Error deleting scan:", error);
      res.status(500).json({ error: "Failed to delete scan from Firestore" });
    }
  } else {
    res.setHeader("Allow", ["POST", "GET", "PUT", "DELETE"]);
    res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }
}
