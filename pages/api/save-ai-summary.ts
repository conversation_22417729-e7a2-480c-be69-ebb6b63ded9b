import type { NextApiRequest, NextApiResponse } from "next";
import { db, auth } from "@/lib/firebase-admin";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean } | { error: string }>
) {
  if (req.method !== "POST") {
    res.setHeader("Allow", ["POST"]);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  // Authenticate user
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized" });
  }
  const idToken = authHeader.split(" ")[1];

  let decodedToken;
  try {
    decodedToken = await auth.verifyIdToken(idToken);
  } catch (error) {
    console.error("Error verifying Firebase ID token:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }

  // Extract request data
  const { scanId, aiSummary } = req.body;

  if (!scanId || !aiSummary) {
    return res.status(400).json({ error: "Missing required fields: scanId and summary" });
  }

  try {
    // Get the scan document
    const scanRef = db.collection("scans").doc(scanId);
    const scanDoc = await scanRef.get();

    if (!scanDoc.exists) {
      return res.status(404).json({ error: "Scan not found" });
    }

    const scanData = scanDoc.data();

    // Check if the user has permission to update this scan
    // Admin users can update any scan, but regular users can only update their own
    const userEmail = decodedToken.email || "";

    // Get user's custom claims to check role
    const userRecord = await auth.getUser(decodedToken.uid);
    const customClaims = userRecord.customClaims || {};
    const isAdmin = customClaims.role === 'admin';

    console.log("User email:", userEmail);
    console.log("User role:", customClaims.role);
    console.log("Is admin:", isAdmin);
    console.log("Scan user ID:", scanData?.userId);
    console.log("Token user ID:", decodedToken.uid);

    if (!isAdmin && scanData?.userId !== decodedToken.uid) {
      return res.status(403).json({ error: "Unauthorized to update this scan" });
    }

    // Update the scan with the summary
    await scanRef.update({
      aiSummary: aiSummary
    });

    return res.status(200).json({ success: true });
  } catch (error: any) {
    console.error("Error saving summary:", error);
    return res.status(500).json({ error: error.message || "Failed to save summary" });
  }
}
