[build]
  command = "npm run build"
  publish = ".next"

[dev]
  command = "next dev"
  targetPort = 3000

# Force HTTPS for all requests
[[redirects]]
  from = "http://:hostname/*"
  to = "https://:hostname/:splat"
  status = 301
  force = true

# Add security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "origin-when-cross-origin"
    Strict-Transport-Security = "max-age=63072000; includeSubDomains; preload"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com https://challenges.cloudflare.com https://www.googletagmanager.com https://us.i.posthog.com; connect-src 'self' https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://firebasestorage.googleapis.com https://firebase.googleapis.com https://firebaseinstallations.googleapis.com https://www.google-analytics.com https://challenges.cloudflare.com https://us.i.posthog.com https://app.posthog.com https://sockjs-ap2.pusher.com wss://ws-ap2.pusher.com; img-src 'self' data: https://lh3.googleusercontent.com https://firebasestorage.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; frame-src 'self' https://challenges.cloudflare.com;"
