'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from './useAuth';
import { initPusherConnection, getPusher, sendPusherMessage } from '@/lib/pusher-client';
import type { Channel } from 'pusher-js';

export const usePusher = () => {
  const { user, role } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [channels, setChannels] = useState<Map<string, Channel>>(new Map());

  // Initialize Pusher when user is authenticated
  useEffect(() => {
    if (user) {
      const pusher = initPusherConnection();
      
      pusher.connection.bind('connected', () => {
        console.log('Pusher connected');
        setIsConnected(true);
      });

      pusher.connection.bind('disconnected', () => {
        console.log('Pusher disconnected');
        setIsConnected(false);
      });

      pusher.connection.bind('error', (error: any) => {
        console.error('Pusher connection error:', error);
        setIsConnected(false);
      });

      return () => {
        pusher.disconnect();
        setChannels(new Map());
      };
    }
  }, [user]);

  // Subscribe to a channel
  const subscribe = useCallback((channelName: string, eventName: string, callback: (data: any) => void) => {
    const pusher = getPusher();
    if (!pusher) return () => {};

    let channel = channels.get(channelName);
    if (!channel) {
      channel = pusher.subscribe(channelName);
      setChannels(prev => new Map(prev).set(channelName, channel!));
    }

    channel.bind(eventName, callback);

    // Return unsubscribe function
    return () => {
      channel?.unbind(eventName, callback);
    };
  }, [channels]);

  // Send message via Pusher
  const emit = useCallback(async (channel: string, event: string, data: any) => {
    try {
      await sendPusherMessage(channel, event, data);
    } catch (error) {
      console.error('Failed to emit message:', error);
    }
  }, []);

  return {
    isConnected,
    subscribe,
    emit,
    isServerless: false // Pusher works in serverless environments
  };
};