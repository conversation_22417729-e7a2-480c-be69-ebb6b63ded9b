'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
// Import from the client-side socket configuration
import {
  Socket,
  initSocketConnection,
  getSocket,
  isInServerlessEnvironment
} from '@/lib/socket-client';

// Debug mode flag - set to false to reduce console logging
const DEBUG_SOCKET = false;

// Create a singleton socket instance outside of the hook
let globalSocket: Socket | null = null;
// Create a map to store event callbacks
const eventCallbacks = new Map<string, Set<(data: any) => void>>();
// Track connection status
let isGlobalConnected = false;
// Track if socket is initialized
let isInitialized = false;
// Track connection listeners
const connectionListeners: Set<(connected: boolean) => void> = new Set();
// Track joined rooms to prevent duplicate joins
const joinedRooms = new Set<string>();
// Track active intervals for cleanup
const activeIntervals = new Set<NodeJS.Timeout>();

// Helper function for logging
const logDebug = (message: string, ...args: any[]) => {
  if (DEBUG_SOCKET) {
    console.log(message, ...args);
  }
};

// Socket initialization now happens inside the useSocket hook
// after user authentication is complete

// Function to subscribe to an event with proper cleanup
const subscribeToEvent = (event: string, callback: (data: any) => void): (() => void) => {
  if (!globalSocket) return () => {};

  // Get or create the set of callbacks for this event
  if (!eventCallbacks.has(event)) {
    eventCallbacks.set(event, new Set());

    // Set up the event listener on the socket
    globalSocket.on(event, (data: any) => {
      // Call all callbacks for this event
      const callbacks = eventCallbacks.get(event);
      if (callbacks) {
        callbacks.forEach(cb => {
          try {
            cb(data);
          } catch (error) {
            console.error(`Error in event callback for ${event}:`, error);
          }
        });
      }
    });
  }

  // Add the callback to the set
  const callbacks = eventCallbacks.get(event);
  if (callbacks) {
    callbacks.add(callback);
    logDebug(`Subscribed to ${event} (${callbacks.size} listeners)`);
  }

  // Return a function to unsubscribe
  return () => {
    const callbacks = eventCallbacks.get(event);
    if (callbacks) {
      callbacks.delete(callback);
      logDebug(`Unsubscribed from ${event} (${callbacks.size} listeners remaining)`);

      // If no more callbacks for this event, remove the event listener
      if (callbacks.size === 0 && globalSocket) {
        globalSocket.off(event);
        eventCallbacks.delete(event);
        logDebug(`Removed all listeners for ${event}`);
      }
    }
  };
};

// Global cleanup function
const cleanupGlobalSocket = () => {
  if (globalSocket) {
    logDebug('Cleaning up global socket');
    
    // Remove all event listeners
    globalSocket.removeAllListeners();
    
    // Clear all callbacks
    eventCallbacks.clear();
    
    // Clear connection listeners
    connectionListeners.clear();
    
    // Clear joined rooms
    joinedRooms.clear();
    
    // Clear all active intervals
    activeIntervals.forEach(interval => clearInterval(interval));
    activeIntervals.clear();
    
    // Disconnect socket
    globalSocket.disconnect();
    globalSocket = null;
    
    // Reset flags
    isGlobalConnected = false;
    isInitialized = false;
  }
};

// Function to emit an event
const emitEvent = (event: string, data: any) => {
  if (globalSocket) {
    logDebug(`Emitted ${event}:`, data);
    globalSocket.emit(event, data);
  }
};

// We'll initialize the socket when the user is authenticated, not on module load
// This helps prevent the transport issues when refreshing the page
// The initialization will happen in the useSocket hook when user is available

// The hook itself
export const useSocket = () => {
  const { user, role, loading: authLoading } = useAuth();
  const [isConnected, setIsConnected] = useState(isGlobalConnected);
  const [initializationAttempted, setInitializationAttempted] = useState(false);
  const [isServerless, setIsServerless] = useState(false);
  const cleanupRef = useRef<(() => void) | null>(null);

  // Initialize socket when user is authenticated
  useEffect(() => {
    // Only initialize if we have a user and auth loading is complete
    if (user && !authLoading && !initializationAttempted) {
      logDebug(`Initializing socket with authenticated user: ${user.uid}`);
      setInitializationAttempted(true);

      // Initialize the socket
      const initializeSocket = async () => {
        if (isInitialized) return;

        try {
          // Check if we're in a serverless environment first
          const serverlessEnv = isInServerlessEnvironment();
          setIsServerless(serverlessEnv);

          if (serverlessEnv) {
            console.log('Running in serverless environment, WebSockets are not supported. Using polling fallback.');
            // In serverless environments, we'll use polling instead of WebSockets
            // Set up a polling interval to fetch messages periodically
            isInitialized = true;
            isGlobalConnected = false; // WebSockets are not connected in serverless
            return;
          }

          // Get existing socket or initialize a new one
          globalSocket = getSocket() || await initSocketConnection();
          isInitialized = true;

          // Set up event listeners
          globalSocket.on('connect', () => {
            logDebug(`Socket connected for user ${user.uid}`);
            isGlobalConnected = true;
            // Notify all listeners
            connectionListeners.forEach(listener => listener(true));
          });

          globalSocket.on('disconnect', (reason) => {
            logDebug(`Socket disconnected for user ${user.uid}: ${reason}`);
            isGlobalConnected = false;
            // Notify all listeners
            connectionListeners.forEach(listener => listener(false));
          });

          globalSocket.on('connect_error', (err) => {
            console.error(`Socket connection error for user ${user.uid}:`, err.message);
            isGlobalConnected = false;
            // Notify all listeners
            connectionListeners.forEach(listener => listener(false));
          });

          // Check if already connected
          if (globalSocket && globalSocket.connected) {
            isGlobalConnected = true;
          }
        } catch (error) {
          console.error(`Error initializing socket for user ${user.uid}:`, error);
          // Mark as not initialized so we can try again later
          isInitialized = false;
        }
      };

      initializeSocket();
    }
  }, [user, authLoading, initializationAttempted]);

  // Add this component as a connection listener
  useEffect(() => {
    const listener = (connected: boolean) => {
      setIsConnected(connected);
    };

    // Set initial state
    setIsConnected(isGlobalConnected);

    // Add listener
    connectionListeners.add(listener);

    // Clean up
    return () => {
      connectionListeners.delete(listener);
    };
  }, []);

  // Join room when user changes or socket connects
  useEffect(() => {
    if (globalSocket && isConnected && user?.uid) {
      // Track if we've already joined this room to avoid duplicate join messages
      const roomKey = `${user.uid}-${role || 'client'}`;

      if (!joinedRooms.has(roomKey)) {
        logDebug(`Joining room for user ${user.uid} with role ${role || 'client'}`);

        // Use the raw event name 'join_room' to match the server implementation
        globalSocket.emit('join_room', {
          userId: user.uid,
          role: role, // Use the role from useAuth hook
        });

        // Mark this room as joined
        joinedRooms.add(roomKey);
      } else {
        logDebug(`Already joined room for user ${user.uid}, skipping`);
      }
    }
  }, [user, role, isConnected]);

  // Create stable subscribe function
  const subscribe = useCallback((event: string, callback: (data: any) => void) => {
    return subscribeToEvent(event, callback);
  }, []);

  // Create stable emit function
  const emit = useCallback((event: string, data: any) => {
    emitEvent(event, data);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  // Setup cleanup function
  useEffect(() => {
    cleanupRef.current = () => {
      logDebug('Component unmounting, cleaning up socket resources');
      
      // Remove this component's connection listener
      const listener = (connected: boolean) => setIsConnected(connected);
      connectionListeners.delete(listener);
      
      // If this is the last component using the socket, clean up everything
      if (connectionListeners.size === 0) {
        cleanupGlobalSocket();
      }
    };
  }, []);

  return {
    isConnected,
    subscribe,
    emit,
    isServerless,
    cleanup: cleanupGlobalSocket // Expose cleanup for manual cleanup if needed
  };
};
