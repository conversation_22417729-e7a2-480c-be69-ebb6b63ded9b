import { useEffect } from 'react';

export const usePreloadRoutes = () => {
  useEffect(() => {
    // Preload critical routes
    const routes = ['/dashboard', '/scans', '/messages'];
    
    routes.forEach(route => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = route;
      document.head.appendChild(link);
    });
    
    return () => {
      // Cleanup prefetch links
      routes.forEach(route => {
        const link = document.querySelector(`link[href="${route}"]`);
        if (link) document.head.removeChild(link);
      });
    };
  }, []);
};

export const usePreloadImages = (images: string[]) => {
  useEffect(() => {
    images.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, [images]);
};