"use client"

import { useEffect, useState, useCallback } from 'react';

type VisibilityCallback = () => void | Promise<void>;

/**
 * Hook to detect when a user is leaving the page or switching tabs
 * @param onHidden - Callback to run when the page becomes hidden
 * @param onVisible - Optional callback to run when the page becomes visible again
 * @param onBeforeUnload - Optional callback to run before the page is unloaded
 */
export function usePageVisibility(
  onHidden: VisibilityCallback,
  onVisible?: VisibilityCallback,
  onBeforeUnload?: VisibilityCallback
) {
  const [isVisible, setIsVisible] = useState(true);

  // Handle visibility change
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden) {
      setIsVisible(false);
      onHidden();
    } else if (onVisible) {
      setIsVisible(true);
      onVisible();
    }
  }, [onHidden, onVisible]);

  // Handle before unload
  const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
    if (onBeforeUnload) {
      onBeforeUnload();
    }
  }, [onBeforeUnload]);

  useEffect(() => {
    // Set initial visibility state
    setIsVisible(!document.hidden);

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      // Run the onHidden callback when the component unmounts
      // This ensures messages are saved when navigating away within the app
      onHidden();
    };
  }, [handleVisibilityChange, handleBeforeUnload, onHidden]);

  return isVisible;
}
