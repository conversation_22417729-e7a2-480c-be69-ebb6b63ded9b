"use client";

import { useMemo, useCallback, useState } from 'react';
import { ScanType } from '@/types/scan-types';

interface UseOptimizedScansProps {
  scans: ScanType[];
  searchQuery: string;
}

interface OptimizedScansResult {
  filteredScans: ScanType[];
  statusCounts: {
    pending: number;
    inProgress: number;
    reTest: number;
    completed: number;
    total: number;
  };
  scanMatchesQuery: (scan: ScanType, query: string) => boolean;
}

// Cache for search results to avoid re-filtering identical queries
const searchCache = new Map<string, ScanType[]>();
const MAX_CACHE_SIZE = 50;

export function useOptimizedScans({ scans, searchQuery }: UseOptimizedScansProps): OptimizedScansResult {
  // Optimized search function with early returns and caching
  const scanMatchesQuery = useCallback((scan: ScanType, query: string): boolean => {
    if (!query) return true;
    
    const lowerQuery = query.toLowerCase();
    
    // Check most common fields first for early return
    if (scan.name?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.target?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.status?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.asset_type?.toLowerCase().includes(lowerQuery)) return true;
    
    // Check less common fields
    if (scan.title?.toLowerCase().includes(lowerQuery)) return true;
    
    // Check vulnerability counts (convert to string only if needed)
    if (scan.criticalVulnerabilities > 0 && scan.criticalVulnerabilities.toString().includes(query)) return true;
    if (scan.highVulnerabilities > 0 && scan.highVulnerabilities.toString().includes(query)) return true;
    if (scan.mediumVulnerabilities > 0 && scan.mediumVulnerabilities.toString().includes(query)) return true;
    if (scan.lowVulnerabilities > 0 && scan.lowVulnerabilities.toString().includes(query)) return true;
    
    // Check AI summary and files only if other checks fail
    if (scan.aiSummary?.summary?.toLowerCase().includes(lowerQuery)) return true;
    if (scan.files?.some((file: { originalName?: string }) =>
      file.originalName?.toLowerCase().includes(lowerQuery)
    )) return true;
    
    return false;
  }, []);

  // Memoized filtered scans with caching
  const filteredScans = useMemo(() => {
    if (!scans?.length) return [];
    if (!searchQuery.trim()) return scans;

    const query = searchQuery.toLowerCase().trim();
    const cacheKey = `${JSON.stringify(scans.map(s => s.id))}-${query}`;
    
    // Check cache first
    if (searchCache.has(cacheKey)) {
      return searchCache.get(cacheKey)!;
    }

    // Filter scans
    const filtered = scans.filter(scan => scanMatchesQuery(scan, query));
    
    // Cache the result (with size limit)
    if (searchCache.size >= MAX_CACHE_SIZE) {
      const firstKey = searchCache.keys().next().value;
      if (firstKey) {
        searchCache.delete(firstKey);
      }
    }
    searchCache.set(cacheKey, filtered);
    
    return filtered;
  }, [scans, searchQuery, scanMatchesQuery]);

  // Optimized status counts with single pass
  const statusCounts = useMemo(() => {
    const counts = {
      pending: 0,
      inProgress: 0,
      reTest: 0,
      completed: 0,
      total: filteredScans.length
    };

    filteredScans.forEach(scan => {
      switch (scan.status) {
        case "pending":
          counts.pending++;
          break;
        case "in-progress":
          counts.inProgress++;
          break;
        case "re-test":
          counts.reTest++;
          break;
        case "completed":
          counts.completed++;
          break;
      }
    });

    return counts;
  }, [filteredScans]);

  return {
    filteredScans,
    statusCounts,
    scanMatchesQuery
  };
}

// Hook for optimizing scan card rendering
export function useVirtualizedScans(scans: ScanType[], itemsPerPage: number = 10) {
  const [currentPage, setCurrentPage] = useState(0);
  
  const paginatedScans = useMemo(() => {
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return scans.slice(startIndex, endIndex);
  }, [scans, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(scans.length / itemsPerPage);
  const hasNextPage = currentPage < totalPages - 1;
  const hasPrevPage = currentPage > 0;

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage((prev: number) => prev + 1);
    }
  }, [hasNextPage]);

  const prevPage = useCallback(() => {
    if (hasPrevPage) {
      setCurrentPage((prev: number) => prev - 1);
    }
  }, [hasPrevPage]);

  const goToPage = useCallback((page: number) => {
    if (page >= 0 && page < totalPages) {
      setCurrentPage(page);
    }
  }, [totalPages]);

  return {
    paginatedScans,
    currentPage,
    totalPages,
    hasNextPage,
    hasPrevPage,
    nextPage,
    prevPage,
    goToPage
  };
}