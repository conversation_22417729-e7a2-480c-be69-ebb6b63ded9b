"use client";

import { useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface UsePrefetchScansProps {
  fetchScans: (forceRefresh?: boolean) => Promise<void>;
  fetchOrganizationScans: (forceRefresh?: boolean) => Promise<void>;
  isOrganizationView: boolean;
}

export function usePrefetchScans({ 
  fetchScans, 
  fetchOrganizationScans, 
  isOrganizationView 
}: UsePrefetchScansProps) {
  const { user, role } = useAuth();

  // Prefetch the opposite view data in the background
  const prefetchOppositeView = useCallback(async () => {
    if (!user || role !== 'client') return;

    try {
      // Use requestIdleCallback for background prefetching
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(async () => {
          if (isOrganizationView) {
            // Currently in org view, prefetch personal scans
            await fetchScans(false);
          } else {
            // Currently in personal view, prefetch org scans
            await fetchOrganizationScans(false);
          }
        }, { timeout: 5000 });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(async () => {
          if (isOrganizationView) {
            await fetchScans(false);
          } else {
            await fetchOrganizationScans(false);
          }
        }, 2000);
      }
    } catch (error) {
      // Silently fail prefetching - it's not critical
      console.debug('Prefetch failed:', error);
    }
  }, [user, role, isOrganizationView, fetchScans, fetchOrganizationScans]);

  // Prefetch opposite view after initial load
  useEffect(() => {
    if (user && role === 'client') {
      // Wait a bit after initial load before prefetching
      const timer = setTimeout(prefetchOppositeView, 3000);
      return () => clearTimeout(timer);
    }
  }, [user, role, prefetchOppositeView]);

  return { prefetchOppositeView };
}

// Hook for intelligent cache warming
export function useCacheWarming() {
  const warmCache = useCallback((data: any[], cacheKey: string) => {
    try {
      // Store in sessionStorage for faster access
      const cacheData = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      };
      sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      // Storage might be full, silently fail
      console.debug('Cache warming failed:', error);
    }
  }, []);

  const getCachedData = useCallback((cacheKey: string, maxAge: number = 300000) => {
    try {
      const cached = sessionStorage.getItem(cacheKey);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const age = Date.now() - cacheData.timestamp;
      
      if (age > maxAge) {
        sessionStorage.removeItem(cacheKey);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      return null;
    }
  }, []);

  const clearCache = useCallback((cacheKey?: string) => {
    if (cacheKey) {
      sessionStorage.removeItem(cacheKey);
    } else {
      // Clear all scan-related cache
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('scans-') || key.startsWith('org-scans-')) {
          sessionStorage.removeItem(key);
        }
      });
    }
  }, []);

  return { warmCache, getCachedData, clearCache };
}