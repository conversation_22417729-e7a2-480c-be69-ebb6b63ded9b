import { useEffect, useState } from "react";
import { User, onAuthStateChanged, getIdTokenResult } from "firebase/auth";
export type { User };
import { auth } from "../lib/firebase";
import Cookies from 'js-cookie';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<"client" | "admin" | "manager" | null>(null);
  const [loading, setLoading] = useState(true);
  const [managedTeam, setManagedTeam] = useState<string[]>([]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setUser(firebaseUser);

      if (firebaseUser) {
        try {
          // Get the ID token result which contains custom claims
          const idTokenResult = await getIdTokenResult(firebaseUser);
          const customClaims = idTokenResult.claims;

          // Get role from custom claims
          let userRole: "client" | "admin" | "manager" | null = null;

          if (customClaims.role) {
            // Use role from custom claims
            userRole = customClaims.role as "client" | "admin" | "manager";
            console.log(`User ${firebaseUser.uid} has role from custom claims: ${userRole}`);
          } else {
            // Default to client if no role is set
            userRole = "client";
            console.log(`User ${firebaseUser.uid} has no role in custom claims, defaulting to: ${userRole}`);
          }

          // Set the role in state
          setRole(userRole);

          // Set the role in a cookie for middleware to use
          if (userRole) {
            Cookies.set('user_role', userRole, {
              expires: 7, // 7 days
              path: '/',
              sameSite: 'strict',
              secure: process.env.NODE_ENV === 'production'
            });
          } else {
            Cookies.remove('user_role');
          }
        } catch (error) {
          console.error("Error getting token claims:", error);
          // Default to client role on error
          const userRole = "client";
          setRole(userRole);

          Cookies.set('user_role', userRole, {
            expires: 7, // 7 days
            path: '/',
            sameSite: 'strict',
            secure: process.env.NODE_ENV === 'production'
          });
        }
      } else {
        setRole(null);
        Cookies.remove('user_role');
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch team members for managers
  useEffect(() => {
    const fetchManagedTeam = async () => {
      if (user && role === "manager") {
        try {
          const idToken = await user.getIdToken();
          const response = await fetch('/api/teams/members', {
            headers: {
              'Authorization': `Bearer ${idToken}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            setManagedTeam(data.teamMembers || []);
          } else {
            console.error("Failed to fetch team members");
            setManagedTeam([]);
          }
        } catch (error) {
          console.error("Error fetching team members:", error);
          setManagedTeam([]);
        }
      }
    };

    if (user && role === "manager" && !loading) {
      fetchManagedTeam();
    }
  }, [user, role, loading]);

  return { user, role, loading, managedTeam };
}
