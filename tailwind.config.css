@config "./tailwind.config.ts";

@theme {
  /* Container */
  --container-center: true;
  --container-padding: 2rem;
  --container-screens-2xl: 1400px;

  /* Colors */
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  /* Status Colors */
  --color-pending: #f59e0b;
  --color-in-progress: #3b82f6;
  --color-completed: #10b981;
  --color-critical: #ef4444;
  --color-re-test: #843DF5;

  /* Shadows - Updated for v4 */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-none: 0 0 #0000;

  /* Border Radius */
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
}

@keyframes accordion-down {
  from { height: 0; }
  to { height: var(--radix-accordion-content-height); }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height); }
  to { height: 0; }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@utility animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

@utility animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

@utility animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

@utility animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

@utility animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@utility card-hover {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

@utility card-hover-active {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

@utility status-dot-pending {
  background-color: var(--color-pending);
}

@utility status-dot-in-progress {
  background-color: var(--color-in-progress);
}

@utility status-dot-completed {
  background-color: var(--color-completed);
}

@utility status-dot-critical {
  background-color: var(--color-critical);
}
