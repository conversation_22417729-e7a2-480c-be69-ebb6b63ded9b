{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "next build", "start": "NODE_ENV=production node server.js", "lint": "next lint", "use:dev": "node scripts/switch-env.js development", "use:prod": "node scripts/switch-env.js production"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.76.1", "@types/js-cookie": "^3.0.6", "@types/react-window": "^1.8.8", "all": "^0.0.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "8.6.0", "firebase": "^11.7.3", "firebase-admin": "^13.4.0", "form-data": "^4.0.2", "formidable": "^3.5.4", "input-otp": "1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "next": "^15.3.3", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.1.1", "pdf-parse": "^1.1.1", "pnpm": "^10.12.1", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-textarea-autosize": "^8.5.9", "react-window": "^1.8.11", "recharts": "^3.0.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "resend": "^4.5.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "1.1.2", "zod": "^3.25.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/formidable": "^3.4.5", "@types/node": "^24.0.1", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}}