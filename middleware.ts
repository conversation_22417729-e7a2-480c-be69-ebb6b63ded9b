import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the cookie that indicates the user's role
  const roleCookie = request.cookies.get('user_role')?.value;

  // Check if the user is authenticated
  const isAuthenticated = !!roleCookie;

  // All routes (not excluded by the matcher config below) require authentication.
  // The matcher already excludes /login, static assets, public files, etc.

  // If the user is not authenticated, redirect them to the login page.
  // This applies to all pages that are not excluded by the matcher.
  if (!isAuthenticated) {
    console.log(`Middleware: Redirecting unauthenticated user from ${request.nextUrl.pathname} to /login`);
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Handle role-based redirects for the home page
  if (request.nextUrl.pathname === '/') {
    // If the user is an admin, redirect to /scans
    if (roleCookie === 'admin') {
      return NextResponse.redirect(new URL('/scans', request.url));
    }
    // If the user is a manager, redirect to /dashboard
    else if (roleCookie === 'manager') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
    // If the user is authenticated but not admin or manager, they can stay on the home page
    // If not authenticated, they'll see the public home page
  }

  // Role-specific access restrictions
  if (isAuthenticated) {
    // Admin-only routes
    if (request.nextUrl.pathname.startsWith('/admin/') && roleCookie !== 'admin') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Manager-only routes
    if (request.nextUrl.pathname === '/request-pentest' && roleCookie !== 'manager') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    if (request.nextUrl.pathname === '/team-dashboard' && roleCookie !== 'manager') {
      return NextResponse.redirect(new URL('/', request.url));
    }
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - login page (to avoid redirect loops)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public|login|auth).*)',
  ],
};
